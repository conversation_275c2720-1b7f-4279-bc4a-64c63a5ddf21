# Changelog

Traces of your evolution, detailed below.

## [0.0.17-9] - 2025-08-29

### 🚀 Features

- *(es)* 重构同步失败记录逻辑，使用统一服务方法
- *(ImageUtils)* 增强PNG转JPG功能，支持自定义背景色和压缩质量
- *(es)* :sparkles: 实现基于Binlog的ES同步系统 添加完整的ES同步解决方案，包括Binlog事件监听、同步记录管理和定时任务处理。主要功能： - 新增BinlogEventListener处理RocketMQ消息，支持多种业务表变更监听 - 实现EsSyncRecordCollector收集需要同步的记录 - 添加EsSynchronizer负责实际的数据同步到ES索引 - 创建EsSyncJobController定时任务处理同步队列 - 支持sale_goods单表索引和聚合索引同步 - 添加失败日志记录和重试机制 新增支持的表变更处理器：Product、ProductTag、SaleGoods、SaleSkc、SaleSku等表的变更监听
- *(es)* :sparkles: 实现Lazada商品ES分页查询功能 新增LazadaEsQueryService服务类，支持单站点和聚合查询两种模式。重构ES文档结构，优化字段映射和查询条件。在ProductLazadaPageResp中新增聚合字段并添加@JsonIgnore注解。扩展ES同步功能，支持批量操作和失败重试机制。 - 新增LazadaEsQueryService处理ES分页查询 - 重构SaleGoodsBaseDocument字段映射，支持聚合查询 - 在响应对象中添加聚合字段用于ES存储 - 优化EsSyncJobController，支持批量同步和重试机制 - 扩展SQL查询，添加skc_list字段支持
- *(es)* 实现多语言商品标题搜索功能 为Lazada商品搜索添加多语言支持，使用多字段映射策略为不同国家站点配置专属分析器。商品标题字段现在支持PH、TH、VN、SG、MY、ID等地区的语言分析，包括泰语、印尼语等特定语言处理。 新增Analyzers常量类统一管理分析器名称，更新ES文档映射和查询服务，优化搜索结果排序策略。 测试用例增加泰语搜索验证，确保多语言搜索功能正常工作。
- *(es)* 添加批量更新库存后的ES同步功能 在ProductController的batchStockQuantity方法中添加ES同步逻辑，当批量更新商品库存后，自动同步相关sale_goods数据到Elasticsearch。同时优化EsSynchronizer类，增加空集合检查和文档注释。 - 在库存批量更新操作后查询相关sale_goods记录 - 调用esSynchronizer同步数据到ES索引 - 为EsSynchronizer添加空集合检查避免mybatis-plus异常 - 补充方法文档注释和类注释

### 🐛 Bug Fixes

- *(es)* 优化Lazada同步失败处理和Redis队列性能 修复EsSyncFailLogRepository的saveOrUpdate方法，使用UPSERT模式 优化LazadaSaleSyncService同步逻辑，增加空数据检查和日志记录 重构LazadaSyncAggregator消息处理，添加Redis超时处理和批量移除优化
- *(product)* 修复Lazada聚合查询分页结果返回空列表问题 在ProductLazadaServiceImpl中修正分页返回逻辑，当查询结果不为空时正确返回records数据而非空列表。同时移除LazadaSaleAggEsRepository中冗余的ES连通性测试代码。
- *(controller)* 修正导入商品接口参数可为空类型 将shopIdList参数类型从String改为String?以匹配服务层方法签名，确保空值传递正确处理。
- *(es-sync)* 修复sale_goods聚合索引同步顺序问题 修复EsSyncJobController中sale_goods聚合索引收集与同步的顺序问题。原先在删除记录后才收集受影响的数据对，可能导致数据不一致。 现在改为在删除记录前先收集所有受影响的sale_goods聚合索引对，确保同步时能正确处理所有相关数据。 同时修复EsSynchronizer中删除聚合文档时使用错误mapper的问题，将saleGoodsEsMapper更正为saleGoodsAggregationEsMapper。
- 在单表索引同步前，需要提前收集所有受影响的 sale_goods 聚合索引

### 💼 Other

- *(rocketmq)* 升级RocketMQ客户端版本并重构消息监听器 将RocketMQ Spring Boot Starter从2.3.0升级到2.3.4版本，并切换到新的rocketmq-v5-client-spring-boot-starter依赖。重构了BinlogEventListener以适配新的RocketMQ v5客户端API，使用MessageView和ConsumeResult替代原有的MessageExt。同时注释掉了基于旧版客户端的DtsMessageListener，并添加了ByteBuffer工具类用于字节数组转换。 新增了异常处理逻辑，在EsSyncJobController中正确处理CompletionException，提取实际异常原因并记录更准确的错误日志。

### 🚜 Refactor

- *(product)* 重构Lazada商品分页查询逻辑 移除原有的ES查询策略实现，改为简单的数据库查询和ES查询占位符。 当ES功能关闭时直接查询数据库并返回结果，ES功能开启时使用TODO占位符等待后续实现。 此重构为后续实现新的ES查询策略做准备，简化了当前代码结构。
- *(es-sync)* 调整ES同步任务的默认配置参数 将ES同步任务的默认窗口大小从2秒改为1秒，默认批处理大小从50条改为20条。 这些调整旨在优化同步性能，减少单次处理的数据量，提高同步任务的响应速度。 同时为Binlog事件类型添加DDL枚举值，以支持数据库结构变更事件的同步处理。
- *(es)* 重构ES同步架构，移除旧版Easy-ES实现 移除基于Easy-ES的旧版条码和Lazada销售数据同步实现，包括： - 删除Barcode相关的ES实体、Mapper、Repository和同步服务 - 删除Lazada销售数据相关的ES实体、聚合索引和同步逻辑 - 移除DTS消息监听器和RocketMQ处理器 - 移除Easy-ES配置和初始化服务 迁移到新的ES同步架构： - 保留并重构SaleGoods相关的ES文档和Mapper - 优化ES同步控制器和定时任务 - 添加ES同步刷新策略支持 - 重构Binlog事件监听器 此次重构旨在简化ES同步逻辑，提高系统稳定性和可维护性，为后续ES集群升级做准备。
- *(es)* 重构ES工具类并优化查询服务 删除原有的EsMappers.kt和Pages.kt文件，将相关功能整合到新的EasyEsExtensions.kt扩展文件中 新增日期范围查询扩展方法betweenDate，优化LazadaEsQueryService中的日期查询逻辑 在查询服务中添加按得分排序功能，提升搜索结果相关性 测试类EsDemo.kt添加createdTime字段用于测试日期查询功能 此次重构将分散的ES工具函数集中管理，提高了代码的可维护性和复用性

### 📚 Documentation

- *(page-es)* :fire: remove outdated ES sync design documentation and SQL 删除与Lazada ES同步方案相关的过时设计文档和SQL文件。这些文件包含早期的技术设计方案和数据库表结构定义，现因架构调整或方案更新而不再适用。 移除的文件包括： 1. ES同步失败记录表的SQL建表脚本 2. Lazada平台ES同步技术设计方案详细文档 这些文档已不再反映当前系统的实现方式，保留可能会造成团队成员的困惑。

### ⚡ Performance

- *(es)* 优化ES同步配置和索引设置 将ES同步作业的批处理大小从20增加到50，提升数据同步效率 调整SaleGoods和SaleGoodsAggregation索引的分片和副本配置，从3分片2副本改为1分片1副本，降低集群资源消耗

## [0.0.17-7] - 2025-08-21

### 🚀 Features

- *(rocketmq)* 支持DTS消息批量处理 在DtsMessage中新增批量获取主键和数据的方法，并重构所有处理器支持批量操作： - 新增getAllPrimaryKeyValues()和getAllData()方法 - ProductTableProcessor支持批量处理product变更 - BarcodeTableProcessor支持批量ES同步 - ProductTagTableProcessor支持批量标签处理 - LazadaSaleTableProcessor支持批量提取sale_goods_id 提升处理效率，减少数据库查询次数。
- 移除DtsMessage中未使用的getFirstOldData方法
- *(es-sync)* 同步成功后自动清理失败记录 在LazadaSaleSyncService中添加同步成功后自动删除EsSyncFailLog记录的功能，优化EsSyncFailLogRepository的物理删除方法使用deleteByIds提升性能。
- *(es-sync)* 支持逻辑删除记录的ES同步处理 在Lazada销售商品和条码的ES同步服务中增加逻辑删除处理能力： - 同步时检查记录删除状态，自动从ES删除已逻辑删除的记录 - 优化全量同步查询，忽略逻辑删除过滤以包含所有记录 - 简化RocketMQ处理器，直接从消息获取关联ID避免数据库查询 - 添加异步执行和批量删除成功记录功能提升性能
- *(es)* 重构条码同步服务，支持事件类型区分同步来源 在BarcodeSyncService中： - 为processBarcodesBatch方法添加eventType参数 - 为convertToEsEntity方法添加eventType参数 - 同步方法支持传入事件类型标识来源 重构BarcodeTableProcessor： - 移除直接ES操作逻辑，统一调用BarcodeSyncService - 简化处理器逻辑，提取主键后调用统一同步服务 - 支持批量处理upsert和delete操作
- *(es)* 增强ES同步服务对删除记录的处理能力 在BarcodeSyncService和LazadaSaleSyncService中： - 扩展逻辑删除检查功能，同时处理物理删除记录 - 新增deleteByPrimaryKeys方法处理DTS DELETE事件 - 统一日志和注释描述 在EsSyncController中： - 新增物理删除条码ES数据的API端点 在DtsConstants中： - 新增字段名常量统一管理 在LazadaSaleTableProcessor中： - 使用常量替代硬编码字段名，提高可维护性
- *(ae-price)* 新增AE价格重算工具，支持Excel导入商品调用生产API重新算价并生成SQL更新语句
- *(lazada)* 实现Lazada商品分页查询ES优化方案 新增LazadaConvert工具类处理ES查询条件转换和实体映射，重构ProductLazadaServiceImpl支持ES优先查询降级数据库兜底，优化LazadaSaleEsRepository原生分页查询性能
- *(es)* 新增Lazada销售数据聚合索引支持多站点聚合查询 新增LazadaSaleAggEsEntity聚合索引实体及相关Mapper、Repository组件，实现按productId+shopId聚合多站点数据。ProductLazadaServiceImpl优先查询聚合索引，降级到详情索引和数据库查询。LazadaSaleSyncService同步时自动更新聚合索引，支持全量构建和增量更新。新增LazadaAggConvert转换工具类处理聚合数据转换。
- *(es)* 优化Lazada聚合索引重建逻辑，解决数据一致性问题 重构聚合索引重建机制，采用数据库为主数据源，新增V2版本使用现有page方法获取完整业务数据。添加前台链接JSON存储字段，移除全量构建测试方法，优化批量查询性能避免N+1问题。
- *(es)* 优化Lazada商品ES同步逻辑与数据转换 重构Lazada商品ES同步服务，主要变更： 1. 将ES实体转换逻辑集中到LazadaConvert工具类 2. 优化删除逻辑，先保存productId+shopId组合再删除详情索引 3. 新增前台链接JSON解析工具方法 4. 修复聚合索引更新时已删除记录的处理问题 5. 使用常量替代硬编码配置值
- *(es)* 移除LazadaSaleSyncService中的全量聚合索引构建功能
- *(es-sync)* 实现部分同步异常处理和重试机制 新增PartialSyncException异常类，用于处理批量同步中部分成功部分失败的情况。重构LazadaSaleSyncService的syncToEs方法，改为逐条处理并记录失败信息。在LazadaSyncAggregator中增加对部分同步异常的处理逻辑，只对失败记录进行重试。同时添加队列状态监控和清空接口。
- *(es)* 在Lazada销售同步中增加聚合信息支持 新增ProductAggregatedInfoBO聚合信息对象 在LazadaSaleSyncService中查询并应用聚合信息 ProductLazadaService增加getAggregatedInfo接口 实现聚合状态计算逻辑（发布状态、同步状态等）
- *(lazada)* 优化Lazada商品分页查询的ES策略 重构ProductLazadaServiceImpl的分页查询逻辑： - 单站点查询使用详情索引，多站点查询使用聚合索引 - 简化查询流程，移除冗余的pageBySingleCountry方法 - 统一异步同步方法命名，优化错误处理 - 更新测试用例验证新策略
- *(es)* 添加ES功能开关配置 新增EsProperties配置类，支持全局和分模块（Lazada商品和条码）的ES查询开关。在ProductLazadaServiceImpl和BarCodeServiceImpl中集成开关检查逻辑，当ES关闭时直接使用数据库查询，提升系统灵活性。
- *(rocketmq)* 更新商品CDC监听器配置及TopicHolder命名
- *(es)* 添加Lazada同步任务配置化和并发处理优化 在EsProperties中添加同步任务配置项，支持开关控制、批处理大小和锁时间配置。优化LazadaSyncAggregator使用线程池并发处理，增加队列监控和超时控制。新增SchedulingConfig启用定时任务调度。
- *(product)* 新增按店铺更新商品毛利率功能 新增UpdateProductGrossMarginByShopReq请求类 在ProductManageService接口和实现类中新增updateProductGrossMarginByShop方法 添加对应的Controller接口和单元测试 更新版本号至20250820

### 🐛 Bug Fixes

- *(SaleGoodsMapper)* 移除platform_product_id非空条件并简化排序语法
- *(es)* 优化Lazada聚合索引构建逻辑，改为同步执行并基于详情索引数据 - 将聚合索引更新从异步改为同步执行，确保数据一致性 - 重构rebuildAggregatedRecord方法，完全基于详情索引数据构建聚合记录 - 简化实现逻辑，移除冗余的数据库查询和复杂兜底方案 - 修复platformProductId字段类型从KEYWORD改为LONG - 增加全量构建聚合索引的方法buildFullAggregatedIndex

### 🚜 Refactor

- *(dts)* 统一DTS处理器表名常量管理 新增DtsConstants集中管理表名字符串常量，替换各处理器中的硬编码表名，包括product、product_barcode、product_tag及lazada相关表。优化LazadaSyncAggregator异常日志记录。
- *(es)* 重构ES索引初始化服务位置和Redis常量 将BarcodeEsInitService从impl子目录移至es主目录，并更新RedisConstants中的锁键名为更通用的ES_INIT_LOCK

## [0.0.17-6] - 2025-08-19

### 🚀 Features

- *(deps)* 添加 rocketmq-spring 和 easy-es 依赖
- *(es)* 新增条码数据同步至ES功能 - 新增BarcodeCdcListener监听RocketMQ消息 - 实现BarcodeDataSyncService全量同步服务 - 添加BarcodeEsService及相关实现类 - 配置EasyEs和RocketMQ - 在BarcodeController添加ES同步接口
- *(es)* 标准化条码ES实体时间字段格式
- *(rocketmq)* 重构条码CDC监听为通用DTS消息处理框架 - 删除旧的条码CDC相关类(BarcodeCdcListener/BarcodeCdcMessage/BarcodeEsService) - 新增通用DTS消息处理框架(DtsMessage/DtsMessageListener/DtsMessageProcessor) - 实现条码表处理器(BarcodeTableProcessor)和Lazada表处理器 - 新增消息路由机制(DtsMessageRouter)支持多表处理
- *(es)* 新增条码数据同步功能及接口 新增BarcodeDataSyncReq和BarcodeDataSyncResp类，支持全量/批量/时间范围同步 重构BarcodeDataSyncService，提供统一同步方法 在BarcodeController添加同步接口
- *(barCode)* 添加ES查询支持与数据同步功能
- *(条码服务)* 重构ES查询逻辑并新增转换工具类 - 将BarCodeServiceImpl中的ES查询逻辑提取到BarcodeEsRepository - 新增BarcodeConvert用于构建ES查询条件 - 新增BarcodeEntityConvert用于实体转换 - 新增BarcodeEsRepository封装ES数据访问操作
- *(es)* 优化条码ES查询逻辑并删除冗余转换类 - 重构BarcodeEsRepository，使用Easy-ES原生分页API - 合并BarcodeEntityConvert功能到BarcodeConvert - 在ProductBarcodePageResp中增加来源标识 - 优化BarCodeServiceImpl的分页查询逻辑
- *(es)* 新增ES性能测试数据生成功能 在BarcodeDataSyncService中新增generateTestData方法，用于生成随机测试数据并批量插入ES。新增BarcodeTestDataGenerateReq和BarcodeTestDataGenerateResp类处理请求和响应。在BarcodeController中添加对应接口。
- *(es)* LazadaSale 监听dts同步 es
- *(es)* 重构ES同步服务并新增进度响应类 1. 将SyncProgress重构为独立响应类SyncProgressResp 2. 同步接口改为异步执行并优化日志记录 3. 新增Lazada销售商品全量同步断点续传功能 4. 统一使用标准时间格式化工具类
- *(es)* 更新LazadaSaleEsEntity索引名称格式 test(processor): 新增LazadaSaleTableProcessor测试类 test(aggregator): 新增LazadaSyncAggregator测试类
- *(product)* 添加 AE 商品区域定价功能 - 在导入 AE 商品时，增加区域定价相关逻辑 - 新增区域定价标识字段，并在导入时进行处理 - 优化物流成本、仓库成本等字段的处理逻辑 - 调整代码格式，提高可读性
- *(product)* 添加区域定价标识导入 - 在产品导入 Excel 监听器中增加"是否区域定价"字段的处理 - 将"是否区域定价"的值赋给 productDTO.regionalPricingFlag

### 🐛 Bug Fixes

- *(rocketmq)* 优化条码表DTS消息处理器逻辑 改用主键查询数据库后同步ES，解决字段命名不匹配问题
- *(test)* 更新测试配置使用env和app变量 统一修改测试类中的Spring启动配置，将原有的spring.profiles.active和spring.config.import替换为使用env和app变量的新格式。同时更新AliexpressServiceTest中的测试店铺ID。
- *(docs)* 修改ES同步失败日志表索引命名和字段注释
- *(product)* 优化商品导入毛利率配置逻辑 修复商品导入时毛利率配置逻辑，增加对指定店铺的判断条件，避免错误覆盖店铺独立配置。同时调整毛利率缓存清理时机至导入前，确保数据一致性。
- *(product)* 修复导入毛利率时店铺配置处理顺序错误 调整毛利率配置代码块位置，确保标签添加操作在配置更新前执行

### 🚜 Refactor

- *(config)* 修正redis配置项缩进并更新变量名
- *(ProductLazadaServiceImpl)* 移除冗余注释
- *(pop-product-service)* 优化 AeProductManageComponent 中的 SKU 数据处理逻辑 - 将空列表检查逻辑提前，减少不必要的操作- 使用 if (skuIdAeList.isNotEmpty()) 包裹整个逻辑，提高代码可读性 - 移除多余的空行和缩进，使代码结构更紧凑
- *(pop-product-service)* 修改产品导出区域定价逻辑 -将区域定价标志逻辑修改为始终设置为 "是"，以符合新的业务需求 - 移除了原有的判断逻辑，简化了代码结构

### 🎨 Styling

- *(gradle)* 增加空格

## [0.0.17-5] - 2025-08-15

### 🚀 Features

- *(regionpricerule)* 新增商品售价划线价计算接口及缓存优化 1. 新增CalAeRetailPriceForPageResp响应类 2. 实现calAeSaleAndRetailPriceForPage接口用于异步计算商品售价和划线价 3. 优化RegionPriceRuleServiceImpl缓存机制，增加随机TTL和多种规则缓存 4. 新增invalidateAllCache方法统一清理所有缓存 5. 在RegionPriceRuleController中新增计算接口
- *(product)* 修改商品毛利率接口返回价格信息 新增UpdateProductGrossMarginResp返回售价和划线价信息 修改ProductManageServiceImpl返回价格数据 调整Controller和Service接口返回类型

### 🐛 Bug Fixes

- *(regionpricerule)* 添加折扣率配置缓存功能
- *(service)* 添加表达式解析缓存及清除功能 在ProductPriceRuleV2Controller中注入ExpressionParserService并添加缓存清除调用，ExpressionParserService实现表达式解析结果缓存机制，包含缓存大小限制和过期时间配置
- *(pricing)* 优化定价组件逻辑与错误处理 1. 添加AE平台默认目标国家常量 2. 重构变量预计算逻辑，增加空值检查 3. 将日志级别从info调整为debug 4. 拆分大函数为多个小函数，提高可读性 5. 增强错误处理，添加详细日志 6. 优化BigDecimal比较方法
- *(regionpricerule)* 修复区域定价缓存清除遗漏问题 添加productSaleAndRetailPriceCache缓存清除逻辑
- *(product)* 更新毛利率前清除缓存 在ProductManageServiceImpl中，更新毛利率配置前增加缓存清除操作，确保数据一致性
- *(regionpricerule)* 处理空产品列表时的价格计算 当传入空产品列表时直接返回空结果，避免不必要的查询
- *(product)* 修复AE商品导入后空值SKU的价格计算问题
- *(export)* 根据店铺ID筛选SKU数据
- *(pricing)* 移除店铺循环直接计算跨境商品成本价
- *(pricing)* 修正Ae模板定价服务中的成本计算参数 将硬编码的isCbType参数改为使用isLoginNum方法判断
- *(export)* 修复选款店铺为空时默认店铺逻辑

## [0.0.17-3] - 2025-08-14

### 🚀 Features

- *(pricing)* 新增清除定价变量缓存功能 在RegionPriceRuleController添加清除缓存接口，PricingVariableService增加invalidateAllCache方法实现
- *(product)* 优化AE商品导入发货地与价格校验逻辑
- *(product)* 优化商品主图获取逻辑并更新版本号 • 在ProductManageServiceImpl中添加从图库获取主图的逻辑，优先使用图库图片 • 在ImageRepositoryRepository中新增通过SPU编码列表查询图片的方法 • 更新versions.properties中的版本号为20250814 本次修改主要优化了商品主图显示逻辑，优先从图库获取图片，提升用户体验。同时更新了服务版本号以部署新功能。
- *(product-service)* 优化产品详情图片获取逻辑 • 从imageRepository获取产品图片作为主图 • 当图片不存在时回退到product.mainImgUrl • 提升产品详情页的图片展示可靠性

### 🐛 Bug Fixes

- *(pricing)* 毛利率根据店铺查询时带上供给方式判断、移除百分比值处理的注释并更
- *(image-repository)* 修复根据spuCodes查询图片仓库列表的方法 • 将eq方法改为in方法以支持多spuCode查询 • 修复当传入多个spuCode时无法正确查询的问题 • 保持原有返回类型不变 该修改解决了当需要查询多个spuCode对应的图片时，原方法无法正常工作的问题。
- *(export)* 移除AeProductExportComponent中多余的店铺ID过滤逻辑

### ⚙️ Miscellaneous Tasks

- *(dto)* 移动ProductPriceFormulaV2Dto到service模块

## [0.0.17-2] - 2025-08-12

### 🚀 Features

- *(regionpricerule)* 新增发货地字段并优化划线价计算逻辑 在CommonProperties中新增发货地相关字段，重构RegionPriceRuleServiceImpl中的划线价计算方法，增加getAeRetailPriceV3版本
- *(产品标签)* 添加毛利率标签支持并调整定价服务 • 在ProductTagEnum中添加TagPopGrossMarginEnum处理逻辑 • 修改AeTemplatePricingService中价格字段从salePrice改为retailPrice • 确保国家报价配置使用正确的零售价格字段 涉及产品标签枚举和AE模板定价服务的关键业务逻辑调整
- *(price)* 优化AE价格查找器 - 在AePricingCalResp中增加版本字段和价格查找器创建方法 - 重构AeSalePricingServiceImpl的定价计算流程 - 优化AePriceLookup结构并增加版本判断 - 更新相关服务类使用新的价格查找器创建方式
- *(price)* 重构AE定价计算逻辑并优化价格查找器 - 在AePricingCalResp中增加版本字段和价格查找器创建方法 - 重构AeSalePricingServiceImpl的定价计算流程 - 优化AePriceLookup结构并增加版本判断 - 更新相关服务类使用新的价格查找器创建方式
- *(pricing)* 新增冗长计算详情开关及组件级BO 在V2定价计算中新增enableVerboseCalculationDetails开关，控制是否返回表达式和变量映射等详细计算信息。同时引入AePricingV2ComponentCalcBO作为组件级入参封装，简化接口调用。
- *(pricing)* 新增覆盖区域定价配置选项 在AePricingCalculateReq中新增overrideExistingRegionalPricing字段，控制保存定价结果时是否覆盖原有区域定价配置。优化AeTemplatePricingService保存逻辑，根据该参数决定是否保留原有区域价格。同时完善ProductPublishAeHelper的价格应用逻辑。
- *(config)* 新增市场默认发货地和目的地配置 在CommonProperties中增加MarketDefaultShippingAndReceivingPlaceConfig类，支持多发货地和目的地配置
- *(regionpricerule)* 修改店铺ID为JSON数组存储 主要变更： - 将各价格规则实体中的shopId字段改为shopIds，支持存储多个店铺ID - 更新相关DTO、Service和测试代码 - 修改数据库表结构，将shop_id字段改为shop_ids JSON类型 - 调整价格规则匹配逻辑，支持多店铺ID匹配
- *(ProductPublishAeHelper)* 新增覆盖目的地国家列表参数并优化区域定价逻辑
- *(pricing)* 优化定价错误处理逻辑与结果聚合
- *(pricing)* 新增统一 AE 模板SKU定价逻辑并重构价格更新流程 1. 在ProductPublishAeHelper中新增applyUnifiedPricingToTemplateSku方法实现统一价格计算逻辑 2. 重构AeTemplateUpdateServiceImpl使用新定价方法 3. 优化AeTemplatePricingService.updateSkuPricesBatch使用统一价格查找器 5. 调整versions.properties版本号
- *(product)* 添加商品毛利率更新事务支持及划线价计算逻辑 在ProductManageServiceImpl中添加@Transactional注解确保毛利率更新操作的事务性，并重构划线价计算逻辑，改为直接调用aeTemplatePricingService.calculateAndSave方法。同时在AeTemplatePricingService中添加事务支持，RegionPriceRuleService新增prepareCalculateReq方法用于准备定价计算请求参数。
- *(product)* 在价格计算请求中添加覆盖目标国家字段
- *(product)* 在商品毛利率修改后添加划线价计算日志
- *(product)* 优化毛利率标签添加逻辑 增加毛利率变更判断条件，仅在毛利率为null或发生变化时添加修改标签
- *(export)* 在AeProductExportComponent中添加国家报价配置处理
- *(pricing)* 重命名resolveProductGrossMargin为getProductSpecificMargi,简化resolveGrossMarginFromCache方法，移除override参数
- *(product)* 更新 SKU 模板数据 - 添加 shopId 字段 - 移除 sellerSku 相关代码
- *(product)* 新增店铺毛利率配置填充方法 将毛利率配置逻辑提取为独立方法fillProductShopGrossMarginConfigDto，并在商品导入和更新时复用
- *(product)* 支持导入商品时指定店铺ID 修改了商品导入功能，新增shopIdList参数用于指定店铺ID，并在ImportBaseProductDTO中添加shopIds字段存储店铺ID列表
- *(pricing)* 修改定价变量类型为可空以区分未配置和配置为0 - 修改 PricingVariableService 接口的11个方法返回类型为 BigDecimal? - 修改 PricingVariablesDto 中12个字段为可空类型，移除默认值0 - 修改 PricingCalculatorV2 支持可空变量映射，null值校验逻辑调整为允许0值但拒绝null值 - 修改 PricingVariableServiceImpl 返回null表示无配置，而非兜底值0 变更后语义：null = 未配置（不允许计算），BigDecimal.ZERO = 已配置为0（允许计算）
- *(pricing)* 修改定价变量类型为可空以区分未配置和配置为0 - 汇率查询方法 getExchangeRateV2 返回类型改为 BigDecimal?，避免未配置时返回0造成的歧义 - ASP配置查询方法 getAspConfigV2 同样返回可空类型，缺少平台ID或店铺运营模式时返回null - 修改汇率有效性检查逻辑，区分"未配置"(null)和"配置为0"两种不同状态
- *(product)* 优化商品毛利率计算逻辑及版本更新 - 在ProductManageServiceImpl中增加对指定店铺毛利率的处理 - 修改fillProductShopGrossMarginConfigDto方法支持传入店铺ID集合 - 更新versions.properties中的版本号至20250811
- *(pricing)* 修改成本价校验提示信息为"定价成本必须大于0"
- *(product)* 添加产品企划审核状态字段 - 在 ProductManageDetailResp 类中添加 planAuditState 字段 - 用于表示企划审核状态（0待审核、1通过、2不通过） - 在 ProductManageServiceImpl 中设置该字段的值
- *(product)* 新增商品ID和saleGoodsId字段到DTO fix(AeSaleGoodsMapper): 添加product_id和sale_goods_id查询字段 feat(export): 增加最新Max成本价和发货地字段导出 新增商品链接字段并优化成本价计算逻辑
- *(product)* 优化AE发货地显示逻辑 在ProductPublishAeExportTaskStrategy中新增发货地字典查询功能，通过字典转换发货地ID为中文名称。AbstractProductPublishPlatformExportTaskStrategy新增字典查询工具方法。
- *(定价计算)* 移除CNY汇率字段并优化变量处理 - 从PricingVariablesDto移除cnyToUsdExchangeRate字段 - 在PricingCalculatorV2中统一处理变量转换 - 简化PricingVariableServiceImpl的汇率相关逻辑
- *(ProductPublishAeExportTaskStrategy)* 新增定价明细导出功能并优化通用模板 - 新增AePricingDetailExcelDTO用于定价明细数据导出 - 在通用模板导出中增加定价明细sheet - 优化定价变量DTO字段注释和单位 - 在通用模板DTO中增加saleGoodsId字段
- *(product)* 优化AE发货地处理逻辑并更新版本号 - 重构ProductPublishAeExportTaskStrategy中的发货地处理逻辑 - 移除AbstractProductPublishPlatformExportTaskStrategy中废弃的发货地字典方法 - 更新versions.properties中的服务版本号至20250812
- *(product)* 新增款式类型字段并优化成本价计算逻辑 在PublishProductGeneralTemplateDTO中新增styleType字段，AeSaleGoodsMapper.xml添加对应查询字段，ProductPublishAeExportTaskStrategy中根据款式类型区分计算成本价逻辑
- *(pricing)* 优化Excel导出逻辑和汇率获取方式 - 优化ProductPublishAeExportTaskStrategy中的Excel写入逻辑 - 修正PricingCalculatorV2中汇率获取参数
- *(regionpricerule)* 将毛利率规则业务类型字段替换为供给方式 修改毛利率规则相关逻辑，使用supplyType替代原businessType字段，涉及实体类、接口及SQL表结构变更
- *(PriceAlertTypeEnum)* 更新OTHER类型的注释说明
- *(export)* 优化AE查询数据导出功能，增加定价明细
- *(regionpricerule)* 新增售价计算功能并完善划线价逻辑
- *(export)* 优化AeQueryDataExportComponent查询逻辑及SQL字段 修改AeSaleGoodsMapper.xml添加product_id等查询字段，重构AeQueryDataExportComponent组件，简化dictDataCache参数传递
- *(产品管理)* 添加售价信息到产品管理响应 修改ProductRegionPriceRuleDto字段名salePriceDto为salePrice 在ProductManagePageResp中新增salePrice字段 更新RegionPriceRuleServiceImpl和ProductManageServiceImpl相关逻辑

### 🐛 Bug Fixes

- *(pricing)* 移除价格计算中的冗余错误日志
- *(price)* 统一使用V2价格计算规则 - 移除ProductTemplateAeSpu中的priceCalculateRule字段 - 在CallAeComponent和AeSalePricingServiceImpl中强制使用V2规则 - 更新相关SQL表结构
- *(pricing)* 统一百分比字段注释说明 refactor(sale): 优化统一定价结果判断逻辑 refactor(product): 重构跨店铺复制商品的价格计算流程
- *(config)* 移除CommonProperties中冗余的店铺和发货地名称字段
- *(product)* 移除AE模板SKU价格的自动计算 在ProductManageServiceImpl中删除AE模板价格自动计算逻辑
- *(pricing)* 修复售价和划线价为0时的处理逻辑 修改AeSalePricingServiceImpl和AeUnifiedPricingResultDto，当售价或划线价为0时设为null
- *(pricing)* 优化商品毛利率计算逻辑 refactor(region-price-rule): 移除未使用的仓库依赖并优化代码 fix(ae-sale): 修复空SKC列表时的处理逻辑
- *(pricing)* 移除getTargetMargin中的冗余fallback逻辑
- *(pricing)* 优化商品毛利率使用判断逻辑
- *(ae-product)* 修复美元报价转换问题 • 当货币为美元时，将CNY价格转换为USD价格 • 使用汇率计算并保留两位小数 • 更新国家报价配置的数据处理逻辑 涉及汇率计算和价格转换，确保国际报价准确性
- *(export)* 优化AE商品导出逻辑中的价格处理 移除getRetailPrice方法，简化无发货地时的价格处理逻辑，优先使用模板SKU中的价格
- *(product)* 修复 USD货币转换逻辑 - 移除了不必要的循环遍历 -优化了美元货币转换的逻辑
- *(product)* 修复 SKU 匹配逻辑 - 优化了 SKU 匹配逻辑，增加了对发货地为中国时的特殊处理 - 解决了由于平台发货地设置为 null 导致的 SKU匹配问题 - 提高了产品更新时的准确性和可靠性
- *(pricing)* 优化价格计算逻辑，修复零值处理问题 scope: AeSalePricingServiceImpl, AePricingResultDto, AeUnifiedPricingResultDto 主体变更： 1. 修改价格零值判断逻辑，从>BD_ZERO改为!=BD_ZERO 2. 优化AePriceLookup性能，预计算发货地价格映射 3. 更新区域定价计算，处理可空价格字段 4. 重构ProductPublishAeHelper，优化目的地国家获取逻辑 辅助变更： 1. 重命名AePricingCalculateReq参数 2. 优化AeTemplatePricingService保存逻辑 3. 修复RegionPriceRuleServiceImpl中的价格极值计算
- *(controller)* 修正AE V2价格计算接口路径格式
- *(product)* 修复 SKU 同步时物流属性为空的问题 - 在 AeProductSkuSyncComponent 中添加了对 shipsFromAttributeValueId为空的处理 - 当 shipsFromAttributeValueId 为空时，使用 AliexpressProperties 中的默认值
- *(ProductPublishAeHelper)* 优化价格更新逻辑，仅当原值非空时更新
- *(product)* 修复组合商品skc为空时的定价逻辑 调整applyUnifiedPricingToSku调用逻辑，增加coverDestinationCountries参数，优化代码格式
- *(price/sale)* 修复V1结果转换中店铺信息和价格处理问题
- *(pricing)* 重构商品毛利率计算逻辑，支持多店铺配置
- *(product)* 修正毛利率更新逻辑条件判断 修改ProductManageServiceImpl中毛利率更新逻辑，仅当划线价存在时不更新目标毛利率
- *(pricing)* 简化商品毛利率解析逻辑
- *(pricing)* 移除毛利率配置中的冗余注释
- *(export)* 修正AE商品导出中SKC维度的发货地价格处理逻辑
- *(product)* 修复毛利率标签逻辑错误 修改毛利率计算逻辑，优化标签处理流程。修复负毛利率标签判断错误问题，增加毛利不达标标签的配置判断。
- *(ProductManageController)* 修正导入Excel方法命名错误
- *(product)* 调整AE导入店铺ID处理逻辑位置 将店铺ID处理逻辑移至数据过滤后，提高代码可读性
- *(product)* 修复店铺ID为空时的默认值处理逻辑
- *(regionpricerule)* 修复defaultRule空指针异常 修改RegionPriceRuleManageServiceImpl中defaultRule判空逻辑，避免空指针异常
- *(regionpricerule)* 修复物流支出配置默认值判断逻辑 修正RegionPriceRuleManageServiceImpl中物流支出配置的默认值判断逻辑，优化错误提示信息
- *(product)* 为商品导入方法添加事务注解 为Lazada、AE、TEMU商品导入方法添加@Transactional注解确保事务性
- *(product)* 优化多发货地导入时的旧SKU禁用逻辑 修改发货地变更判断逻辑，当导入的发货地列表和价格列表均为空时，不触发旧SKU禁用
- *(product)* 导出上架商品 AE平台导出 defaultFlag字段命名
- *(product)* 修复毛利率标签更新条件逻辑 调整产品毛利率标签的更新判断条件，增加区域定价配置的校验
- *(pricing)* 修复产品特定利润率返回值处理问题
- *(product)* 修复毛利率应用逻辑问题 当未指定店铺时，毛利率仅应用到选款店铺或市场默认店铺
- *(regionpricerule)* 修复欧美中东市场价格计算逻辑 根据市场代码设置发货地和目的地，欧美市场发货地中国目的地美国，中东市场发货地中国目的地沙特
- *(regionpricerule)* 修正划线价计算方法命名 将getAeRetailPriceV3重命名为getAeRetailPriceV3ForPage以明确方法用途
- *(regionpricerule)* 移除硬编码的市场区域价格计算逻辑
- *(pricing)* 将日志级别从info调整为debug
- *(pricing)* 移除getTargetMargin方法中的businessType参数
- *(regionpricerule)* 中东欧美外市场不计算零售价 当市场为空或非中东(zd)/欧美(om)时，直接返回空零售价对象
- *(product)* 修复毛利率调整逻辑的条件判断
- *(product)* 修复毛利率比较使用compareTo替代!= fix(region-price-rule): 添加店铺存在性校验及空值检查
- *(regionpricerule)* 修正店铺不存在错误提示信息
- *(export)* 为AE产品导出添加产品URL字段

### 🚜 Refactor

- *(pop-product-service)* 修改国家目的地字段名并更新相关引用- 将 AePlatformNationalQuoteConfigDto 中的 shipToCountry 字段名改为 shiptoCountry - 更新了相关组件和测试文件中的引用 - 添加了新的测试方法 pullSync - 更新了版本号至 20250809
- *(pricing)* 重构商品毛利率获取逻辑仅使用shopGrossMargin配置 - 移除对 product.grossMargin 字段的依赖，统一使用 product.shopGrossMargin JSON 配置 - 优先从 shopGrossMarginConfigList 中查找匹配店铺ID的特定毛利率配置 - 如果找不到店铺特定配置，则使用 allShopGrossMargin 作为默认值 - 移除复杂的 grossMarginUseAllShop 判断逻辑和市场默认店铺匹配逻辑
- *(product)* 重命名AePricingDetailExcelDTO为AePublishProductPricingDetailExcelDTO
- *(product/strategy)* 优化AE商品导出任务策略性能 - 重构字典数据加载为预加载模式 - 增加基础数据批量预加载机制 - 延长相关缓存过期时间为5分钟 - 增加错误处理与日志记录
- *(product)* 优化速卖通商品发布导出策略的内存使用 - 将批量处理改为流式处理，避免内存占用过高 - 增加详细日志记录处理过程 - 重构数据处理逻辑为按产品逐个处理 - 移除不再使用的批量数据加载方法
- *(expression)* 简化中缀运算符优先级判断逻辑 将when表达式条件简化为直接匹配TokenType，合并边界token判断条件 refactor(ExpressionParserService): 优化日志输出级别 移除成功解析的info日志，将验证失败的日志级别从debug提升为warn
- *(product)* 移除花型图片相关代码及无用参数 主要变更： - 删除花型图片下载及处理逻辑 - 清理相关常量和方法参数 - 简化SKC图片处理方法
- *(product)* 移除未使用的SKC/SKU相关接口和方法 移除ProductPublishPlatformExportTaskStrategy中getSaleSkcList和getSaleSkuList接口方法及其实现，清理相关扩展方法和依赖注入

### 📚 Documentation

- 更新 README.md 项目文档 更新项目依赖版本和文档内容，优化项目描述和开发指南

### 🧪 Testing

- *(aliexpress-service)* 更新测试用例并调整SKU属性拼接逻辑 • 注释掉AliexpressServiceTest中的updatePlatform调用 • 添加新的商品详情查询测试用例 • 修改AeUpdateProductComponent中的SKU属性拼接格式为"propertyId:valueId" 修改主要涉及测试用例调整和SKU属性格式规范化

## [0.0.17-1] - 2025-08-07

### 🚀 Features

- *(AE模板SKU)* 为AE模板SKU添加店铺维度支持 • 在product_template_ae_sku表新增shop_id字段支持店铺维度 • 实现fixTemplateSkuShopId方法自动填充空shopId记录 • 添加测试用例验证修复功能 涉及修改： - 数据库DDL新增字段 - 新增Mapper更新方法 - 实现批量处理逻辑 - 添加测试类
- *(pricing)* 新增目的地列表缓存及相关物流成本计算方法
- *(pricing)* 新增获取发货地列表功能并优化缓存配置 在PricingVariableService中新增getShipFromList接口 优化缓存配置，新增CACHE_MAX_SIZE_MEDIUM和CACHE_MAX_SIZE_SMALL常量 调整各类缓存的最大大小配置 在AEPricingService中完善发货地获取逻辑
- *(pricing)* 重构AE定价模块命名和结构 - 重命名AE定价相关类，统一使用Ae前缀 - 将AeV2PricingCalculateResp迁移至AePricingCalculateResp - 优化AePricingResult数据结构 - 更新ProductManageController适配新命名
- *(product)* 新增平台店铺查询及划线价计算功能 fix(export): 修复AE商品导出价格计算逻辑 refactor(pricing): 重构定价配置类为可变属性 test(region-price): 完善区域价格规则服务测试
- *(商品管理)* 新增AE上架区域字典及默认店铺标识 • 在DictEnum中添加AE_PRODUCT_LISTING_REGION枚举项 • 在商品详情响应中增加defaultShopFlag字段标识默认店铺 • 通过dictClientExternal获取OPS默认店铺信息并设置标识 涉及文件修改： - DictEnum.kt - ProductManageDetailResp.kt - ProductManageServiceImpl.kt
- *(pricing)* 将supplyPrice重命名为costPrice并新增定价成本变量 修改定价计算相关文件，统一将supplyPrice字段更名为costPrice，并在ProductPriceVariableV2Enum中新增COST_PRICE变量。涉及PricingCalculatorV2、AePricingService等核心计算类及测试用例的调整。
- *(product)* 新增AE商品导入毛利率变更处理 新增UpsertProductByImportAeBO类用于处理导入AE商品时的毛利率变更状态。在ProductManageServiceImpl中，当导入AE商品时若目标毛利率变更，则重新计算划线价。
- *(pricing)* 添加跨境商品判断逻辑及优化定价计算 在ProductPublishHelper添加跨境商品判断方法isProductCrossBorder 优化AePricingService中定价计算逻辑，使用新的跨境判断方法
- *(product)* 移除未使用的依赖并添加aePricingService
- *(pricing)* 重命名calculateAe4DPricing为calculate4D
- *(ProductPriceRuleV2)* 移除产品价格规则表的唯一索引约束
- *(ProductPriceRuleV2)* 优化价格规则响应对象和服务实现 - 将ProductPriceFormulaRespV2的字段改为非空类型 - 新增CountryRoundingPrecisionBO类型处理国家小数位配置 - 简化规则组创建更新逻辑 - 移除未使用的测试响应类
- *(regionpricerule)* 修改平台字段为platformId 修改所有regionpricerule相关请求类中的platform字段为platformId，并更新测试用例和ServiceImpl中的相关逻辑
- *(regionpricerule)* 修改平台字段为platformId 修改regionpricerule相关请求类中的platform字段为platformId，并更新测试用例和ServiceImpl中的相关逻辑
- *(ProductPriceRuleV2Controller)* 新增批量验证公式表达式接口
- *(ProductPriceRuleV2)* 增加请求参数的业务校验逻辑
- *(pricing)* 优化汇率相关变量命名及计算逻辑 - 将exchangeRate重命名为cnyToUsdExchangeRate以明确含义 - 优化汇率计算逻辑，增加零值校验 - 统一物流成本计算精度为6位小数
- *(pricing)* 增加默认发货地及目的地标识功能
- *(页面配置)* 新增列默认显示属性并优化配置逻辑 在PageColumnEnum中新增defaultShow字段控制列默认显示状态 UserPageColumnConfigServiceImpl中优化未配置列的显示逻辑 更新版本号至20250806
- *(pricing)* 新增AE定价兼容服务及V2计算组件
- *(product)* 新增AE商品价格兜底判断功能 新增AE商品价格兜底判断相关接口及实现，包括： - 新增AeProductPriceAlertCheckReq请求类 - 新增AeProductPriceAlertCheckResp响应类 - 在ProductPriceAlertCheckService中实现AE价格检查逻辑 - 在ProductPriceAlertCheckController中新增AE相关接口
- *(pricing)* 重构AE定价服务使用V2组件计算 将AePricingService的计算逻辑重构为使用AePricingV2Component，移除直接模板SPU依赖，通过AeSkcDataConverter转换SKC数据格式，简化定价流程
- *(price-alert)* 支持AE平台区域定价校验 在AeProductPriceAlertComponent中实现区域定价平铺逻辑，将区域价格作为虚拟SKU加入校验流程。新增AeNationalQuoteSkuAdapter适配器处理区域定价数据，修改基础组件暴露配置加载方法。
- *(ProductPriceRule)* 添加手动清除价格计算缓存接口
- *(pricing)* 使用常量替换AE定价中的硬编码国家值
- *(pricing)* 支持默认规则组功能 - 新增对supplyMode为空的默认规则组支持 - 修改规则组查询逻辑以支持默认规则组 - 更新定价计算引擎以支持默认规则组回退机制 - 添加默认规则组唯一性校验
- *(售价计算)* 新增AE售价计算服务接口 • 添加AeSalePricingService接口用于AE平台售价计算 • 包含待上架商品和已上架商品两种计算场景 • 支持V1和V2版本的价格计算兼容 该接口为AE平台商品售价计算提供统一服务入口
- *(pricing)* 重构AE定价服务，拆分模板和销售场景 - 删除AePricingCompatibilityService及实现类 - 将AePricingV2Component移动到component/ae目录 - 新增AeSalePricingServiceImpl处理销售场景定价 - 新增AeTemplatePricingService处理模板场景定价 - 修改相关调用方适配新服务
- 移除AePricingService并重构相关依赖 删除AePricingService类，将其功能迁移至AeTemplatePricingService。更新AeProductExportComponent、RegionPriceRuleServiceImpl和AeProductManageComponent中的相关引用。
- *(pricing)* 重构AE定价相关DTO命名及引用 - 重命名PricingOverrideConfig为PricingVariablesOverrideConfigDto - 重命名PricingVariables为PricingVariablesDto - 重命名AePricingResult为AePricingResultDto - 重命名AePricingCalculateResp为AePricingCalResp - 调整相关引用及调用逻辑
- *(ae-product)* 增强AE商品价格计算逻辑 • 在CallAeComponent中新增自动价格计算功能 • 修改AeProductManageComponent的fillShopSkuData方法为公开方法 • 优化国家报价配置处理逻辑，支持多店铺SKU数据填充 涉及价格模板计算和店铺SKU数据复制功能增强
- *(product)* 新增AE价格兜底简化响应结构并优化检查逻辑 - 新增AeProductPriceAlertSimpleCheckResp简化响应结构 - 将shopId/shipFrom/shipTo移至SKU级别 - 优化价格检查错误码处理方式 - 重构请求响应类命名规范
- *(ProductPublishAeHelper)* 新增AE统一定价应用到SKU功能 fix(ProductAeServiceImpl): 重构商品复制逻辑使用统一定价服务 新增applyUnifiedPricingToSku方法支持V1/V2版本价格计算，包含区域定价逻辑。重构商品复制流程，替换原有价格计算方式为统一定价服务调用。
- *(regionpricerule)* 优化AE划线价计算逻辑 增加模板划线价优先使用逻辑，并优化店铺选择策略
- *(pricing)* 新增AE价格查找器优化定价性能
- *(pricing)* 优化价格计算逻辑及常量管理 1. 修改发货地匹配逻辑，支持ID和名称两种方式 2. 统一百分比值的处理方式，保留6位小数 3. 新增NumberConstants集中管理数字常量 4. 优化公式计算错误处理，直接抛出异常 5. 重构价格查找方法，提高代码复用性
- *(product)* 添加企划审核状态查询功能
- *(产品标签)* 新增毛利率标签支持 fix(DDL): 调整毛利率字段精度为decimal(10,2) chore(版本): 更新nacos版本号为20250807
- *(ProductManageController)* 新增AE售价计算接口
- *(ProductManageController)* 兼容V1V2版本的AE售价计算接口
- *(ae-sale-update)* 添加物流配置支持区域定价 • 引入regionPriceRuleLogisticsRepository获取物流成本配置 • 根据物流配置动态计算目的地价格并更新nationalQuoteConfig • 移除价格计算失败时的return逻辑，确保流程继续执行 涉及AeSaleUpdateServiceImpl.kt核心定价逻辑调整
- *(pricing)* 添加BigDecimal扩展函数并优化百分比计算逻辑 在EntityExtensions.kt中添加divideIfNotZero和toDecimalFromPercent扩展函数，优化PricingVariableServiceImpl中的百分比值处理，统一使用新扩展函数进行除法和百分比转换。同时简化汇率获取逻辑，当汇率无效时抛出异常。
- *(ProductPublishAeHelper)* 新增覆盖原价选项并优化价格计算逻辑 - 新增applyUnifiedPricingToSku方法支持覆盖原价选项 - 优化价格计算逻辑，记录价格变更历史 - 移除冗余代码，直接修改原对象而非创建副本
- *(pricing)* 优化价格计算逻辑并添加计算详情 重构 PricingCalculatorV2 的价格计算流程，使用 FormulaCalculationResult 封装计算结果，并在 AePricingResultDto 中新增 calculationDetails 字段记录计算详情
- *(pricing)* 统一变量命名并添加百分比单位说明 将营销费用和物流支出相关变量统一命名为marketingRate和logisticsRate，并在DTO字段注释中添加百分比单位说明

### 🐛 Bug Fixes

- *(ProductPriceRule)* 移除规则组ID非空校验注解并添加业务逻辑校验
- *(product-price)* 移除roundingPrecision非空校验并优化国家小数位配置 修改ProductPriceRuleV2Req.kt中roundingPrecision字段的非空校验，将countryRoundingPrecisions类型从String改为List<CountryRoundingPrecisionBO>。同时在ProductPriceRuleV2ServiceImpl.kt中添加按国家配置时的非空校验逻辑。
- *(sql)* 统一唯一键命名规范为uniq前缀 修改产品价格规则V2表SQL脚本，将唯一键命名从uk_前缀统一改为uniq_前缀
- *(pricing)* 移除shipFrom参数的可空性标记 修改PricingVariableService接口及实现，将shipFrom参数从String?改为String类型，并移除相关空值检查逻辑
- *(sql)* 标准化定价规则V2表公共字段定义 修改产品定价规则V2相关表的公共字段定义，统一字段类型和顺序，增加注释清晰度
- *(RegionPriceRuleController)* 修改平台参数类型为String并添加校验 将平台参数从PlatformEnum改为String类型，增加平台编码有效性校验，无效时抛出BusinessException
- *(product-ae)* 修复物流配置匹配问题并补充目的地数据 • 将物流配置匹配从发货地名称改为ID，确保正确性 • 新增获取物流配置列表方法，支持发货地到多目的地的映射 • 自动填充sku中缺失的目的地配置，保持数据完整性 涉及模块：产品服务、物流配置仓库、AE产品管理组件
- *(ProductPriceRuleV2ServiceImpl)* 统一使用SaveMode.INSERT_ONLY保存实体
- *(ProductPriceRuleV2)* 修复规则详情响应中groupId传递问题 修改convertToRuleDetailResp及相关方法，确保规则详情响应中正确包含groupId参数
- *(region-price-rule)* 修复物流成本和运费率计算逻辑 • 移除多余的lambda表达式大括号导致的计算错误 • 将first替换为firstOrNull以避免空指针异常 • 修正运费率计算中的min/max方法调用错误 修复了在特定情况下可能导致物流成本和运费率计算不准确的问题
- *(regionpricerule)* 修正运费率请求字段名 将feightRateDtoList改为feightRateReqList以保持命名一致性，同时更新相关测试用例和服务层代码。
- *(region-price-rule)* 添加异常处理以防止物流配置转换失败 • 在拆分物流配置key时添加try-catch块处理潜在异常 • 记录转换失败的错误日志以便排查问题 • 防止因格式错误的key导致服务中断 该修改增强了RegionPriceRuleServiceImpl的健壮性，避免因意外数据格式导致的服务崩溃
- *(regionpricerule)* 优化空值检查使用扩展方法
- *(region-price-rule)* 修复物流配置空指针问题 • 在获取默认规则时增加shippingPlaceId非空过滤 • 在分组查询时同样添加shippingPlaceId非空检查 • 防止因空shippingPlaceId导致的NPE异常
- *(ProductPriceRuleV2ServiceImpl)* 修改规则组保存模式逻辑 根据groupId判断使用INSERT_ONLY或UPDATE_ONLY模式
- *(product)* 移除AE模板SKU价格自动计算逻辑 将价格计算逻辑移至统一服务aePricingService处理
- *(ProductPriceRuleV2ServiceImpl)* 优化规则组删除逻辑并添加空值检查 删除规则组时自动删除关联规则，同时为保存条件和公式添加空值检查
- *(enums)* 统一产品价格变量枚举值为大写格式
- *(ProductPriceRuleV2ServiceImpl)* 修复规则组保存后查询失败问题 修改保存逻辑，增加保存后查询校验，避免返回未持久化的数据
- *(pricing)* 优化税率计算逻辑和物流成本处理 refactor(variable): 支持固定值变量名验证 feat(pricing): 增加目标货币国家参数
- *(pricing)* 修复公式变量空指针检查并优化日志级别 1. 增加公式变量空值检查，避免空指针异常 2. 调整ASP配置缺失时的日志级别为debug 3. 优化公式计算失败日志，增加表达式信息
- *(region-price)* 修复物流和仓储成本计算逻辑 • 将max()/min()替换为maxByOrNull()/minByOrNull()以避免潜在空指针异常 • 修正仓储成本最小值计算错误（之前错误使用max()） • 更新服务版本号至20250806以标记本次修复
- *(页面列配置)* 修改默认显示逻辑，将boolean类型改为int类型 修改PageColumnEnum中defaultShow字段类型为Int，并调整UserPageColumnConfigServiceImpl中相关逻辑，统一使用1/0表示是否显示。未配置时默认显示改为1。
- *(product-management)* 在商品管理服务中添加shopId字段 • 在ProductManageServiceImpl中为商品添加shopId字段赋值 • 确保商品与店铺关联关系正确建立 • 修改涉及Lazada平台商品同步逻辑 该修改确保商品能够正确关联到所属店铺，解决商品归属问题
- *(region-price-rule)* 移除物流配置中是否默认的必填校验 修改导入监听器和DTO，将"是否默认"字段从必填改为可选，并调整服务实现中的相关校验逻辑
- *(regionpricerule)* 移除SaveRegionPriceRuleAdReq中shopId的非空校验
- *(ae-product)* 在查询AE店铺时添加认证过滤条件 • 修改AeProductManageComponent.kt中的店铺查询逻辑 • 添加对Shop::isAuth字段的过滤，仅查询已认证店铺(Bool.YES.code) • 优化店铺数据获取流程，确保只处理有效店铺 该修改确保产品管理功能仅操作经过认证的AE店铺数据，提高系统安全性
- *(cache)* 调整缓存过期时间为1分钟 修改ProductPriceCalcHelperV2和PricingVariableServiceImpl中的缓存过期时间，从5分钟缩短为1分钟
- *(regionpricerule)* 优化物流和仓储成本计算逻辑 当有选款店铺时优先使用店铺国家配置，否则使用平台默认值
- *(ProductPriceRuleV2ServiceImpl)* 处理supplyMode为空字符串的情况 当supplyMode为空字符串时设置为null
- *(regionpricerule)* 修复平台ID校验及错误提示信息 校验规则中增加平台ID非空检查，并完善错误提示信息
- *(ae-product)* 修复发货地属性处理逻辑并移除TODO注释 • 在AliexpressProperties中新增发货地属性名称和值名称配置 • 完善ProductManageServiceImpl中发货地属性的默认值填充逻辑 • 移除AeProductManageComponent中关于价格计算的TODO注释 涉及发货地属性配置和默认值处理的优化，确保属性展示完整性和一致性
- *(ae-product)* 为AE商品发货地添加默认值处理 • 当请求中未提供发货地信息时，默认使用中国作为发货地 • 新增AliexpressProperties配置类依赖以获取平台默认发货地属性 • 引入ProductAePropertyValueItemDTO用于构建默认发货地数据 该修改解决了商品发布时因缺少发货地信息导致的异常问题
- *(regionpricerule)* 简化物流费用计算逻辑
- *(ae-product)* 修复AeUpdateProductComponent中的map赋值错误 • 将直接赋值改为使用put方法设置map键值对 • 确保AePlatformNationalQuoteConfigDto中absoluteQuoteMap的正确构建 • 避免潜在的map初始化问题 该修改解决了Kotlin中map直接赋值的语法错误问题，确保代码能够正确编译和执行。
- *(regionpricerule)* 简化Lazada/TEMU仓储成本计算逻辑 移除按国家过滤的逻辑，直接取平台配置的最大最小值
- *(ae)* 移除企划审核强制校验 前端已做提示，后端不再强制检验商品企划审核状态
- *(ae-price-matching)* 修正发货地属性值匹配逻辑 • 将发货地匹配条件从名称改为ID字符串比较 • 更新默认价格和全国报价配置的价格匹配逻辑 • 确保价格匹配更准确，避免因名称变化导致错误 修复发货地匹配使用名称而非ID的问题，提高价格匹配的稳定性
- *(regionpricerule)* 优化毛利率计算逻辑 重构毛利率计算方法，移除defaultRule依赖，改为通过shopId判断
- *(regionpricerule)* 修改默认规则判断条件为shopId判空 将defaultRule判断改为shopId是否为null，保持逻辑一致性
- *(ae-pricing)* 修复发货地和目的地默认值逻辑问题 修改AePricingV2Component中发货地和目的地的默认值处理逻辑，当未提供时使用配置值或默认值中国/US。重构defaultShipToMap为defaultShipFromPairMap以更好表示数据结构。
- *(ae-pricing)* 修复计算售价时未传递店铺ID的问题 • 在CallAeComponent.kt中修改autoCalPriceByTemplate调用，增加店铺ID参数 • 将价格计算失败时的异常抛出改为日志记录 • 更新versions.properties中的服务版本号至20250807 说明：本次修改主要解决跨店铺价格计算不准确的问题，同时优化了错误处理方式
- *(product)* 修正定价计算成功状态判断逻辑 修改AePricingCalResp和AeSalePricingServiceImpl中success字段的注释说明，并调整计算失败时的判断逻辑，从"部分失败"改为"全部失败"时才返回错误信息
- *(price/sale)* 默认价格计算规则从V1改为V2
- *(数据库脚本)* 更新DML脚本以修复发货来源属性问题 • 修正plan_audit表的同步条件格式 • 为product_template_ae_sku表添加缺失的发货来源属性 • 为ae_sale_sku表添加相同的发货来源属性 涉及两个主要表结构的属性补充和SQL语法规范化
- *(ae-sale-update)* 移除价格计算失败时的提前返回逻辑 • 删除AeSaleUpdateServiceImpl中价格计算失败时的return@forEach语句 • 允许流程继续执行而非中断，即使价格计算失败 • 修改涉及自动售价计算错误处理部分的逻辑 该变更使得即使部分商品价格计算失败，批量处理流程仍能继续执行其他商品
- *(ae)* 优化售价计算错误日志，增加商品信息 在AeSaleUpdateServiceImpl和ProductAeServiceImpl中，为售价计算失败的错误日志添加spuCode和saleGoodsId信息，便于问题排查
- *(product)* 替换价格计算逻辑为统一定价服务
- *(export)* 修复物流成本等字段空值处理问题
- *(docs)* 移除product_price_formula_v2表的唯一索引约束
- *(pricing)* 修正毛利率计算中的百分数转换问题
- *(product)* 修复SKU级别店铺ID匹配问题 将循环变量从shopId改为tempShopId以避免命名冲突，并确保shopId正确赋值
- *(pricing)* 修复物流规则查询代码格式问题
- *(regionpricerule)* 优化折扣率规则匹配逻辑 修改RegionPriceRuleServiceImpl中的折扣率计算逻辑，增加运营模式匹配条件，并调整匹配优先级
- *(pricing)* 移除未使用的HUNDRED常量并简化FALLBACK_VALUE引用
- *(产品定价)* 过滤skc为空的定价请求 • 在映射skcList前添加过滤条件，排除skc为null的项 • 避免后续处理中出现空指针异常 • 保持原有数据结构不变的情况下增强健壮性
- *(pricing)* 优化定价变量规则匹配逻辑，使用firstOrNull替代find refactor(region-price-rule): 调整折扣率匹配逻辑，增加运营模式+供给方式匹配条件
- *(product)* 修复组合商品SKU价格处理逻辑 处理组合商品时增加空值检查，避免NPE问题
- *(product)* 修复成本价格更新逻辑和SKC空值处理 - 优化ProductAeConfirmUpdateServiceImpl中的成本价格更新状态设置 - 增加SKC空值检查避免NPE - 调整ProductAeServiceImpl中的价格应用逻辑
- *(regionpricerule)* 修正供应类型判断条件
- *(pricing)* 修复定价变量服务中费率匹配逻辑问题
- *(product-service)* 修复商品价格逻辑 • 修改ProductAeServiceImpl和ProductManageServiceImpl中的价格设置逻辑 • 当salePrice为空时默认使用retailPrice • 确保商品SKU价格始终有有效值 涉及商品价格计算的健壮性改进
- *(region-price-rule)* 移除默认规则必填校验并优化错误信息 主要变更： 1. 移除退货率、广告成本等配置的默认规则必填校验 2. 优化错误信息中的平台名称显示 3. 调整测试用例中的默认规则设置
- *(product)* 优化 ETA 更新逻辑 - 添加错误信息记录，当 Lazada 或 AE 更新失败时，将错误信息添加到 errorList- 在 SpuItemStatusUpdateCallBackReq.SpuInfo 中添加 errorMsg 字段，用于存储错误信息 - 优化代码格式和缩进
- *(product-eta)* 优化平台错误消息格式 • 修改Lazada和AE平台的错误消息格式，增加平台标识 • 简化错误消息内容，移除冗余文本 • 保持原有错误处理逻辑不变 涉及文件：ProductETAServiceImpl.kt的异常处理部分
- *(product-eta)* 优化错误消息处理和异常构造 • 在ProductETAServiceImpl中为错误消息添加去重处理 • 修改PublishGlobalBizException的构造方法以包含cause的message • 更新测试用例验证异常处理逻辑 • 升级服务版本号至20250805 涉及核心异常处理和错误消息展示优化
- *(product-eta)* 优化平台更新失败的错误信息处理 • 将Lazada和AE平台更新失败的错误信息统一改为"更新详情失败" • 移除原始异常信息以避免暴露敏感细节 • 保持原有错误处理流程不变 修改了ProductETAServiceImpl.kt中的错误信息格式，同时调整了gradle.properties中的JVM内存参数配置

### 💼 Other

- 更新 ofp-sdk 版本至 0.0.7-SNAPSHOT - 将 ofp-sdk 版本从 0.0.4-RELEASE 升级到 0.0.7-SNAPSHOT - 此更新可能包含新的功能和修复

### 🚜 Refactor

- *(pricing)* 重命名目的地相关方法为shipTo命名规范
- *(pricing)* 移除AE定价相关DTO并重构响应类命名 - 删除AEPricingRequestV2、AEPricingResultV2、AEPricingVariables等DTO - 将AEPriceResult重命名为AePricingResult - 将AEV2PricingCalculateResp重命名为AeV2PricingCalculateResp
- *(export/pricing)* 优化AE定价相关类命名及逻辑 - 将AEV2PricingCalculateReq重命名为AePricingCalculateReq - 将AEPricingService重命名为AePricingService - 简化AeProductExportComponent中的空值判断逻辑 - 优化RegionPriceRuleServiceImpl中的变量声明及逻辑
- *(regionpricerule)* 替换platformId为PlatformEnum类型 主要变更： - 将SaveRegionPriceRule*Req中的platformId字段替换为PlatformEnum类型 - 修改相关Service和Controller方法，使用PlatformEnum替代platformId参数 - 更新测试用例以适配新的PlatformEnum参数
- *(product-management)* 重构AE商品详情逻辑至独立组件 • 将AE商品详情逻辑从ProductManageServiceImpl提取至AeProductManageComponent • 新增组件处理AE商品详情获取、SKU数据填充等核心逻辑 • 优化代码结构，提高复用性和可维护性
- *(ae-product)* 移除TODO注释并重新定位价格计算逻辑 • 删除已完成的TODO注释，该注释涉及重算价格逻辑 • 将新的TODO注释移至更合适的位置，明确需要处理SKU默认价格和区域定价 • 保持现有价格字段赋值逻辑不变 注意：价格计算功能需后续由aePricingService.calculate4D实现
- *(ae-product)* 更新AE上架区域枚举命名及相关引用 • 将DictEnum中的AE_PRODUCT_LISTING_REGION重命名为AE_LIST_REG以保持命名一致性 • 更新AeProductManageComponent中对应的字典引用 • 移除TODO注释，使用新的枚举值获取默认店铺 该修改优化了代码的可读性并保持命名规范统一
- *(regionpricerule)* 优化RegionPriceRuleServiceImpl逻辑与修复存储成本最小值计算错误 - 重构默认店铺计算逻辑，使用val避免闭包捕获可变引用 - 修复storageCostMin错误使用max()的问题 - 简化物流成本配置的lambda表达式，移除多余花括号 - 优化AE物流配置处理，避免名称遮蔽 - 修复freightRateMin错误使用maxByOrNull的问题 - 优化getAeRetailPrice方法异常处理与返回值
- *(ae-product)* 优化AE商品组件和内存配置 • 移除CallAeComponent中未使用的imageRepositoryRepository依赖 • 简化gradle.properties中的JVM内存配置，使用更简洁的单位表示 • 清理ProductManageServiceImpl中的冗余代码和变量声明 这些修改提高了代码整洁度和可维护性，同时保持功能不变
- *(enums)* 移除枚举类中未使用的工具方法
- *(RegionPriceRuleServiceImpl)* 提取物流、仓储等成本计算逻辑为独立方法
- *(ae-product)* 移除自动价格计算相关代码 • 删除与productPriceManagementService相关的自动价格计算逻辑 • 清理不再使用的AutoCalPriceReq和AutoCalCountryPriceResp导入 • 简化价格字段赋值逻辑，直接使用aeSku中的价格数据 该修改移除了不再需要的价格计算功能，简化了代码结构
- *(ae-product-price)* 重构速卖通商品价格计算逻辑 • 移除旧的基于product_skc的价格计算方式 • 引入AeSalePricingService进行统一价格计算 • 增加发货地与目的地匹配的价格计算逻辑 涉及主要文件： - AeSaleUpdateServiceImpl.kt - CallAeComponent.kt 本次重构优化了价格计算流程，支持更灵活的发货地配置
- *(ae-sale-update)* 重构AE商品价格计算逻辑 • 移除旧的AutoCalPriceReq和productSkcMap价格计算方式 • 使用新的aeSalePricingService.autoCalPriceBySale接口计算价格 • 根据发货地和目的地动态匹配价格，支持默认价格回退机制 涉及主要修改：价格计算逻辑重构，支持更灵活的发货地价格配置
- *(ae-product)* 移除ProductAeServiceImpl中未使用的priceService依赖 feat(ae-template): 重构AE模板价格计算逻辑，使用aeSalePricingService直接计算售价
- *(ae-pricing)* 重构AE商品价格计算逻辑 • 移除AliexpressConstants依赖，改用aeSalePricingService统一处理价格计算 • 新增发货地与目的地价格匹配逻辑，支持不同物流路线的差异化定价 • 优化价格更新流程，增加错误处理和日志记录 涉及AeSaleUpdateServiceImpl、DingTalkApprovalServiceImpl和ProductAeConfirmUpdateServiceImpl三个服务类
- *(pop-product-service)* 移除阿里速卖通 SDK 依赖 - 更新 EtaClientExternal 和 StockClientExternal 中的导入路径- 从 PopApplication 中移除阿里速卖通的 Feign客户端 - 更新版本号至 20250804
- *(pop-product-service)* 优化 ETA 更新错误信息 - 在 ProductETAServiceImpl 类中，更新了 Lazada 和 AE 错误信息的格式 - 错误信息现在包含了 SPU 编码，以便更准确地识别问题所在

### 📚 Documentation

- *(config)* 添加商品运营平台服务的Nacos配置 • 新增市场默认店铺配置，用于待上架商品定价计算 • 包含zd市场和平台ID为3的店铺信息 • 配置文件路径：docs/2025/0807/nacos.yml

### 🧪 Testing

- *(RegionPriceRuleServiceTest)* 修改运费规则测试方法名
- *(RegionPriceRuleService)* 禁用测试类并调整Gradle内存配置 • 在RegionPriceRuleServiceTest类上添加@Disabled注解以跳过测试 • 修改gradle.properties中的JVM内存参数配置 • 将最大堆内存从2g调整为1g，元空间上限从1g调整为512m 注意：测试类被临时禁用，需后续跟进处理

### ⚙️ Miscellaneous Tasks

- *(dependencies)* 更新 ofp-sdk 版本至稳定版 • 将 ofp-sdk 从 0.0.7-SNAPSHOT 升级至 0.0.7 • 移除快照版本依赖，使用正式发布版本 • 保持其他依赖版本不变 注意：此次变更不包含破坏性修改

## [0.0.17] - 2025-08-01

### 🚀 Features

- *(product)* 新增商品企划审核功能 - 新增 ProductPlanAuditReq 请求类 - 新增 ProductPlanAuditStateEnum 枚举类 - 修改 Product 实体类添加审核相关字段 - 修改 ProductManageService 添加审核方法 - 修改 ProductManageController 添加审核接口 - 修改 DDL.sql 添加数据库字段 - 删除废弃的区域定价查询请求类
- *(price)* 销售定价规则 V2
- *(fix-data)* 添加修复模板 SKU 店铺 ID 功能- 在 FixData2Service 接口中新增 fixTemplateSkuShopId 方法 - 在 FixData2ServiceImpl 类中实现 fixTemplateSkuShopId 方法 - 在 TestInnerController 中添加修复模板 SKU 店铺 ID 的 API 接口
- *(产品管理)* 新增用户页面列配置功能 新增用户自定义页面列展示配置功能，包括： 1. 新增PageCodeEnum和PageColumnEnum枚举 2. 新增UserPageColumnConfig相关实体、Mapper、Repository 3. 新增UserPageColumnConfigService及实现 4. 新增UserPageColumnConfigController提供API 5. 新增相关DTO和Req/Resp对象
- *(pricing)* 移除废弃的UpdatePriceReq及相关逻辑 - 删除UpdatePriceReq文件及所有相关引用 - 移除ProductPriceManagementService中的updatePrice接口及实现 - 清理ProductCreateV2Service中不再使用的autoCalPrice方法 - 简化ProductPriceVariableV2Enum和ProductPriceTargetFieldV2Enum枚举 - 更新versions.properties版本号
- *(product)* 新增商品价格计算规则V2并实现时间切换逻辑 1. 新增PriceCalculateRuleEnum枚举定义V1/V2价格计算规则 2. 在Product实体类添加priceCalculateRule字段 3. 在CommonProperties配置切换时间userPriceCalculateRuleV2Time 4. 在商品创建/导入/同步逻辑中根据时间自动设置计算规则 5. 添加企划审核状态检查方法checkPlanAuditPassState 6. 更新DDL/DML脚本和版本号
- *(product)* 添加 AE 国家报价默认物流成本配置功能 - 在 AeNationalQuoteConfigDto 中添加 defaultFlag 字段，用于标识是否
- *(product)* 新增区域价格规则服务及商品定价相关功能 1. 新增RegionPriceRuleService及相关实现，提供物流成本查询功能 2. 商品管理页响应类增加定价相关字段 3. 商品实体新增毛利率字段 4. 配置类新增市场默认店铺配置 5. 企划审核状态检查逻辑移至ProductRepository 6. 修改商品管理服务实现类，集成区域价格规则服务
- *(regionpricerule)* 新增AE物流费用和支出比例导入功能 - 新增ImportAERegionPriceRuleFreightRateExcelListener和ImportAERegionPriceRuleLogisticsExcelListener监听器 - 新增ImportRegionPriceRuleFreightRateDTO和ImportRegionPriceRuleLogisticsDTO数据传输对象 - 在RegionPriceRuleManageServiceImpl中实现AE物流费用和支出比例导入逻辑 - 在RegionPriceRuleController中新增导入接口 - 新增AE_SHIPPING_FROM和AE_SHIPPING_TO字典枚举 - 修改相关DTO和Resp对象，增加shippingPlaceId字段
- *(product)* 实现商品区域定价规则计算功能 scope: ProductManageServiceImpl, RegionPriceRuleService及相关DTO/Resp 1. 新增ProductRegionPriceRuleDto用于封装商品定价相关数据 2. 实现RegionPriceRuleService接口，提供各类定价规则查询 3. 在ProductManageServiceImpl中集成区域定价计算逻辑 4. 修改CommonProperties配置类，增加平台ID字段 5. 修复ProductManagePageResp中字段命名错误
- *(v2)* 新增租户ID字段并优化定价规则状态字段 - 在ProductPriceRuleGroupV2等实体类新增tenantId字段 - 将status字段重命名为enabled以更准确表达启用状态 - 新增ProductPriceConditionVariableV2Enum枚举类 - 优化SQL表结构及索引
- *(product-price)* 定价规则V2模块优化 - 删除表达式计算逻辑，改为基于公式明细的配置方式 - 将supplyType字段重命名为supplyMode - 新增ProductPriceFormulaDetailTypeV2Enum枚举 - 新增ProductPriceFormulaDetailDto数据传输对象 - 自动生成规则优先级，移除手动设置 - 移除规则测试接口
- *(ProductPriceRuleV2)* 新增批量保存规则组及规则功能
- *(ProductPriceRuleV2)* 新增批量保存规则组及规则接口 refactor(FormulaCalculationService): 移除变量提取功能 refactor(ProductPriceRuleV2): 优化规则保存逻辑，移除测试请求类
- *(定价规则)* 将产品定价相关枚举和服务重命名为销售定价 主要变更包括将产品定价相关枚举类和服务接口中的命名从"产品定价"统一修改为"销售定价"，涉及多个枚举类和服务实现文件。
- *(product)* 新增商品毛利率配置功能 - 新增UpdateProductGrossMarginReq请求类 - Product实体新增grossMarginUseAllShop字段 - 实现毛利率配置的Controller、Service层逻辑 - 修改RegionPriceRuleServiceImpl中的价格计算逻辑 - 更新DDL.sql表结构 - 调整CommonProperties配置类结构
- *(product)* 添加商品毛利率标签功能
- *(product)* 统一使用V2定价计算规则 移除定价规则V1相关逻辑，所有商品默认使用V2规则。删除CommonProperties中的userPriceCalculateRuleV2Time配置项。
- *(产品模块)* 调整价格计算规则相关字段位置及逻辑 - 将priceCalculateRule字段从Product实体迁移至AeSaleGoods和ProductTemplateAeSpu实体 - 新增导入导出功能中的价格相关字段处理 - 优化区域定价规则服务接口参数 - 调整产品创建和同步时的价格计算规则默认值设置逻辑
- *(pop-product-service)* 添加 AE品类属性数据同步功能- 新增 AeCategoryAttrGroupTmp、AeCategoryAttrValueTmp 和 AeCategoryTmp 三个实体类- 实现递归查询和保存 AE 品类树的功能- 实现递归查询和保存 AE 品类属性的功能- 优化测试用例，添加数据同步相关的测试
- *(pricing)* 新增AE V2定价计算功能，定价参数配置应用
- *(pricing)* 重构定价计算引擎V2并新增相关组件 重构PricingCalculatorV2实现独立计算逻辑，新增ProductPriceCalcHelperV2缓存组件，添加ProductPriceCalcV2BO等数据类，扩展ProductPriceVariableV2Enum增加FIXED_VALUE枚举值
- *(pricing)* 移除定价策略模块及相关代码 - 删除AE4DStrategy、Lazada3DStrategy、Temu3DStrategy等定价策略实现 - 删除PricingEngine核心定价引擎 - 删除PricingStrategy接口及请求响应类 - 清理Controller和Service中定价计算相关接口
- *(product)* 新增AE V2定价计算接口及DTO - 新增AEV2PricingCalculateReq请求类 - 新增AEV2PricingCalculateResp响应类 - 修改ProductManageController实现V2定价计算接口
- *(pricing)* 实现AE四维定价计算与保存功能
- *(pricing)* 新增获取默认目的地功能并优化价格计算 1. 在PricingVariableService中新增获取默认目的地接口 2. 重构getDestinationListByShipFrom方法支持返回默认目的地 3. 优化AEPricingService中的价格计算逻辑，支持默认目的地价格设置
- *(database)* 新增AE精选相关数据库表结构 新增ae_job_time、ae_selected_product_spu、ae_selected_product_sku、ae_update_platform_task表结构，用于支持AE精选商品功能
- *(dao)* 新增TryOn任务相关SQL及Kotlin实体类 新增try_on_task和try_on_task_qc表结构 新增TryOnTask和TryOnTaskQc实体类及相关Mapper、Repository
- *(docs/DDL.sql)* 新增店铺ID字段并优化表结构 1. ae_job_time表新增shop_id字段 2. 标准化POP字段缩进格式 3. 新增ae_selected_product_image表 4. 更新ae_update_platform_task表状态描述
- *(docs)* 更新ae_selected_product_image表结构 新增version_watermark字段，修改ae_image_url为ae_image_urls，新增try_on_image_url字段
- *(tryon)* 新增AI试衣任务及质检功能 主要变更： 1. 新增TryOnTask和TryOnTaskQc相关实体类、DTO、请求响应类 2. 新增TryOnTaskController和TryOnTaskQcController 3. 实现TryOn任务配置、规则创建和质检功能 4. 修改DDL.sql和实体类字段(auto_retry改为stop_retry)
- *(docs)* 重构AE商品表结构并优化索引 - 将ae_selected_product_spu重命名为ae_platform_product_spu并精简字段 - 新增ae_platform_product_sku表 - 优化索引结构并调整表注释 - 为ae_selected_product_image表新增recognition_param字段
- *(tryon)* 更新试穿任务服务接口及版本号 1. 修改TryOnTaskServiceImpl实现新的TryOnTaskService接口 2. 更新versions.properties中的nacos版本号为20250715
- *(tryon)* 新增TryOnHelper并集成到TryOnTaskServiceImpl 新增TryOnHelper辅助类用于AI任务处理，并在TryOnTaskServiceImpl中调用getDetailByIds方法
- *(tryon)* 重构tryOn任务详情相关功能 - 删除旧版TryOnTaskResp - 新增TryOnTaskDetailResp和TryOnTaskQcResp - 实现tryOn任务详情查询接口 - 完善质检任务详情查询逻辑 - 新增Repository查询方法
- *(tryon)* 新增AIGC服务SDK依赖及任务参数DTO重构 - 添加aigc-server-client和aigc-server-common依赖 - 重构TaskParamDto适配AIGC服务接口 - 更新TryOnHelper实现任务推送逻辑 - 调整版本号至20250716
- *(shop)* 新增外部商品标识字段 在Shop实体类、响应对象及数据库表中添加external字段，标识是否外部商品(0-否,1-是)
- *(selected)* 新增AE精选商品相关接口及数据结构
- *(product)* 新增商品主题字段 在CreateProductDto和Product实体类中新增productThemeCode和productThemeName字段
- *(product)* 新增AE精选商品相关实体类及接口 fix(docs): 修正DDL.sql中主键字段名 feat(controller): 实现AE精选商品分页及统计接口 feat(service): 添加AE精选商品服务层实现 feat(entity): 新增AE平台商品SPU/SKU实体类 feat(mapper): 添加AE平台商品相关Mapper接口 feat(repository): 实现AE平台商品数据访问层 feat(req): 为AE精选商品操作请求添加参数校验 feat(resp): 新增AE精选商品分页统计响应类
- *(ae-product-service)* 添加AE商品数据拉取服务接口和实现 • 新增AePullProductDataService接口定义拉取数据功能 • 实现AePullProductDataServiceImpl服务类并注入相关仓储 • 预留pull()方法待后续实现具体业务逻辑
- *(商品服务)* 优化AE商品数据拉取功能 • 在AePullProductDataServiceImpl中添加AliexpressService依赖以支持分页查询 • 实现分页查询商品列表接口pageProduct • 重构实体类代码格式并统一继承BaseEntityWithNamedAndReviser 涉及文件：AePullProductDataServiceImpl.kt、AliexpressService.kt、AliexpressServiceImpl.kt及多个实体类
- *(ae-product)* 新增商品状态枚举及分页查询支持 • 添加AeProductStatusType枚举类定义6种商品业务状态 • 修改ae_job_time表结构增加product_status_type字段 • 实现按商品状态分页查询的逻辑基础 涉及数据库变更，需同步执行DDL更新
- *(商品中心)* 优化AE商品数据拉取功能 • 新增AE商品分页查询响应对象AliexpressProductPageResponse • 重构商品数据拉取服务，支持按状态分页查询 • 完善商品SPU/SKU的upsert操作及字段映射 涉及主要修改： - 新增商品分页响应数据结构 - 优化商品数据拉取逻辑，支持增量同步 - 修正实体类字段映射关系 - 更新数据库表结构定义 - 增加任务时间记录功能
- *(产品识别)* 优化商品拉取与识别任务处理 • 修改DDL表结构，增加租户和审计字段 • 重构商品拉取逻辑，添加水印生成和识别任务自动创建 • 新增日期时间处理工具方法，支持按天划分任务 涉及主要变更：商品表结构调整、拉取服务增强、识别任务自动处理
- *(tryon)* 重构AI tryOn任务模块 主要变更： 1. 新增TryOnTaskSub实体及相关枚举类 2. 重构TryOnTask实体及响应类，移除TaskParamDto 3. 新增多个请求类用于配置试穿任务 4. 实现任务推送、状态同步和质检任务生成逻辑 5. 更新数据库DDL，新增try_on_task_sub表 涉及文件： - 新增AiModelTypeReq等11个请求类 - 新增AiTaskStateEnum等4个枚举类 - 新增TryOnTaskSub实体及Mapper/Repository - 修改TryOnTask等5个实体类 - 重构TryOnHelper等4个服务类 - 更新DDL.sql及build.gradle.kts
- *(tryon)* 新增商品快照功能及优化tryOn任务逻辑 - 新增 TryOnTaskProductSnapshotDto 用于存储商品快照信息 - 在 TryOnTask 实体中增加 isLatest 字段标记最新任务 - 实现商品快照查询及试穿任务创建时自动关联快照 - 优化试穿任务创建逻辑，自动过期旧任务 - 修改 PrepareTryOnTaskResp 中商品图片字段命名
- *(tryon)* 实现prepareTryOnTaskByProductId接口并优化商品快照结构 - 在TryOnTaskServiceImpl中实现prepareTryOnTaskByProductId方法 - 重构PrepareTryOnTaskResp数据结构 - 新增TryOnTaskRepository.listLatestByProductIds方法 - 优化TryOnTaskProductSnapshotDto字段 - 重命名AeSelectedProductSpuRepository.getProductSnapshotByProductIds方法
- *(tryon)* 新增TryOn任务质检功能及重试逻辑 - 新增TryOnTaskQcMapper.xml实现获取待质检任务 - 实现质检提交、批量质检及自动重试功能 - 优化TryOnTaskQcDetailResp字段 - 新增reDoTryOnTask方法支持任务重试 - 完善质检结果处理及AE商品推送逻辑
- *(tryon)* 更新试穿质检接口返回类型为可空 chore(versions): 更新nacos服务发现版本至20250718
- *(pop-product)* 集成butted-sdk并添加ComfyUI任务功能 • 在versions.properties中更新nacos元数据版本至20250718 • 添加butted-sdk依赖并配置gradle相关文件 • 新增ComfyuiTaskClientExternal类实现批量任务管理功能 涉及模块：pop-product-service
- *(商品更新任务)* 添加TryOn任务关联支持 • 在AeUpdatePlatformTask实体类中新增tryOnTaskId字段 • 更新DDL.sql中相关表结构，添加tryOnTaskId字段和索引 • 优化表字段格式和注释一致性 涉及商品更新任务与TryOn任务的关联功能增强
- *(商品管理)* 添加AE商品版本控制及分页查询功能 • 在AeUpdatePlatformTask实体类中新增versionNum字段用于任务版本控制 • 扩展AeSelectedProductPageReq查询参数，支持创建时间和更新时间的范围查询 • 实现AE商品分页查询功能，包含店铺、品类、状态等多条件筛选 涉及主要修改： - 数据库表结构新增version_num和ae_category_id等字段 - 商品分页查询接口支持多条件筛选 - 商品拉取逻辑完善，补充AE商品信息存储
- *(商品统计)* 实现AE商品分页统计功能 • 新增AeSelectedProductCountDto用于存储商品状态统计结果 • 在Repository和Mapper中实现countAe查询方法 • 完善AeSelectedProductServiceImpl中的pageCount逻辑 涉及文件： - 新增AeSelectedProductCountDto.kt - 修改AeSelectedProductSpuRepository.kt - 修改AeSelectedProductServiceImpl.kt - 修改AeSelectedProductSpuMapper.xml - 修改AeSelectedProductSpuMapper.kt - 修改AeSelectedProductPageCountResp.kt
- *(商品更新)* 实现AE平台商品更新任务统计功能 • 在AeUpdatePlatformTaskRepository中添加countProgress方法统计任务状态 • 新增AeUpdateTaskStatusEnum枚举类定义任务状态 • 修改ProductAePublishStateEnum处理空状态值情况 涉及多个文件修改，主要实现商品更新任务的状态统计和分页查询功能
- *(AE精选商品)* 实现批量导入导出功能 • 新增AE精选商品导入DTO和导出DTO • 实现Excel批量导入功能及数据校验 • 添加商品数据和更新任务的导出组件 涉及主要变更： 1. 新增AeSelectedProductImportDto处理导入数据 2. 修改AeUpdateTaskStatusEnum字段名 3. 实现AeSelectedProductServiceImpl中的导入导出逻辑 4. 新增两个导出组件类 5. 在DownloadTaskTypeEnum中添加新的导出类型
- *(selected-product)* 添加平台品类到POP品类的映射功能 • 在AeSelectedProductServiceImpl和AePullProductDataServiceImpl中新增品类映射逻辑 • 实现通过平台分类ID获取POP分类信息的私有方法getCategoryByPlatformCategoryId • 在商品分页和拉取数据时自动填充POP品类ID和名称 涉及平台：Aliexpress 涉及渠道：Alibaba
- *(AE商品操作)* 实现商品操作记录查询功能 • 新增根据tryOnTaskId查询最近成功任务的repository方法 • 扩展AeUpdatePlatformTask实体类，添加requestImageUrls字段 • 完善操作记录查询逻辑，关联TryOn任务、QC记录和平台更新任务 涉及数据库变更：新增ae_update_platform_task表的request_image_urls字段
- *(selected-product)* 为AE选品添加市场code字段 • 在DDL.sql和AeSelectedProductSpu实体中新增market_code字段 • 在AeSelectedProductServiceImpl中集成MarketStyleComponent处理市场字典 • 更新数据同步逻辑以同时处理market_name和market_code字段 涉及字典Market_Style第一层数据的映射处理
- *(ae-product)* 添加ComfyUI任务集成到AE产品识别流程 • 在AePullProductDataServiceImpl中注入ComfyuiTaskClientExternal依赖 • 实现识别任务创建后自动发起ComfyUI工作流 • 添加错误处理日志记录ComfyUI任务创建失败情况 修改主要涉及识别任务流程的扩展，通过ComfyuiTaskClientExternal发起710类型工作流任务，并将任务ID和参数保存到数据库
- *(产品服务)* 更新Nacos版本并优化ComfyUI任务处理 • 将Nacos发现元数据版本更新为20250721 • 移除AePullProductDataServiceImpl中的注释代码并优化任务发起逻辑 • 新增ComfyuiTaskClientExternal测试类验证任务处理功能 测试包含ComfyUI任务创建和查询功能验证
- *(recognition-task)* 添加识别状态字段和相关功能 • 在RecognitionTask实体和DDL中新增recognitionStatus字段 • 创建RecognitionStatusEnum枚举定义状态值 • 实现pullRecognition方法定时拉取识别结果并更新状态 涉及数据库变更和状态管理逻辑
- *(selected-product)* 更新选品数据同步逻辑 • 在AePullProductDataServiceImpl中添加选品表结果同步逻辑 • 当试穿图片URL有效时更新selected表中对应记录 • 仅更新productImage为空且productId匹配的记录 该修改确保试穿图片结果能及时同步到选品表，保持数据一致性
- *(selected)* 新增更新平台接口 - 在 AeSelectedProductService 中添加了新的 updatePlatform 方法 - 实现了将 TryOn 任务的质检结果更新到 AE 平台的功能 - 新增了图片处理逻辑，确保更新的图片数量符合要求 - 添加了必要的异常处理和事务管理
- *(tryon)* 优化tryOn任务参数结构及质检逻辑 - 重构TryOnHelper参数映射逻辑，移除冗余字段 - 在TryOnTaskQcDetailResp中新增categoryId字段 - 将postureFission改为postureFissionList支持多姿势 - 更新versions.properties版本号至20250721 - 新增TryOnTaskServiceTest测试类
- *(tryon)* 实现按规则保存tryOn任务功能 新增TryOnTaskRuleEnum枚举类定义tryOn规则 修改TaskParamRuleDto增加规则配置明细 完善TryOnTaskServiceImpl.saveTryOnTaskByRule实现 调整相关请求参数字段命名及类型
- *(tryon)* 新增质检通过事件处理逻辑 - 新增TryOnTaskQcPassEvent事件类 - 在TryOnListener中添加质检通过事件处理 - 修改TryOnTaskQcServiceImpl触发质检通过事件 - 更新TryOnTaskServiceImpl中tryOn任务保存逻辑
- *(selected-product)* 为AE平台更新方法添加选图参数 • 在updatePlatform方法中新增tryOnTaskSelectImage参数 • 将选中的图片更新到productImage字段 • 同步修改接口和实现类的参数列表 涉及文件： - AeSelectedProductService.kt - AeSelectedProductServiceImpl.kt
- *(AE商品服务)* 实现AE平台商品更新功能 • 在AeSelectedProductServiceImpl中添加更新商品到AE平台的逻辑 • 新增AliexpressService接口方法用于编辑商品单个字段 • 创建AeEditFiedEnum枚举定义AE平台可编辑字段类型 该提交实现了将精选商品更新到AE平台的功能，包括商品图片更新等操作。新增了AE平台字段编辑枚举类型，为后续字段更新功能提供支持。
- *(AE商品服务)* 完善AE商品图片更新逻辑 • 修改AeSelectedProductServiceImpl中的TODO注释，明确更新任务处理流程 • 在AliexpressServiceImpl中添加editSimpleProductFiled方法实现单个字段编辑功能 • 准备支持AE商品图片URL更新操作 涉及AE平台商品图片更新流程优化
- *(商品识别)* 优化AI任务状态处理及商品状态查询 • 新增AI任务状态枚举类并完善识别任务处理逻辑 • 修改商品状态查询为多选模式并更新相关SQL映射 • 添加识别失败原因字段及错误信息记录 涉及主要变更： - 识别任务状态处理适配新枚举 - 商品状态查询支持多状态筛选 - 识别任务增加错误信息记录
- *(数据库)* 在DDL表中添加识别失败原因字段 • 在DDL.sql文件中新增recognition_error_msg字段 • 该字段用于记录识别失败的具体原因 • 字段类型为text，允许为空 涉及文件修改: docs/2025/0731/DDL.sql
- *(商品筛选)* 支持多状态筛选并新增商品风格字段 • 将单状态字段改为列表形式(tryOnStatus, qcStatus, updateStatus) • 新增styleCode字段支持商品风格筛选 • 将marketName改为marketCode并使用IN查询优化多值筛选 • 修改resultImages字段为List<String>类型并添加转换方法 涉及文件：AeSelectedProductPageReq.kt、AeSelectedProductSpuMapper.xml、AeSelectedProductPageResp.kt
- *(selected-product)* 添加商品标签显示功能 • 在商品详情页添加品牌、主推款、市场、风格和TryOn规则的标签显示 • 修改AeSelectedProductPageResp以支持商品标签列表字段 • 更新SQL映射文件以包含风格相关字段 涉及文件：AeSelectedProductServiceImpl.kt、AeSelectedProductSpuMapper.xml、AeSelectedProductPageResp.kt
- *(shop)* 添加平台枚举支持店铺列表查询 • 在ShopListReq中新增platform字段用于平台枚举类型筛选 • 修改ShopController逻辑以支持通过platformId筛选店铺 • 保持向后兼容性，原有platformId参数仍可用 涉及文件：ShopController.kt和ShopListReq.kt的修改
- *(selected-product)* 优化TryOn规则处理并重构导入功能 • 将try_on_rule拆分为code和name字段，增强数据规范性 • 重构导入方法并更名为importUpdate，增加字典校验逻辑 • 更新相关实体类、DTO和SQL定义以支持新字段 涉及控制器、服务、实体、DTO及SQL脚本的多处修改，确保前后端数据一致性
- *(selected-product)* 支持多品类筛选并优化统计字段命名 • 将品类ID从Long类型改为List<Long>以支持多品类筛选 • 更新SQL查询逻辑以支持IN条件查询 • 重命名统计响应字段使其更语义化(totalCount/activeCount等) 涉及文件：请求参数、服务实现、Mapper及响应对象
- *(精选商品)* 添加品类code支持及相关枚举 • 在请求、响应、数据库和mapper中新增品类code字段 • 添加TryOn任务状态枚举类用于页面筛选 • 更新SQL脚本添加pop_category_code字段 涉及多个组件修改以支持品类code的查询和展示
- *(tryon)* 重命名AI完成时间为完成时间并优化任务处理逻辑 - 将aiFinishTime统一重命名为finishTime - 增加tryOn任务推送日志 - 移除TryOnListener的异步注解 - 添加任务状态检查逻辑 - 优化TryOnTaskRepository的更新操作
- *(tryon)* 优化TryOn任务状态同步逻辑及数据库结构 - 新增任务超时处理及失败原因记录 - 修改TryOnTask和TryOnTaskSub实体类及对应DDL - 完善任务状态同步逻辑，增加超时判断 - 添加同步AI任务详情的测试用例
- *(产品服务)* 新增市场任务客户端功能并优化测试 • 添加MarketTaskClientExternal类实现品类和风格识别任务接口 • 修复AePullProductDataServiceImpl中店铺过滤逻辑 • 重命名并扩展ComfyuiTaskClientExternalTest为AigcClientExternalTest 涉及品类/风格识别的批量创建、单条查询和详情获取功能
- *(selected-product)* 完善AE精选商品操作响应数据 • 将productImageUrl改为productImageUrls并解析为List<String> • 新增tryOnTaskId和updateTaskId字段记录任务ID • 使用tryOnSelectImageUrl替换tryOnSrcImageUrl，直接从任务参数获取选中图片 修改涉及AeSelectedProductServiceImpl和AeSelectedOperationResp两个文件，主要优化了商品图片和试穿任务相关数据的处理和返回格式
- *(tryon)* 优化AI任务状态同步逻辑及日志记录 1. TryOnHelper添加aigc-server接口响应日志 2. TryOnTaskServiceImpl重构状态更新逻辑，使用新对象避免脏数据 3. 修复子任务批量更新条件判断错误
- *(tryon)* 新增任务状态更新接口及优化QC逻辑 1. 在TryOnTaskService添加updateTaskState接口 2. 实现带事务的批量状态更新逻辑 3. 优化QC结果图片解析处理 4. 新增任务创建时默认QC状态 5. 补充相关单元测试
- *(商品识别)* 新增品类和风格识别功能 • 添加识别品类和风格的任务表及相关服务 • 修改商品状态字段类型为int • 扩展商品表字段以支持品类和风格信息 涉及数据库变更和识别任务处理逻辑扩展
- *(selected-product)* 支持批量更新AE平台商品 • 将AeSelectedUpdatePlatformReq中的aeProductId改为aeProductIds以支持批量操作 • 修改AeSelectedProductServiceImpl.updatePlatform方法处理商品列表 • 更新相关业务逻辑确保批量操作时正确处理每个商品 注意：此修改涉及AE平台商品更新接口的请求参数变更
- *(ae-product)* 优化AE商品更新任务处理 • 修改AeSelectedProductSpu表结构，将product_id设为主键并移除id字段 • 在AeUpdatePlatformTaskRepository中添加获取最大版本号方法 • 实现AE图片更新任务处理逻辑，包括任务版本控制和图片上传 涉及数据库表结构调整和任务处理逻辑优化
- *(tryon)* 优化质检任务处理逻辑及数据结构 - 将TryOnTaskQcRecordDto.qcRecord改为List类型 - 合并TryOnTaskQcPassEvent和TryOnTaskQcEvent - 增加质检记录中的taskImageId字段 - 优化质检结果处理流程，统一通过TryOnTaskQcEvent处理 - 添加质检时间和质检人信息记录
- *(ae-product)* 添加任务ID字段到产品查询响应 • 在AeSelectedProductPageResp.kt中新增updateTaskId、tryOnTaskId和qcTaskId字段 • 修改AeSelectedProductSpuMapper.xml查询SQL，添加对应任务ID字段 • 保持原有查询逻辑不变，仅扩展返回字段 这些变更支持前端更精确地追踪产品关联任务
- *(ae-product)* 添加AE商品平台更新功能 • 实现updatePlatform方法用于更新AE平台商品信息 • 添加JobAeController接口触发定时更新任务 • 处理商品图片更新及状态管理逻辑 涉及主要修改： - 新增平台更新状态枚举AeUpdateTaskStatusEnum - 集成aliexpressService编辑商品字段功能 - 添加异常处理和日志记录
- *(dao)* 修复AeSelectedProductSpuRepository图片映射逻辑 修改了AeSelectedProductSpuRepository.kt中图片URL映射逻辑，将处理结果正确赋值给productImages字段
- *(tryon)* 优化tryOn任务市场编码和款式编码逻辑
- *(ae-product)* 为商品SPU添加版本号字段 • 在ae_selected_product_spu表中新增version_num字段，默认值为1 • 更新对应的Kotlin实体类AeSelectedProductSpu • 修改产品服务和数据拉取服务中的版本号处理逻辑 当版本水印变更时自动递增版本号，并在产品操作响应中使用V前缀格式
- *(商品筛选)* 优化AE精选商品查询功能 • 将aeProductId改为支持多选的aeProductIdList • 重构TryOn状态筛选逻辑，支持待创建/创建中/已创建等状态转换 • 修复XML中集合判空条件，统一使用size>0 涉及请求参数、仓库逻辑及SQL映射文件的协同修改
- *(resp/tryon)* 在PrepareTryOnTaskResp中新增编码字段 新增styleCode、brandCode、marketCode和tryOnRuleCode字段，同时将tryOnRule重命名为tryOnRuleName
- *(tryon)* 新增AE精选商品与tryOn任务关联功能 在TryOnTaskServiceImpl中新增updateAeSelectedProductTryOnTaskId方法，用于更新AE精选商品对应的tryOn任务ID。同时修改了批量创建tryOn任务和重新执行tryOn任务的逻辑，确保AE精选商品能正确关联最新tryOn任务。测试类TryOnTaskServiceTest新增reDoTryOnTask测试方法。
- *(tryon)* 移除质检任务自动跳转功能 移除提交质检结果后自动返回下一个待质检任务的功能及相关代码，简化质检流程
- *(pop-product-service)* 补充 AE 商品品类映射 - 新增 fixCategory 方法定时补充未映射品类的 AE 商品 - 在 AeSelectedProductSpuMapper 中添加 listByDistinctNullCategory 方法 - 在 PublishCategoryMappingRepository 中添加 listByPlatformCategoryIds 方法 - 在 JobAeController 中添加 fixCategory 接口
- *(tryon)* 添加操作人信息至试穿任务事件及修改参考图标识字段 - 在CreateTryOnTaskEvent和TryOnTaskQcEvent中新增operatorId和operatorName字段 - 将TryOnTaskProductSnapshotDto中的isTryOnReferImage字段重命名为tryOnReferImageFlag - 在TryOnListener中添加操作人上下文处理逻辑
- *(tryon)* 为tryOn任务响应添加品类编码和名称 在PrepareTryOnTaskResp中新增popCategoryCode和popCategoryName字段，并在TryOnTaskServiceImpl中实现品类信息的拼接逻辑
- *(tryon)* 修改tryOn参考图获取方式为getTryOnReferImage方法 refactor(selected): 统一使用getTryOnReferImage方法获取tryOn参考图
- *(商品服务)* 更新商品风格识别功能及版本配置 • 将商品风格查询从单字符串改为列表形式 • 优化风格识别任务处理逻辑，增加事务管理 • 更新Nacos服务发现版本至20250724 涉及主要修改： - 商品请求参数和SQL查询支持风格列表 - 完善风格识别结果处理流程 - 调整测试用例和版本配置
- *(aliexpress)* 添加AE图片上传压缩与格式转换功能 • 在ProductPublishAeHelper中新增buildAliExpressAddImage方法，实现图片下载、压缩和PNG转JPG功能 • 在ImageUtils中添加convertPngToJpg方法用于图片格式转换 • 修改测试用例和相关服务调用逻辑以适配新功能 涉及文件修改：ProductPublishAeHelper.kt、ImageUtils.kt、AliexpressServiceTest.kt、AePullProductDataServiceImpl.kt
- *(tryon)* 新增手动推送tryOn任务接口并优化推送逻辑 - 在TestInnerController新增手动推送tryOn任务接口 - TryOnListener启用异步处理并简化用户上下文设置 - TryOnTaskServiceImpl完善推送任务的用户上下文处理 - 更新服务版本号至20250725
- *(pop-product-service)* 新增单个商品数据拉取功能 - 在 AePullProductDataService 接口中添加了 pullSelectedSingle 方法 - 实现了单个商品数据拉取的逻辑，支持指定商品 ID 和店铺 ID - 新增了 AePullSelectedSingleReq 数据类用于请求参数 - 在 JobAeController 中添加了 pullSelectedSingle接口 - 优化了批量拉取商品数据的逻辑，提取公共部分到 pullAeDetail 方法
- *(tryon)* 添加根据二级品类确定换装类型功能 在PrepareTryOnTaskResp中新增outfitType字段，TryOnTaskServiceImpl中实现通过二级品类代码自动判断换装类型逻辑，默认值为10(上装)
- *(tryon)* 新增AI参考图规则编码字段 在SaveTryOnTaskByConfigReq和TryOnHelper中添加tryonRuleCode字段，用于支持AI参考图规则编码功能
- *(pop-product-service)* 添加 1688 ID 字段并调整相关数据结构 - 在 AeSelectedProductExportDto 和 AeSelectedProductImportDto 中添加 1688 ID 字段 - 调整 AeSelectedProductImportDto 中其他字段的索引位置 -移除 AeSelectedProductServiceImpl 中的冗余代码 - 更新版本号至 20250731
- *(ShopMapper)* 添加外部属性 在 ShopMapper.xml 文件中添加了 s.external 字段映射，以支持外部属性。
- *(selected-product)* 添加 1688 ID 相关功能- 在 AeSelectedProductSpu模型中添加 alibabaId 字段 - 在 AeSelectedProductPageResp 和 AeSelectedProductExportDto 中添加 alibabaId 相关信息 - 在 AeSelectedProductServiceImpl 中实现 alibabaId 的更新逻辑- 在 AeSelectedProductSpuMapper.xml 中添加 alibabaId 的查询 - 在 DDL.sql 中添加 alibaba_id 列的创建语句

### 🐛 Bug Fixes

- *(product-service)* 更新Nacos版本并优化AE商品详情查询 • 将Nacos服务发现的元数据版本从20250729更新至20250730 • 在ProductManageServiceImpl中添加对空店铺ID的检查逻辑 • 优化AE商品详情查询时的店铺数据获取流程 注：本次修改主要解决AE商品详情查询时可能出现的空指针异常问题
- *(product)* 优化 ETA 更新逻辑 - 添加错误信息记录，当 Lazada 或 AE 更新失败时，将错误信息添加到 errorList- 在 SpuItemStatusUpdateCallBackReq.SpuInfo 中添加 errorMsg 字段，用于存储错误信息 - 优化代码格式和缩进
- *(regionpricerule)* 修复AE配置分组逻辑，增加shippingPlaceId字段
- *(config)* 修改marketDefaultShop类型为Array<MarketDefaultShopConfig>
- *(regionpricerule)* 修正Lazada/Temu运费率配置的店铺与国家字段位置 调整运费率配置DTO、Req、Resp中shopId与country字段的位置，保持数据结构一致。更新版本号至20250801。
- *(regionpricerule)* 修复市场默认店铺查询逻辑 修改RegionPriceRuleServiceImpl中市场默认店铺的查询条件，增加空值判断并使用正确的平台ID获取方式
- *(pricing)* 使用CountryEnum统一管理国家代码 1. 在ProductPublishAeExportTaskStrategy中替换硬编码"US"为CountryEnum.US.code 2. 修改AEPricingService中获取目的地列表逻辑，支持从PricingVariableService动态获取 3. 在EntityExtensions和PriceGenerateExcelService中替换硬编码"US"为CountryEnum.US.code
- *(docs)* 移除DDL.sql中enable_status字段
- *(docs/DDL.sql)* 修正表名从ae_selected_product_image改为recognition_task
- *(docs/DDL.sql)* 修正表名并调整字段顺序 修改ae_platform_product_spu为ae_selected_product_spu，调整try_on_rule和try_on_task_id字段位置
- *(docs)* 修正DDL.sql中结果图字段的注释说明
- *(product-data)* 调整版本水印字段长度并修复图片URL格式 • 将DDL.sql中的version_watermark字段从VARCHAR(50)扩展到VARCHAR(300) • 修复AePullProductDataServiceImpl中productImageUrls的分隔符从分号改为逗号 • 启用AliexpressServiceTest测试类用于开发调试 涉及数据库字段变更和数据处理逻辑调整
- *(AeSelectedProductSpuMapper)* 修正创建时间查询参数名错误 • 将req.createdStartTime统一修改为req.createdTimeStart以保持命名一致性 • 修复两处SQL条件中的参数名错误 • 确保时间范围查询功能正常工作 涉及产品列表查询功能的时间筛选条件修复
- *(controller)* 修改TryOnTaskQcController获取质检详情接口路径参数 将@RequestBody改为@PathVariable以正确接收路径参数
- *(selected-product)* 修复AE选品更新任务创建逻辑 • 添加TryOn任务质检结果检查，确保任务通过且存在结果图片 • 调整任务创建顺序，先验证再创建新任务 • 优化版本号获取逻辑，确保版本递增正确 涉及关键修改：新增TryOn任务状态检查，重构任务创建流程
- *(tryon)* 添加商品品类非空校验并修复品类编码生成逻辑 在TryOnTaskServiceImpl中添加商品品类非空断言校验，并修正splicingCategoryCode参数传递错误。同时启用TryOnTaskServiceTest测试类。
- *(tryon)* 修正任务状态检查逻辑条件错误
- *(tryon)* 优化TryOnTaskServiceImpl中的forEach循环结构
- *(tryon)* 修复品类名称格式问题
- *(dao)* 移除TryOnTaskSub中ai_created_time字段及相关SQL
- *(selected-product)* 修复平台品类映射逻辑 • 优先使用resp.categoryId查找品类，不存在时再通过平台品类ID查询 • 补充设置品类编码字段categoryCode • 优化品类名称拼接逻辑 修改原因：原逻辑仅通过平台品类ID查询映射关系，无法处理已存在品类ID的情况
- *(AeSelectedProductServiceImpl)* 修正试穿参考图片字段名 将isTryOnReferImage改为tryOnReferImageFlag以保持字段命名一致性
- *(ae-product-update)* 修复AE平台更新任务并发问题 • 为updatePlatform方法添加事务注解确保原子性 • 调整任务版本号生成逻辑，处理null值情况 • 将待处理任务取消操作移到任务创建前执行 测试用例中添加系统用户上下文模拟
- *(selected-product)* 修复AE选品更新任务与商品表同步问题 • 将AE更新任务保存操作拆分为独立步骤，获取返回对象 • 新增更新selected商品表逻辑，关联任务ID并标记平台未更新状态 • 保持原有版本号递增逻辑不变 该修改确保更新任务与商品数据状态保持同步，避免后续处理中出现数据不一致情况
- *(Ae产品服务)* 优化AE商品图片更新逻辑及事务处理 • 修改图片源字段从productImage到resultImages并添加空校验 • 在AePullProductDataServiceImpl中引入手动事务管理 • 成功更新后同步修改selected商品表的平台更新状态 涉及主要变更： - 调整图片处理逻辑顺序并增强健壮性 - 使用TransactionTemplate确保操作原子性 - 完善成功/失败状态更新机制
- *(resp/tryon)* 修正品类编码字段注释描述
- *(selected)* 修正tryOn选择图片URL获取逻辑
- *(tryon)* 优化品类名称拼接逻辑 修改TryOnTaskQcServiceImpl中的品类名称获取方式，使用PlatformCategoryHelper拼接完整品类名称
- *(ae-product-recognition)* 修复风格识别失败时的异常处理 • 在风格字典匹配时增加空值检查，避免空字符串导致的问题 • 统一风格识别失败的错误处理逻辑，明确错误信息 • 更新错误提示信息，包含taskId以便追踪问题 修改内容主要涉及风格识别失败时的异常处理优化，确保系统能正确捕获并报告识别失败的情况。
- *(selected-product)* 简化风格名称检查逻辑 • 移除对styleName.isNotBlank()的冗余检查 • 仅保留styleName与resultStyle的差异比较 • 优化风格更新条件判断 修改动机：原逻辑存在不必要的检查，简化后代码更清晰且功能不变
- *(AeSelectedProductSpuMapper)* 修正更新平台任务关联条件 • 将关联条件从`tot.task_id`改为`ps.update_task_id` • 修复两处相同的SQL查询逻辑 • 确保与业务逻辑中的任务关联方式一致 涉及文件：pop-product-service/src/main/resources/mapper/AeSelectedProductSpuMapper.xml
- *(AeSelectedProduct)* 修复更新操作和SQL查询问题 • 在AeSelectedProductServiceImpl和AePullProductDataServiceImpl中添加缺失的update()调用 • 修正AeSelectedProductSpuMapper.xml中的SQL查询条件错误 • 移除接口和实现类中过时的参数注释 修复了数据库更新操作未执行的问题，并修正了查询条件和参数引用错误
- *(aliexpress-service)* 修复图片上传逻辑并优化域名检查 • 在测试和主逻辑中增加对ae01.alicdn.com域名的检查 • 修复未返回上传后图片URL的问题 • 统一测试和生产环境的图片处理逻辑 这些修改确保了对阿里系域名的完整识别，并修复了可能导致图片上传后丢失URL的问题
- *(aliexpress-service)* 品类code
- *(tryon)* 添加试穿任务初始化时的质检结果默认值
- *(selected-product)* 修正产品图片URL来源错误 • 将产品图片URL的来源从selectedProduct改为productSnapshot • 确保图片URL与产品快照版本保持一致 • 修复因图片URL不一致导致的显示问题 该修改解决了产品快照与选中产品图片URL不同步的问题
- *(selected-product)* 优化产品图片URL处理逻辑 • 修改产品图片URL来源，从直接使用aeImageUrls改为从productImages映射获取 • 新增tryOnSelectImageUrl字段，直接从productImages中筛选标记为试穿参考的图片 • 移除从tryOn.taskParam解析试穿图片的逻辑，使用更可靠的数据源 这些改动提高了图片数据处理的准确性和一致性
- *(selected)* 修复审核时间与更新任务完成时间 - 在 AePullProductDataServiceImpl 中添加更新任务完成时间 - 在 AeSelectedProductServiceImpl 中更新审核用户名字段
- *(req/tryon)* 将质检结果的NotEmpty校验改为NotNull校验
- *(pop-product-service)* 修复更新任务统计返回参数 - 将 AeSelectedUpdateProgressCountResp 中的 updateStatus 字段类型从 AeUpdateTaskStatusEnum 改为 Int- 在 AeSelectedProductServiceImpl 中相应地修改了返回值
- *(tryon)* 处理创建超时的AI试穿任务 添加对48小时后仍处于创建状态的tryOn任务处理，将其标记为失败

### 💼 Other

- 更新依赖版本 -将 aigc-server-sdk 版本从 0.0.3-SNAPSHOT 升级到0.0.3 -将 butted-sdk 版本从 3.3.1-SNAPSHOT 升级到 3.3.1
- 升级 butted-sdk 版本至 3.3.2 - 在 gradle/libs.versions.toml 文件中更新了 butted-sdk 的版本 -从 3.3.1版本升级到 3.3.2 版本

### 🚜 Refactor

- *(pop-product-common)* 优化代码格式和结构 - 移除了 CreateProductCallbackDto.kt 中未使用的导入 - 优化了 ProductPublishStateEnum.kt 中的列表返回格式
- *(product)* 重构产品管理服务中的店铺提取逻辑 -将店铺提取逻辑从 SKU 列表改为直接从 shopRepository 获取- 更新店铺循环遍历方式，使用 shop 对象替代 shopId- 优化代码结构，提高可读性和维护性
- *(regionpricerule)* 删除废弃接口并重命名服务类 - 删除多个RegionPriceRule*Service接口文件 - 将RegionPriceRuleService重命名为RegionPriceRuleManageService - 更新Controller和Test中相关引用
- *(product)* 重构商品管理服务 - 移除了未使用的导入语句 - 优化了代码格式和缩进 - 删除了冗余的空行 - 统一了日期格式字符串的引号使用 - 移除了未使用的函数 checkPlanAuditPassState
- *(entity)* 将ProductSkc类改为data class并优化结构
- *(database)* 拆分商品风格字段为code和name • 将style_name字段拆分为style_code和style_name • 保持原有注释语义，明确code和name的用途 • 不影响其他字段结构和功能 涉及表结构变更，需同步更新相关业务逻辑
- *(pop-product-service)* 优化测试配置和数据结构 • 将测试环境配置从qa-ola改为dev-ola • 重构AeSelectedProductPageResp为类形式并初始化字段 • 移除AeSelectedProductSpuMapper中多余的deleted条件检查 调整gradle内存配置至2048m以提升构建性能
- *(product-service)* 优化AE商品选择相关DTO和服务实现 • 将AeSelectedProductCountDto从data class改为普通class，直接使用Int类型表示商品状态 • 修改AeSelectedProductServiceImpl中状态过滤逻辑，使用枚举的code值进行比较 • 统一resultImages字段命名，修复映射字段resultImagesStr的拼写问题 涉及组件：商品状态统计DTO、商品服务实现层、MyBatis映射文件
- *(AeSelectedProduct)* 统一字段命名并迁移Excel库 • 将isMainPush和isLatest字段重命名为mainPush和latest以保持命名一致性 • 从Alibaba EasyExcel迁移到EasyPoi库 • 更新相关服务层逻辑以适应字段变更 涉及DTO、导出组件及服务层实现修改
- *(selected-product)* 修改图片URL处理方式 • 将aeImageUrls从JSON解析改为字符串分割处理 • 统一所有图片URL处理逻辑为split(",")方法 • 修复返回空列表的问题，改为返回respList 优化图片处理逻辑，简化代码并提高可维护性
- *(pop-product-service)* 移除识别品类功能 - 删除了 AePullProductDataService 中的 pullRecognitionCategory 方法 - 删除了 AePullProductDataServiceImpl 中的 pullRecognitionCategory 和 createCategoryTask 方法 - 删除了 RecognitionCategoryTask 相关的数据库表结构 - 删除了 RecognitionCategoryTask 相关的实体类、Mapper 和 Repository
- *(product)* 更新产品版本号处理逻辑 - 将产品版本号从 versionWatermark 改为 versionNum - 在 AeSelectedProductSpuMapper.xml 中添加 version_num 字段 - 在 TryOnTaskProductSnapshotDto 中添加 versionNum属性
- *(export)* 统一AE精品商品导出任务名称格式 • 将"导出-AE精选商品-更新任务"改为"AE精品商品导出更新结果" • 将"导出-AE精选商品-商品数据"改为"AE精品商品导出商品数据" • 保持原有功能逻辑不变，仅优化任务名称可读性
- *(AePullProductDataServiceImpl)* 优化AE商品数据拉取逻辑 • 添加对DELETED和SERVICE_DELETE状态的过滤处理 • 在日志中添加商品状态类型信息以便调试 • 保持原有数据拉取和存储逻辑不变 本次修改主要优化了商品状态处理流程，避免拉取已删除商品数据
- *(aliexpress-service)* 重构商品字段编辑功能 • 修改editSimpleProductFiled方法参数为请求对象形式 • 在AePullProductDataServiceImpl中添加请求参数记录 • 清理测试代码中的注释内容 涉及主要修改： - 接口参数结构调整 - 增加请求/响应参数记录 - 测试代码清理
- *(ae-product)* 重构AE平台图片更新逻辑 • 移除AeSelectedProductServiceImpl中直接处理图片上传的逻辑 • 将图片上传功能移至AePullProductDataServiceImpl统一处理 • 优化错误处理和日志记录机制 涉及主要变更： - 删除不必要的ProductPublishAeHelper依赖 - 在数据拉取服务中集中处理图片上传 - 完善异常信息捕获和任务状态更新
- *(ae-product)* 简化平台计数更新逻辑 • 将AeUpdatePlatformCountDto中的updateState类型从枚举改为Int • 移除setUpdateState方法及相关枚举转换逻辑 • 在AeSelectedProductServiceImpl中重构进度统计逻辑，直接使用枚举映射 这些修改简化了DTO结构并优化了进度统计查询的性能
- *(pop-product-service)* 更新商品审核状态 - 在更新 selected 商品表时，添加商品状态为审核中的逻辑
- *(pop-product-service)* 重构查询 AeSelectedProduct 实体及相关任务的逻辑 - 优化了获取 TryOn 任务和相关 QC任务的逻辑 - 使用 TryOnTaskQcResultEnum 替代硬编码的 QC 结果值- 修复了查询历史版本 TryOn 任务时的逻辑问题- 优化了 SQL 查询中的表别名使用 - 使用 AeUpdateTaskStatusEnum 替代硬编码的任务状态值
- *(pop-product-service)* 更新市场风格数据处理逻辑 - 在 AliexpressServiceTest 中添加 style 函数，用于更新选中产品的风格信息 - 在 MarketStyleComponent 中优化 getMarketNodeMap 函数，增加对节点状态的过滤
- *(pop-product-service)* 优化代码结构和可读性 - 删除未使用的导入语句 - 替换 null 安全调用和 Elvis 操作符 - 使用更简洁的 Kotlin 语法 (如关联 by key 而不是 value) - 移除冗余的空行和注释 - 统一使用 Kotlin 标准库函数替代自定义实现
- *(pop-product-service)* 优化代码结构和样式 - 移除不必要的空格和分号- 使用标准的 Kotlin 代码格式 - 删除未使用的变量和导入 - 优化部分代码逻辑，提高可读性
- *(pop-product-service)* 优化产品更新逻辑 - 修改 AePullProductDataServiceImpl 中的产品更新逻辑- 当遇到产品审核中状态时，将任务状态设为等待下次更新 - 优化了错误处理和日志记录 - 更新了相关测试用例
- *(pop-product-service)* 优化代码结构和导入 - 移除了多个文件中未使用的导入语句 - 更新了部分导入语句，使其更加简洁 - 删除了冗余的空行和注释 - 统一了代码格式
- *(selected-product)* 移除tryOn任务相关逻辑并更新版本号 • 注释掉AeSelectedProductServiceImpl中tryOn任务、QC任务和更新任务的获取逻辑 • 修改版本水印检查条件，仅保留空值判断 • 更新nacos服务发现元数据版本至20250728 涉及主要变更：tryOn相关功能暂时下线，后续可能重构
- *(pop-product-service)* 重构 AeSelectedProductServiceImpl - 移除了未使用的 AliexpressService 依赖 - 优化了 AeSelectedOperationResp 对象的构建逻辑 - 调整了 TryOn任务和 QC 任务的处理方式 - 修复了一些潜在的空指针异常问题
- *(pop-product-service)* 移除 AeSelectedProductServiceImpl 中未使用的 RecognitionTaskRepository - 删除了 AeSelectedProductServiceImpl 类中不再使用的 RecognitionTaskRepository 字段 - 优化了类的结构，提高了代码的可维护性
- *(pop-product-service)* 优化 AePullProductDataServiceImpl 中的任务状态更新逻辑 - 移除了多余的更新任务状态操作 -调整了任务状态更新的时机和条件 - 优化了异常处理逻辑，使用 apply 方法简化对象初始化
- *(export)* 优化 AeSelectedUpdateTaskExportComponent 中的查询逻辑 - 使用 QueryWrapper 替代 ktQuery() 方法，以解决查询语句中的 table alias 问题 - 通过 Java 对象引用列名，提高代码的编译时安全性
- *(pop-product-service)* 优化产品导入逻辑 - 重构品牌名称、市场名称、试穿规则等字段的更新逻辑 -增加对"0"值的特殊处理，用于清空对应

### 📚 Documentation

- 更新DDL.sql中商品图和结果图的注释说明

### 🧪 Testing

- *(RegionPriceRuleServiceTest)* 为测试用例添加shippingPlaceId字段
- *(pop)* 添加 ThirdCategoryAttrTest 测试类 - 新增 ThirdCategoryAttrTest 测试类，用于测试 AE 速卖通服务- 包含查询类别树列表的测试方法 prepareTestImage - 使用 SpringBootTest 注解启动 PopApplication 应用 - 注入 AliexpressService 和 ShopRepository 依赖
- *(AliexpressService)* 添加商品分页查询测试并更新配置 • 新增testProductPage测试方法用于验证AE商品分页查询功能 • 将测试环境配置从dev-ola改为qa-ola • 添加AliexpressPostproductRedefiningFindproductinfolistqueryAeopAEProductListQuery类导入 测试环境配置变更需要同步更新相关依赖
- *(aliexpress-service)* 更新商品列表测试并优化试穿助手 • 在AliexpressServiceTest中新增商品状态过滤参数 • 添加对商品列表响应数据的JSON解析处理 • 在TryOnHelper中注释掉虚拟试穿相关客户端调用 修改主要涉及测试用例完善和临时功能禁用，不包含破坏性变更
- *(aliexpress-service)* 优化Aliexpress商品查询测试用例 • 移除未使用的导入类以简化代码 • 调整商品列表查询结果的日志输出格式 • 新增商品详情查询及日志输出功能 修改主要涉及测试用例的日志输出优化和功能增强，便于调试时查看完整商品信息
- *(AE精选商品)* SDK变动, 暂时调整
- *(pop-product-service)* 补充 AE 商品状态类型枚举测试- 引入 AeProductStatusType 和 AeEditFiedEnum 枚举 - 遍历 AeProductStatusType 枚举，分别查询 AE 商品列表 -增加查询商品详情的逻辑- 测试编辑商品图片的功能
- *(TryOnTaskServiceTest)* 禁用测试类 • 在TryOnTaskServiceTest类上添加@Disabled注解 • 该修改暂时禁用测试用例执行 • 适用于需要跳过测试的场景 原因：临时禁用测试以进行其他开发或调试

### ⚙️ Miscellaneous Tasks

- *(gradle)* 更新框架版本至3.1.3
- *(gradle)* 更新框架版本至3.1.4
- *(gradle)* 更新项目版本至0.0.17-SNAPSHOT
- *(gradle)* 更新项目版本至0.0.17 正式版

## [0.0.16-3] - 2025-07-29

### 🐛 Bug Fixes

- *(ae-template)* 修复AE模板SKU店铺关联问题 • 在AeTemplateUpdateServiceImpl中添加店铺ID去重处理 • 修改ProductTemplateAeSkuRepository查询方法，增加shopId参数 • 在ProductManageServiceImpl中完善SKU循环逻辑，按店铺处理发货地 涉及多店铺场景下的SKU管理，确保不同店铺的SKU数据隔离
- *(AeSaleSku查询)* 在AeSaleSku查询中添加enableState过滤条件 • 在根据saleGoodsId和platformSkuId查询时增加enableState=YES的条件 • 优化fixEtaUpdate方法的空值判断格式 • 导入Bool枚举类用于状态判断 该修改确保只查询启用状态的SKU数据，同时保持代码风格统一

## [0.0.16-2] - 2025-07-29

### 🚀 Features

- *(region-price)* 新增区域定价规则相关实体及仓库 新增区域定价规则相关实体类、Mapper接口和Repository实现，包括广告成本、佣金、折扣率、物流支出比例、目标毛利率、物流费、营销费用、退货率、仓储成本、综合税率和提现手续费等规则配置。同时新增对应的DDL.sql文件。
- *(pop-product-service)* 新增 AE 待上架 SKU - 店铺商品价格表相关实体和仓库 - 在 AeSaleSku 实体中添加 nationalQuoteConfig 字段 - 新增 ProductTemplateAeSkuShopPrice 实体类- 新增 ProductTemplateAeSkuShopPriceRepository 仓库类
- *(regionpricerule)* 新增区域定价规则相关服务及实体 - 新增物流费、仓储成本、退货率等查询和保存接口 - 新增广告成本、佣金、提现手续费等规则配置 - 修改RegionPriceRuleWithdraw等实体类，增加businessType字段 - 更新DDL.sql文件，添加business_type字段
- *(product)* 新增 AE 区域报价功能 - 添加 AeNationalQuoteConfigDto 数据类用于处理区域报价信息 - 在 ProductManageDetailResp 和 ProductManageUpdateAeReq 中集成区域报价相关字段 - 实现产品管理中 AE 区域报价的展示和更新逻辑- 优化代码格式和命名，提高可读性
- *(product)* 添加 AE 平台国家区域报价配置功能 - 新增 AePlatformNationalQuoteConfigDto 类用于处理国家区域报价配置 - 在 AeUpdateProductComponent 中集成国家区域报价配置逻辑 - 在 AliexpressProductInfoRequest 中添加 aePlatformNationalQuoteConfigDtoList 字段用于传递配置数据
- *(产品服务)* 添加AE商品区域定价功能 • 在商品详情、更新请求和响应中新增区域定价配置字段 • 实现区域定价数据的JSON序列化与反序列化处理 • 在价格同步组件中添加区域定价处理占位逻辑 注意：需要后续完善区域定价的汇率转换逻辑
- *(商品同步)* 优化AE商品SKU同步逻辑 • 添加区域定价配置解析功能，支持多国家定价 • 移除冗余代码和注释，清理商品属性处理逻辑 • 更新版本号至20250729以部署新功能 涉及主要修改： - 新增AeNationalQuoteConfigDto和AePlatformNationalQuoteConfigDto用于处理区域定价 - 完善汇率转换逻辑，支持美元自动转人民币 - 优化SKU同步流程，增加国家定价配置处理
- *(product)* 将Temu商品英文标题校验逻辑移至请求类 在ProductManageUpdateTemuReq中新增validate方法集中处理校验逻辑，移除ProductManageServiceImpl中的校验代码

### 🐛 Bug Fixes

- *(pop-product-service)* 修复 AE 商品发布时的 SKU 问题- 过滤与店铺 ID 不匹配的 SKU - 添加国家报价配置字段

### 🚜 Refactor

- *(product-template)* 移除AE SKU店铺价格独立实体 • 删除ProductTemplateAeSkuShopPrice实体类及相关Repository • 将价格配置字段合并至ProductTemplateAeSku实体 • 在AeSaleSku中完善区域价格配置字段注释 涉及数据库表结构调整，需同步更新迁移脚本
- *(product)* 优化商品信息结构和分国家报价配置 - 调整代码格式和缩进，提高可读性 - 在 ProductInfoRequest 中添加 NationalQuoteConfig 类的注释，解释分国家报价配置的结构和类型- 在 AeUpdateProductComponent 中添加 nationalQuoteConfig 属性初始化代码（当前为注释状态）
- *(product)* 优化产品导出组件中的数据过滤逻辑 - 在 AeProductExportComponent、LazadaProductExportComponent 和 TemuProductExportComponent 中 - 优化了对模板数据的过滤逻辑，增加了对状态和可用性的判断 - 提高了代码的可读性和效率，避免了不必要的空检查

## [0.0.16-1] - 2025-07-23

### 🚀 Features

- *(AliexpressServiceHelper)* 新增失效所有类目属性缓存方法

### 🐛 Bug Fixes

- *(AliexpressServiceHelper)* 修改类目属性查询参数从accessToken到shopId 主要变更： - 将AliexpressServiceHelper.getCachedCategoryAttributes参数从accessToken改为shopId - 更新相关调用点使用shopId代替accessToken - 修改ProductAeCategoryAttributesQueryReq中的请求参数
- *(ProductSkuSyncComponent)* 简化Lazada商品同步逻辑，移除冗余代码

## [0.0.16] - 2025-07-17

### 🚀 Features

- 源码目录 #init
- Sql #init
- Easy config配置 #init
- 兼容java注解 #init
- 项目运行 #init
- Mybatis demo #init
- Easy code模板 #init
- 企划管理controller
- 灵感数据源和异常订单controller
- 迁移品类和属性
- 提交lazada品类sql和代码
- 补充Google相关依赖
- 优化
- 补充nacos配置和需要的依赖
- 调整sql
- 调整包名
- Java req和resp的getter setter方法改为原来的@Data
- *(image)* 图片管理，qc管理搬运过来，entity, mapper, Repository
- *(image)* 下载管理搬运过来，entity, mapper, Repository
- *(image)* 选款管理，entity, mapper, Repository
- *(image)* 平台上架商品编码规则，entity, mapper, Repository
- *(image)* 商品销售定价基础设置，entity, mapper, Repository
- *(image)* 商品定价规则，entity, mapper, Repository
- 企划管理
- Lazada相关配置类
- 引入文件服务File-manager
- 引入dom处理工具包，com.thoughtworks.xstream:xstream:1.4.20
- 搬运aigc fd 服务类，ImageRepositoryService
- 搬运aigc fd，ImageManagerController
- *(product)* 版型号基础类
- *(product)* 版型号基础类，编译报错
- *(settings)* 下载管理
- 异常订单导出;灵感导入导出
- 调整过时方法
- 调整接口url前缀,增加导入事务
- *(settings)* 下载管理, instanceId与me.ahoo.cosid.spring.boot.starter.machine.CosIdMachineAutoConfiguration.instanceId冲突
- *(settings)* 图片QC管理
- 增加任务枚举
- 注释SDK代码,等待SDK替换
- 异常订单创建逻辑
- 异常订单创建接口
- 移除toList()
- 灵感状态已提交
- *(settings)* SKU编码规则数据结构调整，改为渠道平台主键+名称
- 灵感创建
- *(settings)* SKU编码规则，列表查询接口，枚举
- *(settings)* SKU编码规则，新增，删除接口；实体类字段改为可空
- 波次编号管理
- 异常订单处理
- *(settings)* 基础货币汇率配置接口
- *(settings)* 公网货币汇率数据缓存起来作为参考汇率
- *(settings)* 公网货币汇率数据缓存起来作为参考汇率，调整名称前缀加上参考
- Zj仓库更换jv
- *(sdk)* 店铺信息，渠道信息，品牌列表
- 调整灵感源表
- 启动异常，InspirationInfo
- 销售定价下拉框接口
- 异常订单监听MQ
- 销售定价下拉框接口对应数据库字段改为String类型
- Test
- 实体类增加类头注释
- 产品基础价格计算实现
- 修改价格
- 修改价格 mq
- Update
- 移除过时UrlVersionConstant.VERSION_V1调用
- 产品定价规则查询
- 产品定价规则新增/更新实现
- 产品销售定价规则控制类
- 使用PageRespHelper
- 批量保存汇率配置接口
- ShopResp对象增加渠道ID，渠道名称
- 批量保存或更新汇率配置
- 更新商品定价规则相关控制类类名提交yapi
- 调整商品下拉框，返回枚举值封装的列表
- SKU编码生成服务
- *(settings)* 商品销售定价isp相关移除汇率选项，移除生效时间，失效时间字段
- *(settings)* 产品价格基础倍率
- *(settings)* 新增产品定价规则组表
- *(settings)* 保存或更新价格规则组
- *(settings)* 保存或更新价格规则，增加是否禁止上架
- *(settings)* 产品价格计算逻辑 将请求和响应类从 req/resp 目录移动到 bo 目录
- *(settings)* 产品价格计算服务接口
- *(settings)* 禁用启用使用枚举DisableEnum.ENABLED.code
- *(settings)* 获取所有Lazada店铺类型选项
- *(settings)* ProductPriceCalcBO内置Builder类
- *(settings)* - 修改 ProductPriceCalcBO 中 shopType 字段的注释，使用 cb/asc 替代自营商店/平台商店 - 移除 ProductPriceConditionVariableEnum 中的 SUPPLY_TYPE 枚举项
- *(settings)* 修改销售定价下拉框
- *(settings)* 销售定价服务实现
- *(settings)* 产品定价计算公式明细移除operandType，operandField已经知道公式的变量是固定值还是取其他字段了
- *(settings)* 生成SKU编码抛出指定异常
- *(settings)* 产品定价计算公式supplyType调整到ProductPriceRuleGroup中，商品销售定价时，按ProductPriceRuleGroupId遍历规则组
- *(settings)* 产品定价计算公式supplyType调整到ProductPriceRuleGroup中，从ProductPriceRule中移除
- *(settings)* 商品下架-查询skc，sku出参
- *(settings)* 移除部分注释
- *(settings)* 产品基础价格计算规则主表移除ruleName字段
- *(settings)* 批量保存或更新基础价格规则
- *(settings)* 批量保存或更新基础价格规则优化
- 企划-增加货盘
- *(settings)* 批量查询商品SKU列表服务实现
- *(settings)* 编译提示，@EqualsAndHashCode(callSuper = true)
- *(settings)* 批量查询规则增加缺少的出参；批量保存基础中间价格规则请求调整入参
- *(settings)* 处理规则组的保存或更新优化
- *(settings)* 处理规则组的保存或更新优化，批量保存
- *(product)* 下架商品sdk
- *(product)* SKU编码规则分页新增出参supplyMode
- *(product)* 下架商品，发送mq消息
- *(product)* 下架商品，消费mq
- *(product)* SKU编码生成服务优化
- 修改价格-单个
- *(product)* 增加品牌查询条件 - 在 ShopServiceImpl 中添加了对 brandId 的非空查询条件
- 修改价格-批量
- *(image)* 文件压缩工具
- 波次FeignClient
- *(image)* 实现图片异步下载功能
- *(pop-product-service)* 集成文件上传功能 - 添加文件上传相关依赖 - 实现文件上传DTO创建和处理逻辑 - 更新下载任务处理方式 -调整应用配置和版本号
- *(pop-product-service)* 图包下载，文件上传
- *(product)* 添加异步导出上架数据功能 - 新增导出上架数据的请求对象 ExportPublishProductReq - 在 ProductController 中添加提交导出任务的接口 - 实现 ProductPublishTaskExportService 接口，提供导出功能 - 新增 SkuImageExcelDTO 用于导出 SKU 图片信息 - 添加线程池配置和监控类，支持异步处理导出任务
- 企划汇总更新,清空其他tab所有数据
- 企划调整
- *(product)* 添加商品上架数据导出功能 - 新增 ImageDownloadHelper 类用于下载和压缩图片 - 实现 ProductPublishTaskExportServiceImpl 类中的导出逻辑 - 添加 PublishProductExportTypeEnum 枚举定义导出类型 - 在 SaleGoodsMapper 中新增 findFirstPublishedGoods 方法查询最早上架的商品 - 优化 SkuImageExcelDTO 数据结构，移除不必要的 URL 字段 - 新增 UploaderOssHelper 类封装文件上传逻辑
- *(product)* 添加 LOGO 供应模式并优化商品导出功能- 新增 LOGO 供应模式，用于处理特定类型的商品 - 优化商品导出功能，支持下载花型图片 - 重构代码，提高可维护性和可扩展性 - 更新数据库查询，提高查询效率
- *(pop-product)* 增加批量查询店铺功能 - 在 ShopReq 类中添加 shopNames 字段，用于批量查询店铺名称 - 在 ShopServiceImpl 类中添加对 shopNames 的查询条件
- 调整供给方式枚举字段,移除波次和选款相关表和代码
- Sql文档调整-移除选款,重新生成企划相关DDL
- 品类id改为code
- *(pop-product-service)* 集成参考汇率 API - 新增 DateTimeUtils 工具类，用于处理日期和时间 - 更新 ExchangeRateMarketResp 类，添加新字段以支持 API 响应 - 修改 OkHttpClientConfig，增加连接和读取超时时间 - 新增 RefExchangeRateProperties 类，配置 API 基础 URL - 更新 RefRefExchangeRateMarketServiceImpl 类，集成新 API 并使用 DateTimeUtils
- *(rate)* 手动触发同步所有币种汇率 - 新增手动同步所有币种汇率的接口和实现
- 企划-企划品类供给数统计接口(内部)
- 补充品类api
- 移除灵感相关代码,修复异常订单分页sql
- 修复导出
- 字段调整
- *(pop-product-service)* 新增 SkuCodeRule 相关功能- 在 SkuCodeRule 模型中添加 supplyModeName 字段 - 在 SkuCodeRulePageResp 和 SkuCodeRuleSaveReq 中添加 supplyModeName 字段- 实现 SkuCodeRule 的保存和更新逻辑 - 添加请求参数验证和规则路径构建功能- 优化删除操作，采用逻辑删除方式
- 字典sdk
- Commit
- 修复更改价格逻辑
- *(product)* 新增发布平台信息查询接口PlatformList 服务方法 - 在 PublishPlatformServiceImpl 中实现 getPlatformList 方法
- *(product)* 店铺放开校验接口
- *(shop)* 重构店铺列表查询功能 - 新增 getInnerShopList 方法，使用 MyBatis 实现店铺列表查询 -优化查询逻辑，移除冗余代码 - 增加平台名称字段，提高查询结果的完整性 - 调整服务层，简化店铺列表查询的业务逻辑
- *(product-price)* 实现产品价格计算功能并优化缓存策略- 新增产品价格计算服务和控制器 - 实现产品价格计算逻辑，支持多国语言和不同供货类型 - 优化缓存策略，提高数据加载效率 - 增加输入验证，确保计算数据的准确性
- *(product-price)* 调整计算价格方法入参类型
- 同步品类定时任务API
- *(pop-product-service)* 增加按国家配置小数位数功能 - 新增 CountryRoundingPrecisionBO 和 CountryRoundingPrecisionManager 类用于管理各国小数位数配置 - 在 ProductPriceFormula 中添加 roundingPrecisionType 和 countryRoundingPrecisions 字段 - 修改 ProductPriceCalcServiceImpl 中的计算逻辑，支持按国家配置小数位数 - 更新相关请求和响应对象，增加小数位数配置相关字段 - 添加 ProductPriceRoundingPrecisionTypeEnum 枚举类，用于定义小数位数配置类型
- 字典枚举和方法
- *(pop-product-service)* 添加下载任务和参考汇率相关接口 - 新增 JobDownloadTaskController，用于处理下载任务 - 新增 JobRefExchangeRateMarketController，用于处理参考汇率相关操作 - 修改 ProductPriceFormulaResp 中 countryRoundingPrecisions 字段的描述 - 优化 ProductPriceRuleGroupSaveReq 中的错误提示信息
- *(pop-product-service)* 添加自定义 BigDecimal 序列化支持 - 新增 BigDecimalFormat 注解和 CustomBigDecimalSerializer 类 - 在多个响应类中应用自定义 BigDecimal 格式化 - 优化汇率数据同步日志输出 - 替换 JSON 解析工具为 Hutool
- *(pop-product-service)* 添加批量保存基础中间价格规则功能
- *(image)* 优化花型图片处理逻辑 - 新增 getFlowerImageNames 方法获取花型图片名称集合 - 使用 Set集合存储花型图片名称，提高查询效率 - 对图片名称进行小写处理，确保大小写不敏感 - 优化图片处理逻辑，跳过花型图片- 增加参数校验，确保图片名称和 URL 不为空
- *(pop-product-service)* 分页查询SKU编码规则返回添加供应模式名称字段
- *(controller)* 更新 API 路径并添加权限检查忽略 - 将 API 路径从 "/inner/v1/job/download" 修改为 "/job/v1/download" - 将 API 路径从 "/inner/v1/job/market-rates" 修改为 "/job/v1/market-rates" - 在两个控制器类上添加 @PreCheckIgnore 注解，忽略权限检查
- *(pop-product-service)* 添加同步全量汇率数据异常处理
- *(pop-product-service)* 增加图片下载和上传的异常处理及重试机制 - 优化图片下载逻辑，增加 URL 校验和异常处理 - 引入异步下载任务处理，提高效率 - 新增重试配置和上传重试监听器 - 改进文件上传逻辑，增加异常捕获和重试
- 品类绑定平台类别-放开类别校验,支持1:n
- 开发节奏和上架节奏数量不能为空
- *(product)* 添加条码导出任务提交功能
- *(barcode)* 实现条码导出功能
- 对接sdp sdk
- *(product)* 增加价格有效性检查并优化产品创建逻辑 - 在 ProductCountryPriceCalcResultBO 中添加 hasInvalidPrice 方法，检查是否存在价格为零的情况 - 修改 ProductCreateServiceImpl 中的产品价格计算逻辑，增加对无效价格的判断
- *(price-calc)* 新增 AIGC 产品价格计算功能- 新增 ProductCountryPriceCalcInputBO 类用于封装产品价格计算的输入数据 - 重构 ProductPriceBasicCalcService 接口，新增计算 ISP 和 SP 倍率的方法 - 更新 ProductPriceBasicCalcServiceImpl 实现类，支持单独计算 ISP 和 SP 倍率- 修改 ProductPriceCalcServiceImpl 类，使用新的输入对象进行价格计算- 在 ProductPriceFormulaVariableEnum 中添加 AIGC_ISP_RATE枚举项
- 新增企划是否存在校验接口
- *(category)* 添加品类映射导入功能 - 新增 CategoryMappingExcelVO 类用于解析 Excel 数据 - 添加 CategoryMappingImportService 接口和实现类 CategoryMappingImportServiceImpl- 实现了从 Excel 导入品类映射的功能，包括读取 Excel 数据、构建映射关系、验证并保存映射 - 在 PublishCategoryMappingController 中添加了导入映射的 API 接口
- *(product-service)* 添加品类属性导入功能 - 新增 CategoryAttrExcelDTO 类用于解析品类属性 Excel 数据 - 在 CategoryMappingImportService接口中添加 importCategoryAttr 方法- 实现 CategoryMappingImportServiceImpl 中的品类属性导入逻辑 - 在 PublishCategoryMappingController 中添加品类属性导入的 API 接口
- *(pop-product-service)* 添加属性映射 Excel 导入功能 - 新增 AttributeMappingExcelDTO 类用于解析 Excel 数据 - 实现 AttributeMappingExcelListener 监听器处理数据 - 修改 CategoryMappingImportServiceImpl 中的 Excel 读取方法
- 新增接口-字典内部品类code获取平台品类
- *(pop-product-service)* 添加 Lazada 店铺 token 刷新定时任务接口 - 新增 JobLazadaController 类，提供 Lazada定时任务相关接口 - 实现 refreshShopToken 方法，用于刷新 Lazada 店铺 token - 使用 @PreCheckIgnore 注解忽略权限预检查 - 通过 withSystemUser 扩展函数以系统用户身份执行任务
- *(brand)* 初始化 Lazada品牌数据 - 新增接口和方法以保存 Lazada品牌信息 - 实现单个站点和所有站点的品牌数据初始化功能 - 添加品牌数据的获取和保存逻辑
- 企划品类价格-增加品类名称返回
- 增加接口-企划预设品类
- 清洗商品历史数据-品类
- 清洗商品历史数据-异常对象
- 清洗商品历史数据-TODO
- 清洗商品历史数据-空方法
- 清洗商品历史数据-条码
- 清洗商品历史数据-发布时间
- 清洗商品历史数据-skc和spu
- 清洗商品历史数据-空指针
- *(pop-product-service)* 添加产品属性处理功能 - 新增平台属性和属性值缓存初始化逻辑- 实现产品属性处理方法，包括获取 Lazada 商品详情、匹配属性和属性值、保存产品属性 - 增加错误处理和日志记录 - 优化代码结构，提高可读性和可维护性
- *(product)* 重构平台属性缓存结构并优化属性获取逻辑- 将 PLATFORM_ATTR_MAP 从单层 Map 改为嵌套的三层 Map 结构 - 新增 getPlatformAttr 方法以更高效地获取属性 - 更新属性初始化逻辑，按 categoryId 和 platformId进行分组 -调整日志输出，正确统计平台属性数量
- 清洗商品历史数据-颜色
- 清洗商品历史数据-兼容旧尺码
- *(product)* 修复商品图包数据 - 新增 FixProductService 接口的 fixProductImageRepository 方法 - 实现 FixProductServiceImpl 中的 fixProductImageRepository 方法- 添加处理商品图包的逻辑，包括下载和上传图片 - 更新 JobFixProductController，添加修复商品图包数据的接口 - 在 FixDataTypeEnum 中添加 IMAGE_REPOSITORY_ERROR 错误类型
- *(pop-product-service)* 新增尺码映射功能 - 添加 SizeMap 实体类，用于存储尺码映射信息 - 创建 SizeMapMapper 接口，定义尺码映射的数据库操作 - 实现 SizeMapRepository 类，提供尺码映射的数据访问功能- 更新版本号至 20250107
- *(product)* 更新商品尺码信息功能 - 新增 updateSaleSkuSizeNames 方法，用于更新商品尺码信息 - 添加 SaleSkuFetchReq 类作为查询商品尺码信息的请求参数 - 在 FixDataService 接口中定义 updateSaleSkuSizeNames 方法 - 实现更新商品尺码信息的逻辑，包括查询、映射和批量更新 - 在 MqExplaimInnerController 中添加更新商品尺码信息的接口
- *(product)* 优化查询功能- 在查询条件中增加对 "lzdSizeName" 字段的空值检查 - 这个修改可以用于解决特定的业务需求，例如：过滤掉未填写 lzd 尺码名称的商品
- *(product)* 优化更新商品尺码信息，先查出id，再做更新
- *(pop-product-service)* 新增店铺卖家映射表相关实体和接口 - 添加 ShopSellerMapping 实体类，用于表示店铺卖家映射表 - 新增 ShopSellerMappingMapper 接口，用于数据库访问 - 创建 ShopSellerMappingRepository仓储类，用于数据持久化操作
- *(product)* 懒加载用户信息处理 - 将 CountryUserInfo 从 AccessTokenResponse 和 GetTokenResponse 中提取到单独的类中- 在 ShopServiceImpl 中添加处理 CountryUserInfo 数据的方法 - 创建 ShopSellerMappingRepository 并在 ShopServiceImpl 中使用
- *(shop)* 添加店铺卖家映射信息 - 在 ShopResp 中添加 shopSellerMappingList 字段，用于存储店铺卖家映射信息- 新增 ShopSellerMappingResp 类，用于表示店铺卖家映射信息 - 在 ShopServiceImpl 中实现批量设置店铺卖家映射信息的方法- 在查询店铺列表和分页查询店铺时，添加店铺卖家映射信息
- *(product)* 优化增加删除逻辑，同时保持增量保存的功能
- *(pop-product)* 优化条码导出功能 - 添加请求参数校验，确保创建时间和结束时间在半年之内 - 修改导出数据获取逻辑，支持分页查询 - 增加供货价和号型字段，丰富导出数据内容 - 优化导出任务创建流程，先检查是否有数据可导出
- *(product)* 新增商品数据修复功能 - 添加 ProductDataRepairController 控制器 - 新增 ProductDataRepairService 接口及实现类 - 更新版本号至20250117
- 异常商品
- 调整校验异常商品逻辑
- Sql
- 校验非标准尺码
- *(product)* 商品管理-异常数据修复添加商品详情接口并优化相关服务 - 在 ProductDataRepairController 中添加商品详情接口- 在 ProductDataRepairService 接口中定义商品详情方法 - 实现 ProductDataRepairServiceImpl 中的商品详情逻辑 - 优化商品数据修复服务，增加商品属性、尺码排序等功能 - 更新版本号至 20250120
- *(product)* 商品管理-异常数据修复，添加商品数据更新功能 - 新增商品数据更新请求和响应模型 - 实现商品主表、SKC信息、销售商品和销售SKU的更新逻辑- 添加异常处理和事务管理
- *(product)* 添加商品 SKU 列表查询 v2 接口- 新增 OnlineSkuListReq 和 OnlineSkuListResp 类用于 SKU 列表查询 - 在 ProductController 中添加 pageOnlineSkuList 方法处理 SKU 列表查询请求 - 在 ProductOfflineService接口中定义 pageOnlineSkuList 方法 - 在 ProductOfflineServiceImpl 中实现 pageOnlineSkuList 方法（目前返回 null） - 更新版本号至 20250108
- *(product)* 新增批量下架商品功能 - 添加 OfflineSkuSubmitReq 和 OfflineSubmitReq 类用于处理批量下架请求 - 在 ProductController 中增加批量下架接口 - 在 ProductOfflineService 接口中添加 batchSendOfflineMessageV2 方法 - 在 ProductOfflineServiceImpl 中实现 batchSendOfflineMessageV2 方法
- *(product)* 添加导出下架失败数据功能 - 新增 FailedOfflineExportTaskReq 类作为导出下架失败任务的请求参数- 在 ProductController 中添加 submitFailedOfflineExportTask 方法处理导出请求 - 在 ProductOfflineService 接口中定义 submitFailedOfflineExportTask 方法 - 在 ProductOfflineServiceImpl 中实现 submitFailedOfflineExportTask 方法
- *(product)* 更新店铺ID请求参数类型 - 将单个 shopId 字段改为 shopIds 列表 - 支持传入多个店铺ID进行查询
- *(product)* 实现在线 SKU列表查询功能 - 新增 OnlineSkuListReq 请求对象，支持国家站点、店铺 ID等查询条件 - 在 ProductController 中添加在线 SKU 列表查询接口 - 实现 ProductOfflineServiceImpl 中的 pageOnlineSkuList 方法 - 在 SaleSkuMapper 中新增 pageOnlineSkuList 方法，并添加相应的 XML 配置 - 优化 SaleSkuRepository，支持在线 SKU 列表查询
- *(pop-product-service)* 添加商品变更失败记录相关实体和仓储 - 新增 ProductChangeFailure 实体类，用于记录商品变更失败信息- 新增 ProductChangeFailureMapper接口，用于商品变更失败记录的数据库操作 - 新增 ProductChangeFailureRepository 类，用于商品变更失败记录的仓储管理
- *(product)* 实现商品下架V2版本功能 - 新增ProductOfflineMqV2Dto类作为MQ消息体 - 在MqConstants中添加下架V2版本的Exchange、Key和Queue- 在ProductOfflineServiceImpl中实现批量下架商品V2版本的方法- 优化了参数校验、查询请求构建、分批处理和消息发送等逻辑
- *(product)* 增加下架失败记录保存功能 - 在 ProductChangeFailure 模型中添加 country 字段，用于记录站点信息 - 在 ProductOfflineService 接口中新增 consumeOfflineMessageV2 方法，用于处理下架消息 V2 - 在 ProductOfflineServiceImpl 中实现 consumeOfflineMessageV2 方法，并添加保存下架失败记录的逻辑- 优化原有的 consumeOfflineMessage 方法，增加失败记录保存 - 新增 saveProductChangeFailures 方法，用于保存商品变更失败记录
- *(product)* 添加导出下架失败数据功能 - 新增导出下架失败数据的请求对象和DTO - 实现导出失败记录的业务逻辑 - 添加任务处理和文件生成相关方法- 优化错误处理和日志记录
- *(product)* 优化商品下架功能并添加新接口 -重新调整商品下架 Excel 导出字段顺序和样式 - 新增商品下架接口 V2版本 - 优化下架逻辑，增加对排除 SKU 列表的处理 - 修复下架失败记录查询中的店铺 ID空值问题
- *(product)* 更新商品下架监听器 - 修改队列、交换机和路由键的名称，增加 V2 后缀 - 更新消息体解析类型从 ProductOfflineMqDto 到 ProductOfflineMqV2Dto - 调用新的 consumeOfflineMessageV2 方法处理消息 - 更新版本号至 20250114
- *(product)* 添加在线商品数量统计功能 - 新增 OnlineCountResp 类用于响应在线商品数量 - 在 ProductController 中添加 countOnlineSku 接口 - 在 ProductOfflineService 接口中定义 countOnlineSku 方法 - 实现 ProductOfflineServiceImpl 中的 countOnlineSku 方法 - 在 SaleSkuMapper 中定义 countOnlineSku 方法- 添加 SaleSkuMapper.xml 中的 SQL 查询语句 - 更新 SaleSkuRepository 接口，添加 countOnlineSku 方法
- 企划优化
- 同步Lazada商品到本地
- 新增查询商品详情方法
- *(product)* 新增发布商品通用模板导出功能 - 在 ExportPublishProductReq 中添加 exportTypeMode 字段，用于区分导出模式 - 实现通用模板导出逻辑，支持按店铺和时间筛选已发布商品 - 新增 PublishProductExportTypeModeEnum 枚举，定义导出模式类型 - 创建 PublishProductGeneralTemplateDTO 和 PublishProductGeneralTemplateExcelDTO 用于导出数据 - 在 SaleGoodsMapper 和 SaleGoodsRepository 中添加 findPublishProductGeneralTemplate 方法 - 更新版本号至 20250212
- *(shop)* 添加店铺别名和操作人信息字段 - 在 Shop 实体中添加了 shopAlias、operatorId、operatorName 和 operatedTime 字段 - 在 ShopEdit 请求中添加了 shopAlias 字段 - 更新了 ShopMapper.xml以包含新的字段 - 在 ShopResp 响应中添加了相关的新字段 - 修改了 ShopServiceImpl 中的方法，支持设置操作人信息 - 更新了版本号到20250213
- 关联属性-增加锁
- 异常订单导出合并单元格
- *(product)* 增加商品品类名称字段 - 在 PublishProductGeneralTemplateDTO 和 PublishProductGeneralTemplateExcelDTO 中添加 categoryName 字段- 在 SaleGoodsMapper.xml 中添加 category_name 列的查询
- *(product)* 添加 Lazada 商品拉取和同步功能 - 新增 ProductSyncTask 数据模型和相关 mapper、repository - 实现 Lazada 商品拉取和同步的业务逻辑 - 添加任务状态和任务类型的枚举类 - 优化 SaleGoodsRepository，增加按 productId 列表查询的方法 - 在 ProductLazadaController 中添加 @Validated 注解 - 更新 ProductLazadaPullReq 和 ProductLazadaSyncReq，增加参数校验
- Lzd查询接口数据保存源数据
- 异常订单导出-文案调整
- Lzd查询接口数据保存源数据-删除场景
- 抽出逻辑-更新任务状态
- 拉取组件返回商品结构(源数据)
- 任务状态
- 注释
- *(product)* 添加平台商品同步任务分页查询和取消任务功能- 在 PlatformProductPullMapper 中添加分页查询方法 - 在 PlatformProductPullRepository 中实现分页查询方法 - 新增 PlatformProductPullTaskPageQueryReq 和 PlatformProductPullTaskPageVo 类 - 在 ProductLazadaController 中添加分页查询和取消任务的接口 - 在 PlatformProductPullTaskService 中定义分页查询和取消任务的方法- 在 PlatformProductPullTaskServiceImpl 中实现分页查询和取消任务的方法 - 更新 ProductLazadaServiceImpl 中的任务状态更新逻辑
- 减少无用请求
- 兼容
- 任务分页和加锁
- 同步LZD任务
- 补充lzd字段
- 新增SKC逻辑
- Sale_goods更新状态
- Private
- Try catch
- 修复异常处理
- Product entity转为kt
- Title
- 修复sql
- *(product)* 添加平台商品拉取PullTaskMapper.xml 中添加了平台 ID、渠道 ID、平台商品 ID 和商品 ID 的查询字段
- 减少循环
- 增加日志
- 补充店铺和品牌
- *(product)* 优化 Lazada 商品拉取任务创建逻辑 - 新增 createPullTask 函数用于创建商品拉取任务 - 根据店铺类型（本土店或跨境店）处理不同的任务创建逻辑- 本土店创建单条记录，跨境店为每个国家创建记录 - 重构 submitPullTask函数，提高代码可读性和可维护性
- *(product)* 添加平台商品同步任务导出功能 - 新增导出任务类型和相关数据结构- 实现导出任务的提交和处理逻辑 - 添加导出数据的查询和统计接口 - 优化任务类型枚举类，提高查询效率 - 在控制器中添加导出任务提交接口
- 新增product和异常检查
- 无productId时查询
- 休眠2秒
- *(product)* 添加平台商品拉取任务类型筛选功能 - 在 PlatformProductPullTaskExportReq 中添加 taskType 字段，用于筛选任务类型 - 在 PlatformProductPullTaskMapper.xml 中添加与 taskType 相关的查询条件
- 修复product校验逻辑
- 更新颜色
- 增加清理任务, 兼容匹配不到的尺码
- 增加图库和字典颜色
- 尺码排序
- *(pop-product-service)* 优化平台商品同步任务名称生成逻辑 - 在任务名称中增加了任务类型描述 - 使用 PlatformProductPullTaskTypeEnum 枚举类获取任务类型描述 - 优化了任务名称的格式，提高了可读性和准确性
- 修复颜色
- Spu code相同的product
- Skc增加图片
- 品类
- Skc图
- 更新字段, 补充空pid数据删除逻辑
- *(product)* 优化产品服务功能 - 在 PlatformProductPullTaskServiceImpl 中添加 creatorName 字段 - 在 ProductServiceImpl 中优化 SKU 选择逻辑，优先选择启用的 SKU
- 补全sku
- Fix
- 字段更新
- 匹配barcode
- 修复空指针
- Fix sql
- 暂时不处理补充, 否则会导致前端展示错误
- 常量转为kt, 标准尺码统一常量管理
- 枚举转为kt
- Product上下架状态逻辑判断
- Lzd限流-任务状态改为排队中
- Mapper改为kt
- Entity改为kt
- Config改为kt
- Common改为kt
- *(pop-product-service)* 添加费用配置相关实体、Mapper和Repository - 新增 FeeConfiguration 实体类，用于费用配置表 - 新增 FeeConfigurationMapper 接口，用于费用配置表的数据库操作 - 新增 FeeConfigurationRepository 类，用于费用配置表的仓储操作
- *(settings)* 添加费用配置功能- 新增费用配置查询、保存和批量保存接口 - 实现费用配置服务和控制器 - 添加费用类型枚举 -集成国家代码和币种类型校验
- *(dao)* 新增品类价格区间表相关实体和接口 - 添加 CategoryPriceRange 实体类，用于表示品类价格区间信息 - 新增 CategoryPriceRangeMapper 接口，用于操作品类价格区间表 - 创建 CategoryPriceRangeRepository 类，提供品类价格区间的仓库功能
- *(pop-product-service)* 添加产品定价类型和现货类型枚举 - 新增 ProductPricingTypeEnum 枚举类，用于定义产品定价类型 - 新增 ProductSpotTypeEnum枚举类，用于定义产品现货类型 - 每个枚举类都包含了 Companion object 用于根据代码获取对应的枚举值
- *(pop-product-service)* 调整产品定价类型和现货类型枚举注释
- 增加标签mq消费者代码
- 可用库存同步和标签计算
- 清仓标签
- *(product)* 新增定价成本计算功能 - 添加 CostPriceCalculationTypeEnum 枚举类，定义成本价计算类型 - 新增 ProductCostPricingRequestBO 和 ProductCostPricingResultBO 数据传输对象- 实现 ProductCostPricingService 接口及其实现类- 在 ProductSkc 实体中添加采购价和定价成本字段- 更新 ProductPricingTypeEnum 描述 - 在 PublishProperties 中添加定价成本相关配置
- Try
- 库存接口和表增加字段
- 批量更新-导出数据api
- *(price)* 增加物流费用和采购价的计算支持- 在产品价格计算中添加物流费用和采购价的支持 - 新增费用配置缓存，提高查询效率 - 更新产品价格计算逻辑，支持新的费用类型 - 添加相关枚举和数据结构支持
- *(product)* 添加 SPU 编码字段并优化Excel导出- 在 PlatformProductPullTask 模型中添加 spuCode 字段 - 在 PlatformProductPullTaskExcelDTO 中添加 spuCode 属性，并设置列宽 - 更新 PlatformProductPullTaskServiceImpl 和 ProductLazadaServiceImpl，支持 SPU 编码 - 移除 buildTaskName 函数
- *(product)* 添加商品价格修改记录功能 - 在 BatchUpdatePriceReq、EditProductReq 和 UpdatePriceReq 中添加上次销售价和上次划线价字段 - 在 SaleSku 实体中添加上次销售价、上次划线价、采购销售价、采购划线价、常规销售价和常规划线价字段 - 更新 ProductServiceImpl 中的价格更新逻辑，支持记录上次价格信息
- *(product)* 丰富 SKU 响应数据 - 在 ProductSkcResp 类中添加 SKU 信息列表 - 在 SaleSkuResp 类中增加多个价格相关字段，包括上次销售价、上次常规划线价、采购销售价、采购划线价、常规销售价和常规划线价
- *(product)* 增加商品定价和现货类型字段 - 在 ProductDetailResp 中添加 pricingType 和 spotType 字段 - 在 ProductSkcResp 中添加 purchasePrice 和 costPrice 字段
- 通用任务表生成DAO
- 导入任务
- Job分发任务逻辑调整
- 锁5分钟
- 任务进度
- 失败导出
- 调整同步/拉取任务表适配
- 导出字段和更改标题
- 更新价格, 库存, 标题
- 批量更新-校验修改值是否一致
- *(sdk)* 添加店铺外部客户端接口 - 新增 ShopExternalClient 接口，用于获取店铺和品牌列表 - 更新版本号至 20250227
- 抽出更新lzd价格库存逻辑
- 拉取同步-增加2个脏数据处理逻辑
- *(product)* 添加批量查询产品图片功能 - 新增 ProductImageBatchQueryReq 数据类用于批量查询产品图片请求 - 添加 LazadaImageUtil 工具类处理图片相关逻辑 - 在 ProductClient 中添加批量查询产品图片的接口 - 实现 ProductExternalService 接口，提供批量查询产品图片服务- 在 ProductInnerController 中添加批量查询产品图片的控制器方法
- 增加租户id
- 移除已有的entity租户字段
- 实体填充器增加租户id
- *(product)* 添加商品定价成本计算功能 - 在 ProductController 中新增 calculateCostPriceBatch 方法
- 发布标题
- *(product)* 添加自动计算商品价格功能 - 在 ProductController 中新增 autoCalPrice 方法，用于自动计算商品价格
- 商品列表接口增加标签参数
- 商品列表增加标签
- 修复导入
- 商品列表展示标签
- 展示字段
- 兼容未发布场景
- 导出
- 导出数据
- 多次返回
- 标签导出
- 状态导出,更新标题
- 导出无数据
- 状态和上下架
- *(product)* 在产品成本定价服务中，排除导入标志为 YES 的商品
- 导出失败-筛选状态
- 导出增加标题行
- *(pop-product-service)* 新增导入商品成本价计算方式 - 在 CostPriceCalculationTypeEnum 中添加 IMPORT_FLAY 枚举项，用于导出的 SKC 使用供货价 - 修改 ProductCostPricingServiceImpl 中的成本价计算逻辑，增加对导入商品的特殊处理 - 新增 createImportSupplyPriceResult 方法，用于创建导入商品的成本价结果- 调整现有成本价计算方法，以适应新增的导入商品计算方式
- *(product)* 添加产品成本价参数 - 在 ProductPriceCalcBO构建过程中增加了 purchasePrice 字段 - 该字段值来源于 productSkc 的 purchasePrice 属性- 此修改旨在支持产品成本价的计算和管理
- *(price)* 优化价格计算逻辑 - 在 ProductCountryPriceCalcResultBO 中添加采购销售价和采购划线价字段 - 修改价格计算逻辑，支持采购价计算- 优化设置价格的逻辑，区分强制设置和非强制设置- 调整小数点保留位数，提高精度
- SellerSku拉取LZD商品
- *(product)* 优化 SKU 数据处理和排序逻辑 - 在 LazadaCountryEnum 中添加国家代码到顺序索引的映射 - 在 ProductServiceImpl 中实现 SaleSku列表的预排序 -优化分组和遍历逻辑，提高数据处理效率
- 空任务
- 暂时注释租户字段
- 放开注释租户字段
- 同步lzd商品数据增加异常尺码转换
- 标记异常商品
- 删除无用代码
- 处理尺码和颜色为空的场景,跳过匹配barcode
- SaleGoods发布时间使用Lazada创建时间
- 异常商品检查规则-增加skc系统颜色为空
- 匹配barcode增加尺码组条件
- *(product)* 添加发布商品到 Lazada 时的日志记录 - 在 ProductCreateServiceImpl 中添加了发布商品到 Lazada 时的日志记录 - 在 ProductPriceManagementServiceImpl 中添加了开始自动计算价格的日志记录
- *(pop-product-service)* 添加 Lazada 国家代码列表获取功能并优化商品创建逻辑 - 在 LazadaCountryEnum 中添加 getCountryCodeList 静态方法，用于获取国家代码列表 - 修改 ProductCreateServiceImpl 中的 autoCalPrice调用，使用新的国家代码列表方法
- *(ImageRepDetailVO)* 增加图片详情的创建和修改信息字段 - 新增创建人名称 (creatorName) - 新增创建时间 (createdTime) - 新增最近修改人名称 (reviserName) - 新增更新时间 (revisedTime)
- *(product)* 添加产品最近上下架时间字段并更新相关逻辑 - 在 SaleGoods 和 SaleSku表中添加最近上架时间和最近下架时间字段 - 更新 ProductCreateServiceImpl 中的产品发布逻辑，设置最近上架时间 - 修改 SaleGoodsRepository 和 SaleSkuRepository 中的下架逻辑，设置最近下架时间
- *(product)* 添加商品上下架时间信息 - 在 ProductDetailResp 和 ProductPageResp 中添加首次上架时间、最近上架时间和最近下架时间字段 - 实现计算商品上下架时间信息的逻辑- 优化部分代码，如使用 Kotlin data class 创建 PublishTimesBO
- *(product)* 添加商品最近上架人信息 - 在 ProductCreateServiceImpl 中添加最近上架人 ID 和名称字段 - 在 ProductDetailResp 和 ProductPageResp 中添加最近上架人信息 - 重命名 PublishTimesBO 为 ProductPublishInfoBO，增加最近上架人信息 - 更新 ProductServiceImpl 中的相关逻辑 - 在 SaleGoods 实体中添加最近上架人 ID 和名称字段
- *(sku)* 优化 SKU编码生成逻辑 - 移除 SkuCodeGenerateReq 中的 supplyMode 非空校验 - 在 SkuCodeGenerateServiceImpl 中增加对 supplyMode 空白情况的处理 - 当 supplyMode 为空时，使用默认规则生成 SKU 编码 -保留原有的 SPU 编码、颜色和尺寸信息
- 补充初始化数据
- 异常订单-移除订单唯一校验
- *(product)* 添加批量更新图片功能 - 新增批量更新图片请求和任务相关实体类 - 实现批量更新图片任务提交和处理逻辑 - 添加失败数据导出功能 - 更新相关枚举和接口定义
- *(product)* 添加商品图片批量更新功能 - 新增 updateImage函数处理商品图片批量更新任务- 在 TaskServiceImpl 中添加对应任务类型的处理逻辑 - 优化了任务处理流程，增加了异常处理和状态更新
- *(pop-product-service)* 添加批量更新图片失败数据导出功能 - 新增图片更新失败任务导出DTO和导出组件 - 实现导出逻辑，包括数据查询、组装和Excel文件生成 - 优化下载任务类型枚举，修正批量更新图片任务的导出组件引用
- Barcode处理,skc状态更新
- 对齐尺码和补充初始化saleGoods
- Req > kt
- Resp > kt
- *(pop-product-service)* 添加外部 API 调用日志功能- 新增 ExternalApiCallLog 实体类，用于记录外部 API调用的日志信息 - 添加 ExternalApiCallLogMapper 接口，实现对日志数据的持久化操作 - 创建 ExternalApiCallLogRepository 类，提供日志数据的访问接口
- *(pop-product-service)* 添加外部 API 调用日志功能- 新增 ExternalApiCallLog 实体类用于记录 API 调用日志 - 添加 ExternalLogApiCall 注解标记需要记录日志的方法 - 修改 LazadaApiService 接口，为相关方法添加注解 - 实现 ExternalApiCallLogAspect 切面类，负责记录 API 调用日志 - 更新相关服务类，传入业务 ID 参数以支持日志记录
- Req & resp > kt
- *(product)* 添加下架商品操作日志 - 在 ProductOfflineServiceImpl 中添加 productOperateLogRepository依赖 - 在下架商品流程中创建并保存操作日志- 日志内容包括下架类型、产品 ID 和下架的 SKU 列表 - 更新版本号至 20250313
- 对齐尺码,只启用有sellerSku的颜色
- 对齐尺码,不改变skc状态
- 检查skc状态, 只处理取消状态, 因为skc可能会系统更新成取消, 只时候拉下来是否有数据都不能激活, 维持原样
- *(product)* 增加商品最近修改信息并优化图片处理- 在 ProductPublishInfoBO 中添加最近修改用户 ID、名称和时间字段 - 更新 extractProductPublishInfo 方法，增加最近修改信息提取- 优化 ImageRepositoryService 中的图片处理逻辑 - 修复 ProductServiceImpl 中的产品详情和列表页面最近修改信息展示
- 异常订单多次推送
- 调整同步对齐尺码的初始化逻辑
- 补充上架逻辑场景
- 商品更新接口不更新发布状态
- 上架不需要判断product状态
- 回滚部分逻辑
- *(product)* 新增 SKC操作日志记录功能 - 在创建产品时，记录新增和更新 SKC 的操作日志 - 新增日志记录方法，支持批量新增和更新 SKC 的日志记录 - 优化了 SKC 处理逻辑，增加了日志记录相关的参数和方法
- 商品内部查询接口适配组合商品场景
- 调整内部商品接口参数
- 新增关系表DAO
- 商品内部接口支持组合商品barcode
- 多条码
- *(product)* SkcRepository.updateById事务一致性问题
- 调整处理排队的导入任务会话用户
- *(product)* 添加商品下架相关信息 - 在 ProductDetailResp、ProductPageResp、ProductPublishInfoBO 和 SaleGoods 类中添加最近下架用户名称字段 - 在商品下架操作中记录最近下架时间、用户ID和用户名 - 更新商品详情和列表接口，展示最近下架用户名称
- 拉取商品解析SPU
- 拉取商品解析SPU-去空
- *(product)* 添加产品上架用户信息 - 在 Product 实体中添加 publishUserId 和 publishUserName 字段 - 在产品创建时设置上架用户信息 - 在产品详情和列表响应中返回上架用户名称 - 更新数据库查询以包含上架用户信息
- SellerSku拉取lzd调整
- 对齐尺码默认库存100
- 模拟初始化sku, 但不初始化goods
- 增加接口-增加补充条码sku接口
- *(core)* 优化字段自动填充逻辑 - 重构 ApplicationMetaObjectHandler 类，简化字段填充方法- 新增 fillField 和 hasNullField 方法，提高代码复用性 - 更新 BaseEntity 类，使用字段注解替代属性注解 - 增加测试用例验证自动填充功能
- 商品发布/更新任务MQ改为定时任务
- Mq
- Product合并
- *(pop-product-service)* 为多个 Kotlin 类添加 Java 兼容性方法 - 在多个 Kotlin 类中添加了以 `get` 和 `set` 开头的方法，以提高 Java 代码的兼容性 - 新增方法包括 `getIsBothPriceAndState`、`setIsBothPriceAndState`、`getIsForceCreate`、`setIsForceCreate` 等 - 这些方法使用了 `@JvmName` 注解以指定 Java 中的方法名 - 部分类还添加了 `@JvmOverloads` 注解以支持 Java 中的重载 - 更新了 `versions.properties` 文件中的版本号
- *(pop-product-service)* 添加 AliExpress 接口服务 - 新增 AliexpressProperties 配置类，用于 AE 配置 - 新增 AliexpressService 接口和实现类，提供 AE 接口调用功能 - 新增 AliexpressServiceTest 测试类，用于测试 AE 接口调用- 重构测试类包结构，统一到 tech.tiangong.pop.service 下
- 合并SPU逻辑
- 异常修复页面-拉取Lazada数据逻辑改为通用拉取
- 移除product.shop的使用和copy()
- *(AliexpressService)* 添加刷新授权令牌功能 - 在 AliexpressService 接口中新增 refreshToken 方法- 在 AliexpressServiceImpl 类中实现 refreshToken 方法 - 优化 queryCategoryTreeList 方法的错误处理
- 调整代码
- 已上架分页-增加店铺维度
- *(auth)* 添加 Aliexpress 授权回调功能 - 在 AliexpressService 中新增 createToken 方法，用于创建授权令牌 - 在 AliexpressServiceImpl 中实现 createToken 方法 - 在 ShopOpenApiController 中添加 getAEToken 方法，处理 AE 授权回调 - 在 ShopService 中新增 getAEToken 方法，用于获取 AE 授权令牌 - 在 ShopServiceImpl 中实现 getAEToken 方法
- 独立上架代码
- 待上架列表分页接口
- 待上架列表分页接口-补充价格异常字段
- 待上架详情接口
- 待上架详情接口-Lazada详情
- 待上架更新接口-Lazada更新
- 商品内部page接口把分页字段下放, 调用方的框架版本有限制长度
- 待上架商品列表-字段调整
- 待上架商品列表-增加新字段
- 待上架-导入商品
- *(dao)* 新增产品基础价格规则组和条件表
- 待上架-导出商品
- *(product-price)* 实现产品基础价格条件规则计算 - 新增规则条件缓存和明细缓存，优化性能 - 实现条件规则匹配和评估逻辑 -支持根据多个条件计算价格倍率 - 增加字符串和数值类型的条件判断 - 新增基础配置变量选择枚举
- *(product)* 重构基本款价格规则功能 - 新增价格规则条件和明细的保存请求类- 更新价格规则分页查询响应类，增加条件规则相关字段- 在价格规则服务中添加缓存刷新方法 - 删除了旧的保存请求类和明细类- 优化了业务校验逻辑
- 待上架-Lazada-更新/上架
- 待上架-移除字段(发货期,商品状态)
- *(pop-product-service)* 增加价格规则条件列表最大长度- 将条件规则列表的最大长度从 10 条增加到 20 条
- *(pop-product-service)* 更新 SaleGoodsMapper 查询条件- 将 publish_state 的查询条件从单一的1 修改为包含 1 和2
- 待上架-更新sku
- 待上架-分页-增加平台字段
- 调整模板表名
- 待上架-AE详情
- 待上架-AE更新
- 待上架-导入AE
- 待上架-详情增加品类字段
- 待上架-导入数据和执行增加平台id区分
- 待上架-导入数据AE
- 待上架-导入和平台更新
- 待上架-导出
- 组合商品-sku配置查询接口
- 待上架-详情补充字段
- 待上架-组合商品
- 待上架-更新, sku更新enable=0
- 待上架-详情和更新, 上次价格
- *(pop-product-service)* 实现产品 SKC 同步到销售 SKC 功能 - 新增 ProductSkcRepository 中的 getByProductIdAndSkcCodeIn 方法 - 在 SaleSkcRepository 中添加 findBySaleGoodsId 方法 - 实现 SaleSkcSyncComponent 组件，用于同步产品 SKC 到销售 SKC - 优化更新现有 sale_skc 的逻辑，只更新变化的字段
- *(product)* 新增 SKC同步功能并优化 sale_skc 更新逻辑 - 在 ProductServiceImpl 中添加 SKC 同步到 sale_skc 的功能 - 优化 SaleSkcSyncComponent 中的 saleSkc 更新逻辑 - 新增创建产品时的 SKC 同步 - 在更新颜色、采购价等信息时同步到 sale_skc
- 待上架-详情和更新, 是否前台
- Lazada增加发货期字段
- 待上架-上过架的SPU-店铺-站点, 不给再上架
- 待上架-计算SKU国家价格
- 调整和增加表字段
- 待上架-组合商品json参数
- *(product)* 新增款式风格更新功能 - 在 CreateProductDto 和 ProductUpdateDto 中添加款式风格相关字段 - 在 Product 实体中添加款式风格属性 - 实现款式风格更新的业务逻辑 - 添加款式风格筛选条件到产品查询接口 - 更新版本号到0.0.8-SNAPSHOT
- *(pop-product-service)* 添加 RestTemplate 配置类 - 新增 RestTemplateConfig 类，配置 RestTemplate bean - 用于处理 HTTP 请求，与其他微服务进行通信
- Spring data redis 配置名迁移
- 待上架-组合商品-上架记录barcode快照
- 更新EasyCode模板和版本SQL
- Version更新0.0.9-SNAPSHOT
- 待上架-条码快照-增加平台id字段
- *(product)* 添加批量创建商品的 MQ 监听功能 - 新增 BatchCreateProductListener 类用于监听批量创建商品的消息 - 在 MqConstants 中添加相关的 MQ队列和交换机常量- 修改 MqBaseListener 和 PimsMqBaseListenerV2 中的异常处理逻辑 - 更新版本号至 20250407
- 履约内部接口-同步查询SKC
- 内部接口-platformSellerId获取SKU数据
- Doc
- *(product)* 根据店铺ID查询商品详情 - 在 ProductDetailReq 中添加 shopId 字段，用于指定店铺ID - 修改 ProductMapper.xml 中的 SQL 查询，以支持按店铺ID筛选商品 - 更新 ProductServiceImpl 中的商品查询逻辑，增加对店铺ID的处理
- 内部接口-ETA参数
- 内部接口-TEST变更类名
- 内部接口-平台名称字段改成平台id
- 内部接口-调整字段,模板表初始化
- *(product)* 批量改价根据店铺ID筛选上架站点- 在 BatchUpdatePriceReq 中添加 shopIds 字段，用于指定店铺ID集合 - 在 ProductPriceManagementServiceImpl 中增加根据店铺ID筛选上架站点的逻辑 - 优化了 SKU 数据的查询方式，通过 saleGoodsId 进行查询- 增加了对上架站点数据为空的异常处理
- 获取品类-异常信息
- Rename
- 内部接口-新增字段
- 依赖ofp-0.0.3-SNAPSHOT
- 对接ETA
- 调整库存类型String改为Int
- 调整库存类型和发货期字段
- 补充数据权限的provider
- 待上架-详情增加发货期字段
- 修复sale_skc数据SQL
- 接收更新发货期
- 初始化模板表-获取发货期和商品状态
- 组合商品-配置增加条码字段
- 创建商品-初始化模板
- 待上架-更新接口增加上次价格字段
- 上游更新
- 待上架-详情接口sku增加enable
- 待上架-分页接口移除库存类型字段
- 待上架-分页接口移除发货期字段
- 待上架详情,清洗数据SQL
- *(product)* 添加 Lazada 商品分页查询功能接口抽象
- *(product)* 添加 Lazada 商品更新功能 - 新增 Lazada 商品更新接口和相关请求、响应对象 - 在 ProductLazadaService 中添加 update 方法用于处理商品更新 - 更新 ProductLazadaController 中的 page 方法，使用新的 LazadaProductPageResp 类 - 修改 ProductLazadaDetailResp 类，增加店铺信息和启用状态字段
- 增加条码展示
- 修复barcode和updateById
- 组合商品弹窗接口-调整字段
- Enable更新
- *(product)* 添加 Lazada 商品更新功能 - 新增 Lazada 商品更新接口和相关请求、响应对象 - 在 ProductLazadaService 中添加 update 方法用于处理商品更新 - 更新 ProductLazadaController 中的 page 方法，使用新的 LazadaProductPageResp 类 - 修改 ProductLazadaDetailResp 类，增加店铺信息和启用状态字段 shop，lazada 相关 转kt
- *(product)* 调整包路径Lazada平台API交互的响应实体
- 待上架-更新到Lazada
- 待上架-更新到Lazada平台
- Lazada商品API-创建/更新
- 待上架-更新到Lazada平台-创建sellerSku
- 统一风格
- 字符串拼接
- 移除无引用方法
- Log调整
- *(product)* 添加 Lazada 商品详情功能 - 实现了 Lazada 商品详情的查询逻辑 -增加了商品基础信息、店铺信息、属性信息、图片信息等的获取和处理 -优化了 SKC 和 SKU 信息的展示方式 - 添加了灵感来源信息的获取和展示
- 修复lazadaSpuId
- *(product)* 调整 Lazada 商品详情功能
- 待上架-导入导出接口调整
- 优化商品内部分页接口SQL
- *(product)* 数据修复新增生成条码功能 - 在产品数据修复过程中增加生成条码的步骤- 获取产品和SKC信息，构建条码生成请求 -调用条码服务批量生成条码 - 优化错误处理和日志记录
- *(product)* 优化产品导出时成本价的处理逻辑 - 在导出产品时，如果成本价为空，优先使用本地价，再使用采购价 - 在 PublishProductGeneralTemplateDTO 中添加最大本地价和最大采购价字段 - 更新 SaleGoodsMapper.xml，分别查询最大成本价、最大本地价和最大采购价
- *(product)* 新增 Lazada 商品分页查询功能 - 添加 ProductLazadaPageQueryReq 和 ProductLazadaPageResp 模型类 - 实现 Lazada 商品分页查询的接口和服务- 新增相关 mapper 和 SQL 查询 - 优化数据处理逻辑，包括时间条件处理、saleGoods 分组、SKU 统计等
- 重新建表-sellerSku和条码关系
- 组合商品条码
- 记录sellerSku和条码快照
- 条码-组合商品-分页
- 条码-组合商品-导出
- Eta对接字段调整
- *(product)* 增加商品库存类型和发货期字段 - 在 ProductLazadaPageQueryReq 中添加 stockType 和 delayDeliveryDays 字段 - 在 ProductLazadaPageResp 中添加 stockType、stockTypeName 和 delayDeliveryDays 字段 - 在 ProductLazadaServiceImpl 中处理新增字段的数据 - 在 SaleGoodsMapper.xml 中添加相关查询条件
- 修复mybaits的kt lambda
- 内部接口调整字段shopName > shopCode
- 修复上架接口productId集合
- *(product)* 增加选款人和上架人筛选功能 - 在 ProductLazadaPageQueryReq 中添加选款人姓名和上架人字段 -修正 ProductLazadaPageResp 中的上下架状态字段拼写错误 - 更新 ProductLazadaServiceImpl 中的相关查询逻辑 - 修改 SaleGoodsMapper.xml 中的 SQL 查询语句，支持新的筛选条件
- 更新接口增加属性保存
- 上架回滚
- 修复内部商品接口获取最新上架时间
- 待上架-上架Lazada
- 删除无用方法
- 调整上架异常日志
- ODM和现货TRY ON补充默认尺码
- 修复上架
- 修复多SPU情况
- 移除get lazada api 重试
- 非ODM和现货TRY ON补充默认尺码
- *(product)* 更新已上架Lazada商品信息 -增加商品属性更新功能 - 实现销售SKC和SKU数据更新 - 添加商品图库更新逻辑 - 优化商品状态字段更新
- *(product)* 修改库存类型查询条件 - 将 ProductLazadaPageQueryReq 中的 stockType 字段改为 stockTypes，支持多对多查询 - 更新 SaleGoodsMapper.xml 中的 SQL 查询条件，支持库存类型 IN 查询
- 待上架-上架组装sku修复
- 待上架-导出数据
- 待上架-导入进度-增加平台名称字段
- 翻译组件 java > kt
- 组合商品-配置查询
- *(product)* 增加条形码字段并更新相关逻辑
- 组合商品-写入
- 组合商品-兼容
- 包裹重量读取
- 图库校验
- 上架捕捉异常
- 初始化默认勾选前台
- *(lazada)* 添加商品包装规格属性 - 在 sale_goods表中添加包装规格相关字段：package_weight、package_dimensions_length、package_dimensions_height、package_dimensions_width - 在 CallLazadaComponent 和 LazadaUpdateProductComponent 中处理新增的包装规格属性 - 更新 SaleGoods 实体类，增加包装规格相关字段
- 属性平台id不是用product表
- 发布到Lazada好不更新shopId
- 增加条码参数
- 待上架详情
- 发货期
- 发货期默认期货
- 内部接口-查询sku更新
- 修复条码关系
- 待上架-详情Lazada标题
- 内部接口-商品分页-兼容组合商品
- *(product)* 新增 Lazada 商品上架功能 - 添加 updateOrAddLazadaCountry 方法，实现 Lazada 商品的更新和新增- 在 ProductLazadaController 中添加 updateLazadaExistingPublish接口 - 新增 ProductLazadaExistingPublishReq 类用于处理上架请求 - 修改 ProductLazadaServiceImpl 中的查询逻辑，增加按店铺 ID 查询 - 更新 PublishCategoryMappingRepository 接口，增加新的查询方法 - 在 SaleGoodsRepository 中添加 listByProductIdAndShopId 方法 - 更新 SaleSkuRepository 接口，增加 findBySaleSkcIds 方法
- 实用库存接口 java > kt
- 待上架-详情-平台品类名称
- 待上架-上架-优化发货期
- 待上架-组合商品-更新
- 待上架-组合商品-调整
- 条码-组合商品-查询规则调整
- 条码-组合商品-回填SKC图片
- 新增ProductSkuSyncV2Component
- 替换已过时的方法
- 修复组合商品导出
- 导出上架失败-上架时间调整
- 内部接口标题调整
- 内部接口修复
- 组合商品条码导出
- 内部接口-unit快照查询
- 修复待上架
- 组合商品
- 上游改尺码, 只删除了, 没有增加
- 条码弹窗需要barcode再拿一次spu+skc查询
- 更新AE店铺信息兼容lazada多站点mapping表
- 增加校验-库存不能为空
- Todo
- 条码-组合商品-分页-修复
- 上游新增SKC
- 默认值
- 修复内部接口
- 待上架-导出数据-过滤组合商品
- 导入不改条码颜色
- 已上架-组合商品保存
- 已上架-组合商品保存数量
- 上架-标题名称拼接
- 内部接口
- 组合商品-更改记录
- Combo
- 待上架-商品导出
- 上架时取图，目前是完全匹配Blue.png,兼容spu-Blue.png
- Flat
- 移除sale的模板id
- 调整已上架详情和更新接口的id字段
- 已上架-单站点更新
- 已上架-分页
- 已上架-详情-单站点
- 已上架-分页-单站点
- Delete
- 已上架-导出数据-增加波次字段
- 同步
- 批量更新
- 已上架-全站点更新
- PublishCategoryAttributeService Java 转 kt
- 拉取同步
- CategoryMappingImportService Java 转 kt
- PublishCategoryMappingController, PublishCategoryAttributeController Java 转 kt
- 应用更新
- 添加速卖通类目相关实体、映射、服务和缓存功能
- 拉取同步-初始化模板
- 速卖通类目同步优化，实体映射速卖通接口相应解决反序列化map映射问题
- 组合商品-分页-品类code
- 条码-批量生成条码
- 应用更新-LOG
- 速卖通类目同步优化，取指定一级类目而非全部，配置默认值
- 应用更新-尺码判断
- 已上架-详情-sku返回平台sku_id
- 已上架-分页-更新状态
- 速卖通类目同步优化，取指定一级类目而非全部，配置默认值，增加注释
- 已上架-详情-更新状态
- 速卖通类目同步优化，取指定一级类目而非全部，配置调整
- PublishPlatform, Channel Java -> kt
- PublishCategoryService Java -> kt
- 已上架-同步到其他店铺
- Category controller Java -> kt
- Image controller Java -> kt
- 已上架-同步其他店铺-调整 增加价格重算, 手动事务, 更新Lazada
- Download controller Java -> kt
- 已上架-详情-全站点-过滤组合商品
- 已上架-详情-更新Lazada库存
- 已上架-更新
- 已上架-导出
- Add AliexpressCategoryAttributeResponse
- 更新全球速卖通服务中的令牌处理，以支持可空令牌
- 已上架-批量更新-修复
- 清洗SQL
- PublishCategoryAttributeServiceImpl.findByCategoryAttrIds 支持速卖通类目属性查询
- 待上架-AE上架
- 优化品类映射保存 PublishCategoryMappingServiceImpl.save
- 移除无用代码
- 已上架-AE接口
- 优化 PublishCategoryAttributeServiceImpl 中平台属性的属性复制逻辑
- 上传图片到速卖通临时目录接口
- 已上架-应用更新数据来源
- 已上架-AE-接口参数优化
- 内部接口-AE逻辑
- 构建速卖通图片对象ProductsPublishAliExpressHelper.buildAliExpressAddImage
- 解决PublishGlogbalBizException冲突
- 更新BusinessException导入路径
- 已上架-AE-批量下架
- 已上架-AE-TODO
- 已上架-AE-commit
- 新增封装速卖通服务模板，运费模板，设置商品尺码，尺码模板封装接口
- 添加类目属性缓存功能及失效接口
- 已上架-批量更新-价格/库存-过滤skc为空
- 已上架-批量更新-图片
- 添加速卖通授权和刷新令牌响应DTO
- 添加AliExpress店铺token刷新功能及相关方法
- Ae api
- 拉取同步-兼容组合商品
- 修复skc
- 更新访问令牌参数名称为accessToken，并添加查询欧盟责任人列表功能
- 添加速卖通查询制造商信息详情、商品状态接口封装
- 同步状态
- 添加制造商信息列表查询接口及相关DTO
- 添加速卖通商家账号关系列表查询接口及相关DTO
- 使用ShopRepository获取AliExpress测试用accessToken
- 优化AliexpressServiceTest中的日志记录，替换println为log
- 定价成本
- 添加获取AliExpress产品分组的接口及相关DTO
- 组合商品-sellerSku生成
- 组合商品-待上架-sellerSku递增
- 修改AliExpress API调用协议为TOP并优化状态查询逻辑
- 待上架-更新价格
- 更新SKC
- 创建商品
- 新增skc, 模板不处理, 由init处理
- 修改AliExpress API调用协议为TOP并更新测试用例中的制造商ID
- 拉取同步-包裹信息
- 上游更新-定价成本
- 条码-组合商品-图片
- 上游更新-待上架-重算
- 待上架-AE-更新
- 上游创建商品
- 已上架-AE-统计
- 已上架-AE-分页
- 重命名速卖通相关枚举并更新引用
- 拉取同步-包裹
- 已上架-同步
- 待上架-AE-上架
- 添加速卖通商品信息请求和编辑响应DTO
- 已上架-Lazada-分页
- 内部接口-AE-屏蔽了pid和frontUrl
- 已上架-AE-分页-count
- 内部接口-调整
- 添加查询平台属性功能及相关数据传输对象
- 导入-重算价格
- 添加TODO注释以标记AE商品创建/更新逻辑
- 取消SKC, AE待上架没有取消
- 已上架-sellerSku关系绑定, 只记录一次
- 更新速卖通商品上传逻辑，添加图片校验和增量上传功能
- 待上架更新属性
- 优化AeUpdateProductComponent变量名，默认值
- 只有一条快照的时候也要展示列表的更新人
- 同步到其他店铺
- 同步到其他店铺-只处理可用站点
- 上架-校验店铺可用站点
- 待上架-导出-现货类型
- 同步到其他店铺-没有SKU
- 同步到其他店铺-sku下的站点
- 更新字段,强制覆盖
- 商品属性-区分平台
- 上游更新-标记product更新
- 固定ae上架属性值先调试
- 移除AE商品更新方法的注释，确保商品状态更新
- 组合商品重新生成唯一的sellerSku
- 已上架-AE-下架
- 更新AE速卖通响应DTO，统一错误代码字段命名及成功判断逻辑
- 组合商品sellerSku唯一
- 更新AE速卖通响应DTO，新建基类处理公共字段
- 更新AE速卖通产品组件，修改saleGoods字段为platformProductId
- 商品类型
- Ae editProduct 后设置状态审核中
- 增加条件规则列表最大条数至50，优化条件ID处理逻辑
- AE下架弹窗数量统计调整
- 增强价格规则验证逻辑，支持根据规则类型校验价格类型和运算符
- 分页返回平台
- 更新平台属性映射，增强属性查询和数据传输逻辑
- 待上架-平台展示
- AE库存扣减
- AE导出
- SellerSku
- 定时刷新AE商品审核结果
- LZD全站点更新
- 下架
- AE 创建/ 更新商品 sku属性动态设置
- Ae创建或更新 error handling，关联属性优化
- AE下架
- AE前台链接
- 定价成本-损益临界值变更为15
- 更新定价成本-损益临界值为11
- 更新时间
- 优化图片处理逻辑，确保图片和属性唯一性
- 提交平台
- 重构商品更新逻辑，优化SKU和图片校验，增强错误处理
- 站点状态
- 清洗脚本
- 修复AE无sellerSku
- 优化商品更新逻辑
- AliExpress商品信息查询接口
- 已上架排序
- 添加SKU更新功能，增强产品下架原因枚举
- 组合商品支持AE平台颜色展示
- 条码sellerSku
- 计算
- 组合商品-分页-报错
- 重构产品属性转换逻辑，优化SKU匹配和日志输出
- 国家枚举迁移到common模块
- AE币种国家抽到COMMON
- 组合商品-图片
- 组合商品-图片, 检查SPU上下架
- 优化产品属性转换和SKU构建逻辑，增强日志输出及异常处理
- 计算价格标记价格不合理
- 更新
- 全站点更新
- 添加国家列表支持并优化价格计算逻辑
- 添加速卖通运费模板、服务模板、责任人列表、制造商信息及产品分组查询接口
- 内部接口-快照条码-兼容旧条码关系结构
- 已上架-Lazada-修改库存
- 添加速卖通服务模板、运费模板、责任人信息、制造商信息及产品分组响应类
- 待上架-提交平台
- 更新速卖通产品配置，添加多项属性配置支持
- 添加商品创建/更新功能，支持分布式锁机制
- 添加营销图字段和相关逻辑，支持商品图片管理
- 已上架-编辑库存和价格
- 优化商品图片上传逻辑，支持批量处理和动态属性设置
- AE审核状态
- 组合商品-条码校验
- 更新数据库字段名称，优化品类和价格规则描述
- 修改价格库存-弹窗
- SQL调整
- Ae_sale_sku重命名字段 enable > enable_state
- 重命名字段 enable > enable_state
- 更新数据库字段名称，category_names
- 添加UAT环境下ae店铺数据初始化SQL脚本
- 添加PROD环境下ae店铺数据初始化SQL脚本
- 删除特定店铺数据以便于UAT
- 更新产品组件以处理库存和sellerSku验证
- AE更新价格/库存
- 更新销售价格和库存数量验证逻辑
- 应用更新-供货价更新
- Ae速卖通品类树，UAT和PROD
- 待上架-多站点上架, 提交平台没赋值
- 待上架-AE-导出
- Update tokenShopId for Aliexpress properties
- 初始化模板
- AE导入检查多品类
- 发布0.0.9
- 待上架-AE增加字段-产地(国家/地区)
- Add AE service 模板ID
- 已上架-AE增加字段-产地(国家/地区)
- AE只设置销售价
- 异常
- AE只设置销售价，移除划线价校验
- 更新定价规则处理，以支持多规则并改进缓存逻辑
- 通过引入 ImageCollectionDTO 和 \ImageCollectionHelper 重构图像处理，以更好地管理图像
- 待上架提交到已上架, 增加分布式锁
- 清洗数据
- AE导入, 条码
- 移除 product_picture 表中的营销图字段及相关处理逻辑
- 添加营销图片字段到 ProductManageDetailResp，并更新服务实现以支持该字段
- 店铺short_code
- AE序列化
- 添加缓存失效功能，支持清除店铺及所有相关缓存
- 更新采购价重算定价成本
- 更新库存价格
- 添加公共配置类，更新条形码导出限制逻辑，增加测试接口
- 添加商品条码导出功能，重构相关数据访问逻辑，优化尺码映射处理
- 添加入参校验结果和消息字段，优化价格计算逻辑的错误处理
- 添加入参校验结果数据类注释
- 更新条码查询和导出数据方法，增强参数校验逻辑
- 使用反射工具类优化字段查找逻辑
- 修改条码导出数据计数方法的返回类型为Long
- 添加条码查询结果按创建时间降序排序
- 添加商品ASP配置相关的数据库表和请求/响应模型
- 更新商品ASP配置相关的数据库表和请求/响应模型，添加国家类型和店铺业务类型字段
- 更新商品ASP配置相关的数据库表
- 修改商品ASP配置表中的店铺业务类型字段为tinyint类型
- 新增印花类型枚举
- 添加图案元素信息和图片标签信息集合
- 添加印花类型字段到CreateProductDto
- 更新商品ASP配置表，新增图片标签信息集合和印花类型字段
- 更新产品生产资料接口，新增批量更新功能及请求DTO
- 更新产品生产资料接口路径
- 添加产品生产资料图片字段及批量更新接口
- 单例测试-导入AE条码(仅insert)
- 待上架-导出"导入失败"数据
- 已上架-AE-更多-批量同步其他店铺
- 修复待上架-取消SKC, SKU也一起取消
- 修复AE异常列表筛选
- 批量更新-库存,AE信息
- 添加分页查询条码方法注释
- 优化SKU构建逻辑，添加属性值查找的多级匹配策略，优化校验SKU资料
- 优化图片集合处理，移除主图去重逻辑并在详情图列表中添加去重
- 企划品类支持AE
- 计算价格接口判断如果为0则返回空(数码印花不算价格, 因此不使用0)
- 添加异步更新图片库功能
- 导入怎家skc为空判断
- 添加必填属性检查功能并优化属性转换逻辑
- 上架失败日志修正店铺名
- 重构商品发布逻辑，添加公共助手类以判断数码印花类型
- 添加产品价格计算缓存Helper类并优化相关服务逻辑
- 初始化不计算价格
- AE SPU审核中状态, 不允许更新
- 修复查询问题
- 修复价格
- 初始化重算价格
- 更新产品属性处理逻辑并修复价格计算错误
- AE更改库存排除没用shopSku数据
- FirstOrNull
- 计算价格-不计算数码印花
- 导出接口增加平台参数
- 已上架-导出上架数据
- 封装 AE 查询监管属性信息
- 添加 AE 监管属性选择请求和响应DTO
- 添加 AE 类目路径更新功能及相关数据库字段
- 已上架-AE-拉取同步
- 更新 product attribute 出参入参对象
- 任务表增加平台id
- 删除注释
- AE更新价格和更新库存增加同步日志记录
- AE页面更新操作增加操作日志记录
- 待上架-AE-导出商品数据, 处理品类sheet字符规范
- 添加商品属性信息映射构建功能
- 添加AE查询监管属性请求及相关服务方法
- 生成sellerSku和barcode关系数据 - 截止0430组合商品.xlsx - 只关系表数据, 未处理saleSkc的combo, 未处理product的isError
- AE拉取同步
- Sale_goods表增加异常状态字段, 分页接口调整从product > saleGoods
- 异常商品-详情
- 异常商品-更新
- 异常商品-检查
- 异常商品-msg
- 异常商品-检查(Lazada同步)
- 异常商品-检查(test接口)
- 增加字段-控制组合商品unit是否允许修改
- AE增加操作日志: 上下架, 创建, 修改
- 上架-标题名称拼接-增加空格
- 创建商品MQ-DTO注释注解校验
- 新增计税字段
- 优化图片更新
- 修复详情增加字段
- AE同步-暂时注释图库处理逻辑
- 店铺运营类型，AE授权跳转
- 销售定价适配变量 店铺运营类型，印花类型，平台
- 销售定价适配变量优化注释
- 优化ASP配置缓存机制，增加全量配置缓存和单个配置缓存
- Lazada数码银行数据导出，SKU处理逻辑，优化SKU图片和Excel数据对象，
- 导出已上架处理组合条码
- 企划-AE预设品类
- AE拉取同步-增加字段
- Lazada拉取同步-增加平台id
- 已上架-进度分页
- 异常商品-修复检查
- 解决AE categoryCode品类赋值、查询问题
- 已上架-详情
- AE拉取
- Lzd同步
- AE拉取同步-属性
- AE拉取同步-类型查询
- 已上架-AE-计价方式
- 已上架-LAZADA
- 已上架-AE
- 添加601 fabricImgs到图像集合
- 待上架-AE
- 修复异常商品-条码处理
- Dao
- 待上架-AE-计价方式
- QX-00007958
- 添加平台导出策略及相关实现，支持Lazada和AE平台的商品导出功能
- 详情增加印花类型
- AE拉取-价格
- AE拉取-product_skc处理
- 异常商品修复-中文颜色
- 分页返回平台id
- PublishPlatformService list 返回渠道名称
- 添加可选参数param2到类目属性查询和缓存方法
- 异常修复-文案
- 添加产地属性查询功能及相关数据类
- AE拉取-尺码
- 添加产地属性查询功能及相关数据类，增加@Valid
- 更新产地属性查询接口，添加可选参数param2并修改返回类型
- 添加AE商品属性查询功能，支持发货地属性查询及相关数据类
- 添加categoryId字段到请求对象，优化属性查询逻辑
- 添加商品图套图片信息，目前用于营销图，相关数据类，增加非空校验
- 添加原产地和发货地属性值字段及相关处理逻辑
- 重构必填属性处理逻辑，添加品牌和原产地属性的构建方法
- 获取AE海关属性方法 添加品牌属性名称，重构必填属性列表获取逻辑
- 添加发货地属性到SKU构建逻辑，优化SKU属性列表
- 优化必填属性处理逻辑，简化品牌、原产地和省份属性的默认值引用
- 修改必填属性配置项名称，更新相关属性处理逻辑
- 添加图套信息处理逻辑，重构相关DTO和服务方法以支持新属性
- 优化setPictureKitListJson方法名
- 添加时间戳字段到销售商品，优化图套信息处理逻辑
- 添加刷新商品ASP配置缓存功能，重构相关服务方法解决事务问题
- 重构图片常量管理，提取图片类型、格式和后缀常量到ImageConstants类
- 优化文件扩展名获取逻辑，简化代码并处理参数
- 重构图套信息处理逻辑，移除不必要的统计和日志，简化索引获取
- 重构价格计算逻辑，移除冗余方法，使用ProductPublishHelper处理数码印花判断
- 优化类目服务和缓存管理，重构缓存清除逻辑以支持事务处理
- 重构1688类目同步逻辑，优化缓存管理和异常处理，增加事务支持
- AE批量更新
- 优化价格计算入参默认值处理
- AlibabaCategoryServiceImpl刷新缓存逻辑优化，调整类目数据同步和缓存更新流程
- 重命名构建必填属性的方法以提高代码可读性
- 添加原产地和发货地属性值支持，优化产品响应模型
- 重命名ProductAeAttributePropertyValueItemDTO为ProductAePropertyValueItemDTO，并更新相关引用以提高一致性
- 修复-组合商品colorCode使用平台颜色
- AE导出更新失败
- AE导出同步失败
- 优化图片处理逻辑，支持多个SKU图片和生成ImageAniVo对象
- 数码印花改为上游指定尺码
- 优化产品图片处理逻辑，支持空值处理以避免异常
- 拉取AE-提取sellerSku信息
- 优化商品创建/更新逻辑，增强异常处理和日志记录
- 添加非空检查以确保spu编码不为空，优化标题设置逻辑
- 添加字符串前缀检查功能，优化产品标题设置逻辑
- 添加原产地和发货地属性处理逻辑，优化商品更新流程
- AE上下架失败导出
- AE产地-导入导出
- 拉取商品数据-如果英文颜色匹配失败, 尝试缩写匹配
- 添加印花类型处理逻辑，优化商品价格计算
- 添加商品标题前缀处理和原产地、发货地属性支持，优化商品更新逻辑
- 重构商品标题构建逻辑，统一使用 ProductPublishHelper 处理标题前缀
- 重构产地属性处理逻辑，优化获取产地信息的方法
- 异常修复-平台颜色只有数据库为空才更新
- AE拉取-平台颜色优先使用属性内
- 初始化历史数据，更新商品原产地属性，优化图片处理逻辑
- AE已上架-导出下架失败
- 拉取同步不判断为组合商品类型
- 优化原产地和发货地属性处理逻辑，简化代码并增强健壮性
- 更新SKU图片DTO创建逻辑，添加销售商品信息以增强数据准确性
- 改进产品标题处理并删除过时的 SQL 更新
- 移除产品标题前缀设置逻辑，简化标题构建过程
- 更新Lazada商品创建和更新接口，使用accessToken替代shop.token
- RELEASE
- 添加商品创建任务相关功能，包括任务实体、服务和控制器
- BatchSendCallbackByCondition添加请求验证逻辑到批量发送回调功能
- 调整添加商品创建任务DDL
- 添加数字印刷客户端接口及更新推送状态逻辑
- 重构商品创建相关服务，替换为商品创建任务服务
- 更新商品创建任务实体的导入路径并调整线程池配置的队列容量
- 优化DigitalPrintingClient，升级框架到3.1.1
- 重构图片处理逻辑，使用枚举类型替代常量，简化图片集合构建
- 重命名营销规格枚举为ImageMarketingSpecEnum并更新相关引用
- 重构图片集合构建逻辑，统一使用buildImageCollection方法并更新相关引用
- 优化图片处理逻辑，使用ImageCollectionHelper构建图片集合并简化代码
- 优化产品详情图处理，确保图片URL唯一性
- 重构常量和枚举包结构，统一使用新包路径并更新相关引用
- 简化图片集合构建逻辑，合并构建和赋值操作
- 重构图片处理逻辑，使用buildImageCollection方法简化图片赋值和映射
- 重构商品同步逻辑，使用上下文对象简化参数传递和校验
- 上游更新-已上架只更新更新状态, 不更新更新人和更新时间
- 批量更新-导出-AE移除国家
- 使用LazadaSkuStateEnum替代字符串比较，优化SKU状态判断逻辑
- 批量更新-AE标题
- 批量更新-更新价格/库存, 若商品状态=审核不通过, 需更新为审核中
- 移除初始化逻辑
- 模拟添加1分钟延迟到submitAndCallback方法
- 添加1分钟延迟到createProductBy方法并移除submitAndCallback中的延迟
- 更新商品推送状态的日志记录，优化异常处理并调整创建产品的延迟时间
- 更新事务提交后回调的日志记录，增强错误信息的可读性
- 优化事务提交后回调逻辑，简化任务状态检查并增强错误日志记录
- 移除事务提交后回调中的冗余逻辑，简化任务状态检查
- 调整分布式锁的尝试时间和超时时间，优化任务并发处理
- 移除batchRetryTaskByCondition方法的事务注解
- 优化锁处理逻辑，简化任务状态更新和错误处理
- 移除createProductBy方法中的不必要的线程休眠逻辑
- 增加createProductBy方法中的必要的线程休眠逻辑
- 优化批量回调逻辑，增加重试机制并调整重试参数
- 优化事务处理和缓存逻辑，增加异步价格计算和缓存失效机制
- 更新产品创建和管理服务中的包裹重量处理逻辑
- 待上架-导入更新-不更新品类
- 商品标签DAO
- 添加CreateProductDto的校验逻辑，确保输入数据的完整性
- 注释掉批量创建产品的方法调用，改为异步初始化模板
- SkuInfoService 转 kt, PlatformSynOpeatorTypeEnum枚举类名调整
- SizeGroupService 转 kt
- BarCodeService 转 kt
- DownloadTask Java -> kt
- MqBaseListener 转 kt
- MessageRecordService 转 kt
- ImageQcRecordService 转 kt
- ImageRepositoryService 转 kt, 修复部分入参校验错误问题
- LazadaApiService 转 kt, 将部分入参fastjson注解转为jackson
- ProductPublishLazadaHelper 转 kt
- ImportProductExcelListener 转 kt
- 优化BatchGenerateBarCodeReq校验
- 添加@JsonIgnoreProperties注解以忽略未知属性，更新部分数据类的JsonProperty注解
- 替换fastjson为自定义JSON工具类，优化JSON序列化与反序列化
- 修改initConsumerRecord方法，移除返回值类型以简化接口
- 使用fastjson2替换JSON解析，保持与上游序列化一致
- 使用TransactionUtils优化异步图片更新逻辑，确保操作在事务提交后执行，优化mqBaseListener生成锁的key，考虑messageId可能为空
- 使用LockComponent替换RedissonClient实现锁机制，优化类目同步逻辑，确保并发安全
- 使用LockComponent替换RedissonClient实现锁机制；删除不必要的@JvmStatic
- 移除RedissonClient依赖，使用LockComponent进行锁机制实现
- 使用doInLock替换doInLockBy方法，简化锁机制实现
- 复制品类属性, 将已配置完成品类的属性复制到其他品类
- 复制品类属性, 将已配置完成品类的属性复制到其他品类，优化增加批量删除插入
- 优化平台属性更新逻辑，重构处理流程以支持批量更新、删除和新增属性及其值
- 优化商品主图URL处理逻辑，增加长度限制和逗号截断处理
- 使用事务后处理初始化模板，确保异步执行在提交后进行
- ImageRepositoryServiceImpl.edit 更新spucode逻辑
- 优化计算成本时，现货类型判断；updateProduct对入参判空；创建商品初始化模板(待上架)改异步为同步
- 优化商品更新逻辑，简化空数据检查并增强错误日志信息
- 标签配置查询接口
- 添加MultiFormatLocalDateTimeDeserializer以支持多种日期时间格式解析
- 优化消息记录ID获取逻辑并提取主图URL处理方法
- 调整标签展示接口
- 接口字段说明调整
- 供货价更新标识
- 使用常量ASIA_SHANGHAI替代硬编码时区，增强代码可读性
- 提取工作表名称校验逻辑到ExcelUtils，增强代码复用性
- 已上架-批量更新-导出-调整标签
- 已上架-AE-改价和详情弹窗接口
- 新增价格预警配置表、详情、预警消息实体类
- 移除PrototypeBasic
- 基础设置相关服务移除settings包
- 新增价格预警配置CRUD
- 新增价格预警配置Controller
- 价格预警配置CRUD优化
- 已上架-AE-改价
- 适配blade file；优化AE上传图片逻辑，增加AE上传图片大小限制即压缩逻辑
- 增强图像下载和压缩记录功能；更新日期格式处理功能
- 查询汇率列表sdk
- 升版本号，0.0.12-SNAPSHOT
- CurrencyExchangeRateService更新后在事务完成后清除缓存
- 定价成本计算和售价计算使用各自层级的SKC
- 重构 CurrencyExchangeRateService，在数据库操作中使用 ktQuery，并改进缓存失效日志记录
- 添加失效全部缓存的接口，并调整缓存失效逻辑
- 添加商品价格兜底判断功能，包括单个和批量检查接口
- 移除 ProductPriceAlertCheckController 中的 @Validated 注解
- 添加 Pigeon SDK 依赖并更新版本管理
- 添加 Pigeon 消息客户端及其配置属性，支持价格兜底通知功能
- ProductPriceManagementService java -> kt; 移动 SpotTypeOpsEnum 枚举到 common 包，并更新相关引用
- 将 queryExchangeRatesForInner 重新命名为 queryExchangeRates
- 上游更新-待上架-调整更新供货价/采购价, 重算定价成本和售价
- 待上架-导入数据-调整更新供货价/采购价, 重算定价成本和售价
- 已上架-应用更新-改为新的update逻辑选择器调用
- 上游更新-已上架缓存记录-改为调用选择器
- 处理依赖循环
- 待上架, 已上架
- Tmp
- 超价接口
- 已上架-应用更新-不更新供货价
- 移除旧的tag查询
- 重命名 DownloadTaskConstants 包并更新相关引用
- 添加价格信息填充和SKU检查结果创建功能
- 添加根据商品SKC ID添加标签和获取标签的功能
- 添加品类ID字段到多个响应和服务实现类
- 超价查询弹窗增加字段
- 修改价格预警检查请求和服务，使用平台枚举替代平台ID
- 去掉TODO
- 添加简化的商品价格检查功能和响应结构
- 添加价格转换功能，支持Lazada平台的货币转换
- 修复product_skc更新cost顺序
- 添加AE和Lazada价格校验组件，支持商品上架前的价格验证
- 修改价格预警配置，使用PlatformEnum替代平台ID
- 添加SKC字段并增强价格警报请求验证逻辑
- 添加combo字段以支持组合商品的处理
- 移除创建商品计算异步
- 修改currency_type字段的默认值为CNY
- 更新默认消息接收者为新的用户ID
- 审批
- 暂时移除
- 添加publishCategoryCode字段并更新相关逻辑以支持品类编码处理
- 添加异步执行器支持并优化价格校验逻辑
- 重命名价格校验组件为价格警报组件并更新相关逻辑
- 创建商品计算定价成本
- 弹窗接口标签参数调整
- 审核通过/不通过 逻辑
- 增加价格预警失效缓存接口
- 修复更新标识筛选查询问题
- 添加价格预警配置的验证逻辑并更新币种类型注释
- 更新价格预警通知内容，添加商品ID和spuCode信息；供给方式code转中文
- 超价-审核通过-不在线-重算售价
- 超价-更新采购价
- 上游更新-采购价更新
- 上游更新-重算其他skc的价格
- 超价弹窗
- 已上架-标签
- 修改 commons-io、compress依赖适配blade file
- 添加商品标题翻译功能并优化标题设置逻辑
- 修复成本更新状态
- 调整sdk
- Disabled
- 核价对接
- 添加卖家关系缓存功能并优化商品价格转换逻辑
- 移动翻译国家列表到ProductPublishLazadaHelper并优化相关逻辑
- 优化卖家关系缓存逻辑并更新版本号
- 添加店铺渠道货币类型支持并优化相关逻辑
- 优化SKU价格转换逻辑以支持大小写不敏感的货币代码比较
- 添加AE币种处理逻辑以支持美元价格转换
- 添加国家标题获取逻辑并优化商品标题翻译处理
- 标题
- 优化商品标题处理逻辑以支持空列表情况
- 添加@JsonIgnore注解以优化JSON序列化处理
- 优化国家标题获取逻辑以支持多种情况并添加翻译处理
- 优化国家标题获取逻辑以支持多种情况并添加翻译处理, v2
- 解决0527分支冲突
- 添加CheckPriceClient接口以支持批量查询价格
- 更新依赖库版本并优化依赖管理; 移除commons-httpclient依赖，请使用okhttp等
- 超价审批-固定人员id
- 超价-nacos
- 超价-重上-接口
- 标签desc移除key
- 应用更新不更新供货价和采购价
- 设计师
- 添加尺码属性值查找方法并优化尺码映射逻辑
- 添加刷新令牌和访问令牌过期时间处理
- 添加AE尺码映射和反向映射以支持尺码标准化处理
- 添加L尺码映射以支持AE尺码标准化处理
- 添加材质成分属性处理和类目属性查询接口
- 添加必填输入框属性处理以支持属性类型配置
- 添加对空卖家SKU的处理以避免无效请求
- 标签展示
- 升版本号，0.0.13-SNAPSHOT
- 更新价格字段问题
- ProductPriceAlertSimpleCheckResp 拍平SKC和SKU结果列表，优化价格校验项
- 上游增加核价类型字段
- 改价
- 改价修复
- 审批回调
- 改价-更新状态
- 改价-消除成本标签
- 改价/清仓-更新第三方
- 查询汇率列表sdk，解决冲突
- 优化buildAliExpressAddImage
- 审核模版调整
- 超价 传参调整
- 审批文案调整
- 审核不通过-下线
- 超价-没上线的SPU不执行炒超价
- 审批模板去掉店铺字段
- 改价接口-增加是否同步到第三方平台控制
- 重上
- 优化价格兜底
- 移除不必要的productSkcRepository依赖并优化价格校验逻辑
- 成本标签-打标前先清除现有标签
- 优化PriceAlertConfigurationService保存更新操作
- 审核回调
- ProductPriceAlertCheckService，目前的业务配置的价格的币种已经和sku中售价/划线价保持一致了，暂不换算
- 优化PriceAlertConfigurationService 分页
- 优化generateNotificationTitle使用枚举替换魔法值
- 移除ProductPriceBasicCalcService依赖并添加供货价字段
- 优化价格校验组件，添加获取SKC和SKU列表的方法以支持空值处理
- 弹窗-标签展示
- 更新ImageRepositoryService，优化图片处理逻辑，支持批量上传和格式校验
- 审批逻辑
- 超价-审批通过-更新skc成本更新状态为0
- 优化价格校验服务，简化SKC信息处理逻辑，支持直接使用请求中的SKC信息
- 超价-审批修复
- 优化价格校验组件，添加商品标签移除功能，支持独立事务处理
- 上游新颜色-已上架-应用更新 在线max更新所有颜色定价成本,重算所有售价
- 更新ProductPriceAlertComponen验证方法，以处理无效的 saleSkcList 和 saleSkuList
- 更新价格预警组件，优化供货价格计算逻辑，确保使用最大有效价格
- 前台链接对象增加上架状态，创建时间
- 重算售价
- 优化TransactionUtils事务工具初始化transactionManager
- 添加价格异常类型枚举及通知模板，优化价格异常通知逻辑
- 添加价格校验参数对象，优化价格校验逻辑，支持跳过校验和发送通知
- 优化标签映射转换逻辑，去重标签值并简化代码结构
- 超价-过高-先更新缓存价格字段
- 超价-过高-弹窗计算售价, 定价成本不能为空
- 超价-fix
- 超价-文案
- 超价-异常log
- 超价-清仓-下架
- 超价-列表标签
- 更新库存价格-导出-标签
- 超价-重上-修复成本更新状态
- 超价-通过-AE更新状态(上架+审核中)
- 价格兜底-更新价格通知模板移除空格
- 价格兜底-查看确认更新-供货价更新-确认更新
- 价格兜底-重上
- AE上架调用记录错误原因
- 超价
- 统计接口
- AE/Lazada上架调用记录错误原因优化
- 系统更新失败-导出controller
- 改价-多个skc时取最大定价成本
- 重上-修复sku的saleGoodsId绑定
- 系统更新失败-导出
- 清仓
- 清仓-更新skc成本更新状态
- 列表(全部/单个)-前台链接json-把is_deleted的也要展示(saleGoods的发布状态)
- 应用更新使用product_skc的价格(待上架已经更新成最新)
- 价格兜底-更新价格通知模板增加换行
- 价格兜底-lazada-单个/批量上架
- 价格兜底-批量导入改价
- 价格兜底-批量导入改价，变更为提供一个校验接口
- 系统更新失败状态
- AE批量修改分组, 移除非空校验
- 价格兜底-优化通知标粒度
- AE批量修改分组-优化更新逻辑
- AE批量修改分组-异常抛出
- AE批量修改分组-请确认SPU状态是否非审核中
- 价格判断弹窗-增加旧售价展示
- 优化商品属性的查询与更新逻辑，修复品类ID丢失问题，对齐 findPlatformAttributesByList 与 findPlatformAttributes 的行为，统一两者的数据查询结果，解决只能单方查出数据的问题。
- 上架后不处理价格兜底，只在前端做提示；优化待上架价格兜底标签
- 应用更新-2025年6月1日 00:00:00 时间之前的用旧逻辑,
- 清仓/改价/重上-标记已上架的上游更新成本价记录为已处理
- 价格不在区间统计数量(兜底标签) 区间外
- 价格兜底通知，增加图片
- 已上架商品分页-标签筛选支持saleGoodsId
- 改价/重上/清仓 增加操作日志
- 价格兜底通知，增加图片，优化图片取值
- 价格兜底通知，优化校验接口排除通过的sku，skc；优化图片增加阿里云处理参数缩小尺寸
- 优化图片增加阿里云处理参数缩小尺寸
- 价格兜底-调整比较运算符以包含边界值
- 审批流增加字段: SKC图
- 系统更新失败-增加字段
- 优化图片增加阿里云处理参数缩小尺寸，增加注释参考链接
- 审批流图片
- 超价系统更新失败-标记已处理
- 审批流-增加店铺
- 价格兜底通知，增加图片，优化通知内容换行
- 价格兜底通知，增加图片，imageMarkdown
- 优化商品属性的查询与更新逻辑，findPlatformAttributesByList 移除多余的符号
- ImageRepositoryService，优化编辑接口处理lazada、ae的url新旧数据对比
- 审批流-增加比价日志
- 图片压缩优化
- 更新SKC尺码, 增加供给方式类型匹配尺码
- 通知上游修改发货期优化
- 通知修改发货期回调ofp
- 图片压缩优化，优化变量名
- 价格兜底通知，增加图片，imageMarkdown优化
- 审批流
- 自动化标题；根据版型号获取相应信息
- 自动化标题-新增非数码印花需要使用的规则字段，元素，季节，版型
- 自动化标题-新增非数码印花需要使用的规则字段，元素，优化注释
- 自动化标题，相关表设计
- 自动化标题，相关表设计，反引号
- 自动化标题，相关表, entity，Mapper，repository
- 自动化标题，接口定义
- 自动化标题，商品标题规则服务接口实现
- 自动化标题，商品标题热词服务实现
- 自动化标题，商品标题词库字段服务实现
- 自动化标题，商品标题条件词服务实现
- 优化商品标题规则和字段服务，增加字段值长度限制及逻辑删除处理
- 修改商品标题规则查询接口为POST请求
- 商品标题规则不能为空的校验逻辑
- 自动化标题，优化，新增启用规则接口，优化词库导入
- 自动化标题，商品标题生成服务接口实现
- 自动化标题，商品标题生成服务，优化
- 满服印，图套的图片路由到50详情图
- 图包更新优化
- 图包更新优化，更新后将同名的和移除的图片从ae和lazada字段中移除
- 调用接口重新生成价格，然后和excel中根据skc匹配计算出来的价格，生成Excel到本地
- 自动化标题，款式风格名称取末级
- 自动化标题，转换为规则响应对象(包含详情)
- 兼容AE的数码印花
- 自动化标题，同步其他店铺，增加生成的预览标题
- 自动化标题，待上架-上架，不需要再次生成标题
- 自动化标题，导入；初始化标题
- 自动化标题，导出，生成标题
- 自动化标题，同步其他店铺，ae，处理批量标题
- 自动化标题，热词导入覆盖原品类的热词+合并同品类热词
- 自动化标题，根据平台限定最大长度；lazada同步其他店铺优化标题赋值
- 自动化标题，款式风格名称取末级，查询词库的时候使用全路径
- 自动化标题，增加空响应构造方法并改进供应模式验证逻辑
- 自动化标题，生成标题，获取标题字段值逻辑优化
- 自动化标题，生成标题;标题字段缺失信息, 标题是否过长变量名优化
- 执行商品任务,一个平台一个独立线程
- AE拉取同步-平台颜色优先级调整; 属性颜色>字典colorCode>sellerSku颜色
- AE拉取同步-修正平台颜色
- 自动化标题，生成标题，响应增加主图url
- 自动化标题，生成标题，Illegal overloaded getter method with ambiguous type for property 'generatedTitleMissingFieldsJson'
- 自动化标题，标题规则清除缓存逻辑优化，使用选款店铺生成标题
- 自动化标题，通用的词库翻译值获取方法。根据不同字段查询方式（按ID或按Value）统一处理获取逻辑。
- 自动化标题，详情页更新，新增标题字段：缺失信息，标题是否过长
- 自动化标题，构建商品标题通用规则，把前缀判定改为包含
- 调整 Guava CacheBuilder 缓存时间
- 自动化标题, 增加生成记录；待上架详情增加出参标题字段：缺失信息，标题是否过长
- 自动化标题, 增加生成记录；DDL
- 自动化标题, 增加生成记录；缺失字段存code
- 自动化标题, 优化lazada翻译
- 自动化标题, 调整ProductTitleFieldValueInfo 包路径
- 自动化标题, 调整ProductTitleFieldValueInfo 类名加上DTO
- 自动化标题, 图片标签信息集合反序列化问题
- 自动化标题, 增加移除字段记录
- 自动化标题, 优化变量名
- 自动化标题, 优化条件词判重
- 自动化标题, 优化词库翻译入参校验
- 自动化标题, 增加袖长
- 自动化标题, 标题字典/标签数据初始化服务
- 自动化标题, 数码印花版型相关字段初始化
- 商品价格兜底服务前端调用接口，入参中SKC和SKU有效性过滤，优化参数校验逻辑
- 图库管理，优化批量替换同编号文件替换
- 查看确认更新-供货价更新-弹窗，NPE优化
- 查看确认更新-供货价更新-弹窗，NPE优化，maxOrNull()
- 图库对接视觉，监听器，回调，前端手动调用接口
- 图库对接视觉，先注释非视觉回查的其他图片管理创建/更新
- 图库对接视觉，移除图片QC相关controller接口
- 图库对接视觉，监听器，回查引入重试机制
- 图库对接视觉，监听器，回查引入重试机制，提取方法
- 图库对接视觉，监听器，回查引入重试机制，调整同集群内部服务重试参数
- 封装接口
- 修改类定义
- 修改字段定义
- 处理过时方法
- Temu站点和语言配置获取接口
- 获取平台品类树
- 属性
- 代码优化调整
- 代码优化
- Bugfix
- 获取平台类目构建缓存
- 优化类目&属性获取
- 类目路径加上根路径
- 上游更新-缓存Temu记录
- 仓库列表
- 类目从上一级目录开始
- TEMU-增加日志,调整枚举
- 属性处理修改
- 修复空的场景
- 失败日志使用temu枚举
- 店铺管理同步eis
- 校验必选属性
- 销售价格，店铺管理，新增店铺所属主体
- 店铺管理，增加注释
- 店铺管理，优化校验，temu时店铺主体不能为空
- 推送灵感
- 补充方法
- 移除lazada相关注入
- 使用常量
- 调整try
- 创建成功的信息更新到sale
- Temu创建商品成功后逻辑-更新PID
- 已上架-返回标题
- Temu常量类-前台链接
- 已上架-列表-颜色,尺码
- 已上架-列表-颜色,尺码,店铺模式
- 上架-属性空指针
- 增加包装校验
- 已上架-分页-站点
- 已上架-统计
- 上架-价格不写死, 使用待上架价格
- 上架-增加入参校验
- 上架-价格取货币
- 上架-extCode
- 上架成功后-更新平台skcid和平台skuid
- 上架后通知款式开发
- 异步
- 增加skc价格,实现履约查询Temu条码
- 上游更新-TEMU实现
- 定时同步最新商品生命周期
- Temu商品链接(当前写死美国)
- 修改temu的name
- 申报价格
- 已上架增加选品状态参数
- 导出上架失败
- 初始化补充temu模版skc赋值
- Temu使用自己的properties
- 清洗product信息接口
- 计算产品价格，调整支持Long型条件，调整规则组缓存排序为创建时间
- 修复枚举使用错误
- 分页查询-增加标签返回
- 计算产品价格，调整，遍历价格规则组
- 计算产品价格，遍历价格规则组，调整注释
- 备注
- 计算产品价格，调整入参，增加平台参数作为条件
- 待上架-导出"导入失败"
- 待上架-导入导出
- Temu价格兜底组件
- BusinessType枚举
- Temu价格兜底，temuUpdateProductComponent.updateProduct调用前触发
- 计算产品价格，调整入参，增加店铺运营模式作为入参，调整calculatePrice入参抽出对象
- Temu价格兜底，TemuProductPriceAlertComponent查询temuSpuId
- 已上架-申报弹窗
- Temu价格兜底，待上架列表查询标签
- 上架模板-平台颜色
- 上架模板-平台颜色-使用中文颜色
- 上架模板-skc图片
- 已上架-修复商品名和店铺模式筛选
- 特木属性
- 已上架-平台SPU ID和SKC ID筛选
- 已上架-筛选-店铺主体
- 自动化标题，temu导出，详情
- 已上架-筛选-平台id
- 待上架-导出数据(Temu平台结构)
- 导出数据-图片
- 导出数据-单位转换运算
- 待上架-导入-更新价格
- 转换
- 图片压缩方法统一输出格式
- 图片压缩方法统一输出格式; 自动化标题，Temu，productNameEn、productName同时使用
- 自动化标题，Temu，productNameEn、productName同时使用
- 增加Temu状态查询记录表
- SKU分类code转name
- Fix platform
- 导出数据-详情图
- 导出数据-价格为空则计算
- 导出Temu结构-图片分隔符,改为|
- 导入更新产地
- 导出数据-不计算建议零售价
- 上架失败记录日志调整
- 导入导出改为skc维度
- 统计
- 导出库存
- 移除导出宽高固定长度
- 升级eis正式版
- 已上架-分页-尺码排序
- 升级eis版本
- 导入-上架模板
- 导出使用模板填充
- Temu价格兜底，TemuProductPriceAlertComponent查询temuSpuId，一个productId有全托半托两个TemplateTemuSpu，需要根据saleGoods的运营模式反查
- Temu上游更新
- 导出属性
- 导入新增SKU默认美国站点
- 品类筛选增加平台
- 导入导出-增加素材语言
- Temu详情更新
- 导出无版型数据
- 更新 ofp-sdk version 0.0.4 正式包
- Eis-center-sdk 升级为 0.0.3
- 图库对接视觉，移除ProductCreateServiceImpl图片相关写入，移除CreateProductDto 101-601、skc图片字段
- 创建商品任务，ProductCreateTask未赋值的字段会被设置为 data class 的默认值
- 创建商品任务，ProductCreateTask未赋值的字段会被设置为 data class 的默认值, retryCount
- ShopRepository, 优化getByShopName，不同平台可能有同样的店铺名
- 图库对接视觉，抽出createProductBy方法到ProductCreateTaskExecutor
- 图库对接视觉，移除回调通知商品创建MQ消息发送
- 图库对接视觉，创建商品后，异步同步视觉中心图包
- 图库对接视觉，增加日志输出调用视觉中心接口响应
- 图库对接视觉，现货类型校验isNullOrBlank
- AE/Lazada: 修改ae平台属性、选项等缓存时间,上架skc图片强校验
- 图片管理，保留更新、编辑接口兼容导入商品上架的情况
- 优化：Lazada和AE状态 POP与三方不一致，AE定时任务, 同步商品状态
- 优化：Lazada和AE状态 POP与三方不一致，AE定时任务, 同步商品状态，修改方法名
- 更新待上架颜色-改为批量更新
- 优化：Lazada和AE状态 POP与三方不一致，AE定时任务, 同步商品，增加hscode，调整方法名/路径名
- 优化：Lazada和AE状态 POP与三方不一致，AE定时任务, 同步商品，优化查询重试，优化日志输出
- 优化：AE商品同步逻辑，增加限流机制，重试策略调整，更新方法名以反映部分同步
- 添加商品同步时间记录，更新商品状态和HS Code时避免触发拦截器
- 增加绑定关系test接口
- 优化待上架商品查询，重构标签过滤逻辑，使用SQL片段提高可读性
- 添加AE(AeSaleGoods)商品未找到标记
- 优化Lazada/AE商品状态定时同步，提升POP与三方平台一致性，新增lazada
- 优化Lazada/AE商品状态定时同步，提升POP与三方平台一致性，新增lazada，优化方法名
- AliExpress服务接口，封装设置商品分组
- 增加单例模式相关测试，批量设置AE分组
- AE速卖通创建/更新商品，设置商品分组
- AE速卖通创建/更新商品，优化设置商品分组
- 优化Lazada/AE商品状态定时同步，提升POP与三方平台一致性，新增lazada，优化更新sku关联字段用skuId
- 优化Lazada/AE商品状态定时同步，提升POP与三方平台一致性，新增lazada，优化日志输出；优化temu拉取商品的最新库存入参可空
- 优化Lazada/AE商品状态定时同步，提升POP与三方平台一致性，新增lazada，优化日志输出；优化定时任务入参方便人工运维调用
- 优化Lazada/AE商品状态定时同步，提升POP与三方平台一致性；优化分页，使用游标
- Temu详情, 返回品类id
- SKU多发货地 API
- AE待上架-组装API参数-支持多发货地
- AE待上架-上架-发货地匹配
- 优化Lazada/AE商品状态定时同步，提升POP与三方平台一致性；优化分页，增加is_deleted
- 优化Lazada/AE商品状态定时同步，提升POP与三方平台一致性；优化shop token授权判断
- 优化Lazada初始化品牌，解决SQLIntegrityConstraintViolationException
- 优化Lazada初始化品牌，优化日志内容
- 优化Lazada初始化品牌，优化日志内容，增加开始，结束日志
- 待上架、已上架增加商品款式信息
- 报错fix
- 增加更新商品款式信息
- 增加企划类型
- AE导出过滤无用字段
- 升级正式版
- *(ae)* 重构发货地属性存储方式，从JSON改为字段存储 在ProductTemplateAeSku和AeSaleSku实体中： 1. 移除shipsFromPropertyValueItem JSON字段 2. 新增4个字段存储发货地属性信息 3. 修改相关业务逻辑适配新字段 新增ProductTemplateAeSkuRepository.listByAeSpuIds方法用于查询发货地信息
- *(product)* 上游更新商品-更新尺码-兼容多发货地
- *(product)* 新增发货地属性支持SKU查询与创建 修改ProductTemplateAeSkuRepository查询方法，增加发货地属性参数。在AeTemplateUpdateServiceImpl和ProductManageServiceImpl中完善SKU创建逻辑，支持多发货地场景。
- *(product)* 添加 AliExpress 多发货地支持 - 新增代码提取 AE 发货地并处理多发货地逻辑 - 在 SKU 创建和更新中增加发货地相关信息- 优化了 SKU 生成和更新的逻辑，支持多发货地
- *(settings)* 新增图包规则管理功能 新增图包规则相关枚举、DTO、请求响应对象、Controller及Service 新增图包规则相关数据库表
- *(settings)* 新增商品标题配置功能 - 添加商品标题配置相关SQL表结构 - 新增Controller、Req、Resp类
- *(docs)* 更新DDL.sql文件中的数据库表结构 - 修改image_pack_rule表结构，移除AUTO_INCREMENT属性 - 删除部分索引，优化表结构 - 移除image_pack_rule_shop表中的shop_name字段 - 新增商品标题配置相关表结构
- *(product)* 添加 SKU 发货地属性 - 在 AeOriginalProductSku 模型中添加发货地相关字段 - 在 AeProductSkuSyncComponent 中处理发货地属性的同步 - 在 AeProductSyncComponent 中获取和设置发货地属性
- *(product)* 批量更新发货地功能 - 修改 AeBatchUpdateAeInfoReq 中 shipsFromPropertyValueItem 为 shipsFromPropertyValueItems，支持多个发货地 - 实现批量更新发货地的逻辑，支持单个发货地的更新 - 增加日志记录和判断逻辑，处理多个发货地和发货地一致的情况
- 实现ImagePackRuleService
- *(settings)* 重构基础配置模块控制器注释和功能 - 统一各控制器注释前缀为"基础配置" - 删除ProductTitleConfigController并将其功能迁移至ProductTitleRuleController - 重命名ProductTitleConfig相关请求类 - 修复ImagePackRuleController的日志输出问题 - 移除部分控制器的冗余注释
- *(settings)* 新增图片类型枚举并优化规则服务逻辑 - 新增ImageDetailTypeEnum枚举类 - 重构ImagePackRuleServiceImpl使用枚举替代硬编码 - 优化ShopRepository添加getShopMap方法 - 移除Controller中的日志代码
- *(settings)* 实现商品标题配置功能 新增商品标题配置相关实体、仓库和服务 实现配置创建、规则应用、店铺应用等功能 完善分页查询和详情获取逻辑
- *(ae)* 实现动态图包规则功能 新增ImagePackageCollectionDTO和ImagePackageRuleHelper，重构图片处理逻辑以支持动态图包规则。主要变更包括： - 新增图包规则数据结构 - 实现规则缓存和查询 - 重构AE商品图片处理逻辑 - 调整ImagePackTypeEnum顺序 - 新增单位数图片编码支持
- *(ae-product)* 重构图包规则功能并更新相关字段 - 重命名ImagePackageCollectionDTO为ImagePackCollectionDTO - 更新AeSaleGoods实体类中的ruleVersion字段为imagePackRuleVersion - 修改AeUpdateProductComponent中的图包规则处理逻辑 - 更新ProductAeDetailResp中的图片相关字段为imagePackCollection - 调整SQL脚本中的字段名称
- *(ae-product)* 新增ImagePackInnerCollectionDTO并重构图片处理逻辑 重构图片上传逻辑，使用内部DTO统一处理图片分类和规则映射： 1. 新增ImagePackInnerCollectionDTO包含完整图片分类 2. 重构ImagePackCollectionHelper支持内部DTO处理 3. 修改AeUpdateProductComponent适配新图片处理流程 4. 更新ImageConstants添加图包规则映射
- *(repository)* 统一参数命名从sqlClient改为sql 主要修改了多个Repository类中的构造函数参数命名，将sqlClient统一改为sql，保持代码风格一致。涉及ProductTitleConfig、ProductTitleConfigRule、ProductTitleConfigShop和ImagePackRule等Repository类。
- *(图包规则)* 更新缓存键前缀并优化图包集合处理逻辑
- *(ImagePackRule)* 新增批量查询图包规则功能 refactor(enums): 重命名图片相关枚举类 - 将ImageDetailTypeEnum重命名为ImagePackDetailTypeEnum - 将ImageDisplayOrderEnum重命名为ImagePackDisplayOrderEnum - 将MainImageTypeEnum重命名为ImagePackMainTypeEnum
- *(商品管理)* 新增历史风格数据初始化功能 在MarketStyleComponent中新增getMarketNameMap方法 新增StyleHisDataUploadExcelDTO用于Excel导入 在ProductManageService中实现initHisDataV2方法 ProductController新增初始化接口
- *(product-title)* 新增多标题生成功能 - 新增ProductTitleConfigMultiGenerateReq/Resp等DTO - 实现根据配置生成多标题逻辑 - 修改SPU表结构存储多标题数据 - 更新商品管理服务使用多标题生成 - 新增相关API接口
- *(database)* 添加发货地属性相关列 - 在 product_template_ae_sku、ae_sale_sku 和 ae_original_product_sku 表中添加发货地属性相关列 - 新增列包括：ships_from_attribute_id、ships_from_attribute_name、ships_from_attribute_value_id 和 ships_from_attribute_value_name- 这些列用于存储发货地的属性信息，以便在产品标题配置中使用
- *(图包)* 新增规则变更状态字段并优化逻辑 在ImagePackCollectionDTO和ImagePackCollectionResp中新增imagePackRuleChanged字段，用于标识图包规则是否变更。调整AeUpdateProductComponent和ImagePackCollectionHelper相关逻辑，使用新字段判断规则变更。移除ProductAeDetailResp中冗余的imagePackRuleChanged字段。
- *(product)* 修改品类查询为使用编码列表 将品类名称查询改为多级品类编码查询，格式如01-0101-010102，并更新SQL映射文件
- *(ImagePackRule)* 新增默认规则功能 - 在ImagePackRule实体及相关DTO中新增defaultRule字段 - 新增设置默认规则接口及实现 - 修改规则逻辑以支持默认规则 - 更新数据库表结构增加default_rule字段
- *(pop-product-service)* 同步 AE 商品 SKU 信息并处理 SKU 差集 - 优化了产品款式类型和图片状态的同步逻辑 - 新增 SKU 差集处理逻辑，禁用已不存在的 SKU - 添加了 AE SKU 更新逻辑的注释说明
- *(product)* 新增批量生成商品多标题功能
- *(product)* 在创建产品任务中添加事务后提交视觉图片同步 在ProductCreateTaskExecutorImpl中，使用TransactionUtils确保视觉图片同步在事务提交后执行
- *(product)* 新增产品创建时刷新图包状态功能
- *(product-title)* 修改标题序号从1开始为0开始 修改标题生成逻辑及相关DTO、实体类，将标题序号从1-based改为0-based索引，包括： 1. 修改ProductTitleGenerateServiceImpl生成逻辑 2. 更新相关DTO和实体类的字段注释 3. 调整SQL表结构注释 4. 添加标题配置ID字段 5. 优化错误处理逻辑
- *(实体类)* 将多个实体类的tenantId字段改为可空类型 fix(ImagePackRuleServiceImpl): 使用枚举替换硬编码值 chore(versions.properties): 更新nacos元数据版本至20250711
- *(settings)* 优化渠道和平台名称显示逻辑 修改ImagePackRule和ProductTitleConfig相关实体及服务，将channelId和platformId改为非空类型，并统一通过枚举获取名称显示
- *(product)* 新增款式类型和图包状态字段 在ProductAeServiceImpl、ProductLazadaServiceImpl及相关响应类中新增styleType和imagePackageState字段，用于记录产品款式类型和图包状态信息。同时在SaleGoodsMapper.xml中添加对应字段查询。
- *(导出功能)* 重构系统更新导出功能，支持按状态筛选 - 重命名 ExportByUpdateFailReq 为 ExportByUpdateReq - 新增 updateState 字段支持按状态筛选 - 修改相关服务接口及实现类 - 更新 DownloadTaskTypeEnum 枚举定义
- *(ImagePackRule)* 重构验证逻辑并优化实体类生成策略 - 将ImagePackRuleReq.validate改为成员方法 - 调整图片类型验证错误提示 - 统一实体类ID生成策略为IDENTITY - 优化图片打包规则保存逻辑
- *(product)* 修改平台ID类型为枚举并更新SQL映射 修改ProductManagePageReq.kt中platformIdList类型为PlatformEnum 更新ProductMapper.xml中对应SQL查询条件
- *(ImagePackRule)* 修改保存接口路径及默认规则校验逻辑 - 将/save接口改为/save-or-update - 修复默认规则校验逻辑错误 - 优化规则编码查询为直接计算最大值
- *(商品标题)* 新增多标题选择功能及兼容处理 - 新增ProductTitleSelectionHelper用于根据店铺选择标题 - 在AE/Lazada/Temu组件中集成标题选择逻辑 - 在各平台请求/响应DTO中添加generatedTitlesJson字段 - 在商品详情服务中添加多标题兼容处理逻辑
- *(ProductTitleConfigService)* 添加配置更新逻辑 feat(ImagePackRuleService): 修改规则保存模式为INSERT_ONLY
- *(product)* 更新平台ID为枚举类型并调整相关逻辑 - 将ProductTitleConfigMultiGenerateReq中的platformId改为PlatformEnum类型 - 修改ProductManageServiceImpl和ProductTitleGenerateServiceImpl中平台ID的处理逻辑 - 更新versions.properties中的版本号
- *(product)* 为buildCompatibleTitlesJson添加平台参数支持
- *(ProductPublishAeHelper)* 添加SKC图片处理逻辑及异常类型优化 fix(AeUpdateProductComponent): 增加SKC图片处理逻辑 refactor(ImagePackCollectionHelper): 优化SKC图片处理代码结构 chore(versions.properties): 更新版本号至20250713
- *(ProductTitleConfigServiceImpl)* 添加channelId字段并修改保存模式 fix(ImagePackCollectionHelper): 优化平台不存在时的错误提示信息 chore(versions.properties): 更新nacos版本号至20250714
- *(product)* 更新产品管理分页查询逻辑 - 在ProductRepository.kt中禁用分页count优化 - 修改ProductMapper.xml中平台ID查询条件为JSON_CONTAINS - 更新versions.properties中的Nacos版本号
- *(product)* 在AePublishProductGeneralTemplateExcelDTO和ImportAeProductDTO中添加新字段 在AePublishProductGeneralTemplateExcelDTO中添加款式风格、货盘类型等字段 在ImportAeProductDTO中添加发货地、款式类型等字段
- *(ae-product)* 支持多标题和多发货地价格导出与导入
- *(export)* 重命名系统更新导出组件并更新枚举引用 将SystemUpdatePlatformFailExportComponent重命名为SystemUpdatePlatformExportComponent，并同步更新DownloadTaskTypeEnum中的引用
- *(product)* 新增速卖通商品导出模板字段 在ProductPublishAeExportTaskStrategy中新增款式风格、货盘类型等字段映射，并修改相关DTO和SQL查询以支持新字段导出
- *(component)* 重构图片版本号更新逻辑至各平台更新组件 将图片版本号更新功能从CallAeComponent和CallTemuComponent移除，统一在AeUpdateProductComponent、TemuUpdateProductComponent和LazadaUpdateProductComponent中实现。同时在ProductAeRegulatoryServiceImpl中优化商品标题获取逻辑，支持从模板生成标题作为备选。
- *(product)* 新增AE商品查询数据导出功能 新增AE商品查询数据导出功能，支持根据分页查询条件导出商品数据到Excel
- *(product)* 优化Lazada商品标题生成逻辑 - 移除多标题生成功能，改用单标题预览 - 更新标题选择流程，简化逻辑层级 - 删除相关字段generatedTitlesJson
- *(ProductTaskController)* 简化商品创建任务Controller的注释
- *(export)* 使用平台特定标题长度限制替换通用常量 修改了多个导出组件和服务实现，将硬编码的DB_MAX_TITLE_LENGTH替换为通过ProductInnerConstant.getTitleMaxLength()获取的平台特定标题长度限制，支持不同平台的差异化配置。
- *(ProductTitleGenerateServiceImpl)* 简化标题配置查询逻辑并优化规则匹配 移除店铺ID条件查询标题配置，优化规则匹配流程，增加fallback机制
- *(export)* 添加今日时间条件处理到AE查询数据导出
- *(product)* 在ProductTitleConfigGenerationItemDTO中添加errorMessages字段 修改标题生成项DTO，新增错误信息列表字段，并调整empty方法的实现
- *(product)* 优化AE模板商品发货地处理逻辑并新增旧SKU禁用功能 在ProductManageServiceImpl中优化了AE模板商品的发货地处理流程，新增了当从单发货地改为多发货地时自动禁用旧SKU的逻辑
- *(product)* 更新版本号至20250715并优化产品ID处理 修复TestInnerController中产品ID的空安全处理，移除不必要的类型转换
- *(product)* 为Temu平台添加英文标题支持 在ProductTitleConfigGenerationDTO中新增titleEn字段，并在相关服务实现中处理英文标题逻辑
- *(product)* 支持多颜色处理及发货地逻辑优化 1. 在ProductManageServiceImpl中重构颜色处理逻辑，支持多颜色组合 2. 优化发货地处理流程，增加ID比对和旧SKU禁用逻辑 3. 移除ImportAeProductDTO中冗余的颜色格式化方法
- *(ImagePackCollectionHelper)* 移除图片URL去重逻辑并优化图片数量控制
- *(ProductTitleGenerateServiceImpl)* 为店铺同步添加严格校验模式
- *(product-import)* 支持按任务ID处理排队中的商品导入数据 - 修改 ProductImportService 接口，增加 taskIds 参数 - JobProductController 同步接口支持传入 taskIds - 优化 ProductImportServiceImpl 处理逻辑，支持指定任务或全部排队任务 - 修复 ProductTemplateAeSkuRepository 查询条件问题 - 优化 ImportAeProductDTO 的 JSON 序列化配置
- *(product)* 新增颜色解析方法并优化SKC颜色处理逻辑 在ImportProductDTO和ImportTemuProductDTO中新增getParsedColorList方法，用于解析颜色字符串。优化ProductManageServiceImpl中的SKC颜色处理逻辑，支持多颜色组合处理，并改进错误提示方式。
- *(ae-product-sync)* 新增发货地属性映射功能 在AeProductSyncComponent中增加获取发货地属性的方法，并在同步商品信息时使用该属性映射
- *(图包规则)* 添加店铺规则冲突校验功能 新增店铺规则冲突校验逻辑，包括： 1. 启用规则时校验店铺是否已有其他启用规则 2. 应用规则时校验店铺冲突 3. 新增查询店铺已有规则接口 4. 相关请求响应类及仓库方法
- *(ImagePackRule)* 增强店铺规则查询功能，支持多条件筛选和店铺详情返回
- *(产品价格库存)* 添加发货地字段支持 • 在ProductPriceInventoryDto中新增shipsFrom字段 • 更新批量更新服务以处理发货地字段 • 修改Excel导入DTO和SQL查询包含发货地信息 涉及产品价格库存相关功能的发货地信息扩展
- *(产品批量更新)* 添加发货地字段支持 • 在批量更新服务中增加shipsFrom字段处理逻辑 • 更新实体类ProductBatchUpdatePriceInventoryTask添加shipsFrom字段及数据库映射 • 同步修改失败DTO和导出组件以包含发货地信息 涉及文件： - ProductBatchUpdateServiceImpl.kt - ProductBatchUpdatePriceInventoryTask.kt - ProductBatchUpdatePriceInventoryFailDTO.kt - TaskUpdatePriceInvExportComponent.kt
- *(缓存)* 将图片打包规则缓存键常量迁移至RedisCacheConstants
- *(数据库)* 为表添加新字段 • 在图包规则表增加默认规则字段 • 在产品批量更新价格库存任务表添加发货地字段 修改说明: - 产品任务表新增ships_from字段记录发货地信息
- *(product)* 优化getImagesV2方法逻辑并更新版本号 简化ProductManageServiceImpl中getImagesV2方法的条件判断
- *(product)* 优化发货地属性处理逻辑 重构 ProductManageServiceImpl 中发货地属性处理代码，提取 buildShipsFromName 方法，简化比较逻辑。同时补充 ProductAeService 的方法注释。
- *(ci)* 添加基础GitLab CI配置 • 新增.gitlab-ci.yml文件作为CI流程入口 • 引用devops/ci-templates项目中的pipeline模板 • 使用main分支作为模板基准版本 说明：初始化项目持续集成配置，为后续自动化流程建立基础框架

### 🐛 Bug Fixes

- 商品接口
- 上架接口
- Pull-parser重写javax.xml.parsers.SAXParserFactory导致xml解析异常，Caused by: org.xml.sax.SAXNotRecognizedException: unrecognized feature http://xml.org/sax/features/external-general-entities
- 商品条码
- 修改库存接口
- 修改库存接口。
- 商品导入导出
- 商品导入导出属性问题
- 商品导入导出属性问题.
- 上架调试
- 上架调试.
- 库存接口
- Skc
- 去除openApi
- 店铺相关
- 授权相关
- 商品相关
- 条码查询
- 查询
- 上架调试..
- 上架调试...
- 编辑商品
- 店铺授权
- 授权
- 条码
- 修改库存
- *(pop-product-service)* 修复图片下载任务创建临时目录的问题 - 修改了 ImageDownloadHelper 中的文件名生成逻辑，使用原始图片名称而非 URL - 更新了 ImageRepositoryServiceImpl 中的日期格式，使用 PURE_DATETIME_PATTERN 替代 NORM_DATETIME_PATTERN - 调整了创建临时目录的逻辑，增加了额外的目录层次 -优化了 ProductPublishTaskExportServiceImpl 中临时目录的创建方式
- Inner api
- Sdkc
- User问题
- *(pop-product-service)* 修复 SKU 编码规则元素查询功能- 将查询条件从 SkuRuleElement::skuRuleElementId 修改为 SkuRuleElement::skuCodeRuleId - 确保根据正确的字段获取 SKU 编码规则元素列表
- 品牌列表
- 修复字段问题
- 导入商品
- 企划和同步品类
- 上架
- 上架.
- 错误日记
- 属性分页
- 企划名称长度最大50限制
- 企划日期校验
- 错误日记。
- 错误日记。.
- 颜色问题
- 企划字典
- 错误日志提示
- 调整同步更新品类
- 增加企划创建的唯一校验
- 操作日记
- 上架颜色问题
- 企划计算占比
- OpenAPi和上架
- OpenAPi和上架。
- Json序列化注解
- Json序列化
- 删除顺序
- 上架问题
- Sql问题
- 上架问题.
- 建品SDK
- 时间格式化
- 企划复制-货盘
- 企划复制-增加唯一校验
- 花型保存
- 渠道问题
- 用户问题
- *(product)* 修复商品平台同步状态逻辑 - 修正了平台同步失败数量的统计逻辑 - 优化了代码拼写，将 'FAILD' 改为 'FAILURE'
- 消费者增加系统用户 异常订单,更新Lazada价格
- 商品列表排序问题
- 商品列表排序问题.
- 价格异常
- Size chart问题
- 列表查询问题
- 主图问题
- Job
- 价格不合理问题
- 导出问题
- 同步成功日记
- Create product sdk
- Create product sdk.
- 价格问题
- 保存sdk数据
- 上架问题和分页
- *(product)* 修复商品下架时同步结果更新逻辑 -将 updateProductPlatformSyncResult 的调用参数从 true 修改为 false
- 企划汇总更新校验是否清空其他tab
- 上架分页问题
- 数码印花问题
- 查询问题
- 上架查询
- 导入更新问题
- 商品数量统计
- 上架翻译问题
- 商品查询和导入相关
- 条码生成问题
- 应用更新逻辑
- 上架 问题和导入价格
- *(pop)* 修复 QC 任务管理导出 Excel 文件名缺失后缀的问题 - 在生成 Excel 文件名时，增加了 ".xlsx"后缀 - 确保导出的文件具有正确的扩展名，便于用户识别和打开
- 上架初始化goods问题
- 上架去图片校验
- 上架去图片校验。
- 应用更新问题
- 应用更新标识
- 新增SKC问题
- 初始化尺码问题
- 导出待上架和查询
- 导出待上架和查询。
- *(product)* 修复删除规则组时的逻辑错误 -增加了对规则组存在性的检查，如果不存在则抛出异常 - 在删除规则组前，
- 企划日志
- 推送计算价格
- *(pop-product-service)* 修复镜像仓库更新逻辑 - 将 ImageRepository 对象中的 isUpdate 字段改为 update 字段 - 更新相应的 getter 和 setter 方法
- 推送计算价格.
- 新增SKC情况
- 查询SDK
- 查询和计算价格问题
- 修改价格过滤
- 计算价格问题
- 计算价格问题。
- *(product)* 修复价格计算中的默认值问题 - 将汇率缓存的默认值从 1 修改为 0 -调整 ISP 和 SP 倍率的默认值为 0- 修改固定值计算的默认值为 0 - 更新版本号至 20241220
- 计算价格问题。.
- 列表更新标识
- 列表更新标识和导出问题
- 新增SKC 尺码取并集
- 品牌问题
- 品牌问题。
- 价格编辑问题
- *(pop-product-service)* 修复下载任务页面状态描述显示问题 - 将 Optional.ofNullable(task.getTaskType()) 修改为 Optional.ofNullable(task.getStatus()) - 通过 DownloadTaskStatusEnum 获取状态描述，并设置为 task 的 statusName 属性
- 价格计算问题
- 推送图片问题
- 修复企划和异常订单bug
- 企划-平台和渠道查库存name
- 异常订单导出-美化
- 同步店铺问题
- 修改价格-sku不存在
- 同步店铺问题。
- 取消skc问题
- 价格更新记录和供货价价格问题
- 发布状态问题
- 跨境价计算问题
- Sku状态问题
- 发布标题问题
- 库存统计
- 竞品价格
- 新增设计组 保存选款站点
- 商品查询问题
- 商品查询问题.
- 花型查询
- 花型查询。
- 花型查询依赖
- 圖片問題
- 导入颜色解析问题
- 上架同步skc图
- 图片异常原因
- *(pop-product-service)* 修复类目映射导入功能中的数据查询问题 - 为 PublishCategoryMapping 和 PublishAttributeGroup 查询添加排序和限制条件 - 优化日志记录格式 - 移除不必要的空格和换行符
- 导出条码模板
- 更新价格失败日志-平台名称
- 全局异常提示站点
- 发布异常问题
- 克隆店铺问题
- 初始化sku默认尺码为上架
- 初始化sku默认尺码为上架.
- 上架标题
- 修改价格-兼容未上架空数据
- 编辑库存问题
- *(shop)* 修复 Lazada 品牌国家代码处理- 在创建 LazadaBrand 对象时，将国家代码转换为小写 - 使用 Locale.ENGLISH确保在所有环境中一致转换为小写
- 返回供给方式
- 历史数据相关
- 商品查询
- 历史数据处理
- 历史数据处理.
- 历史处理——枚举
- 历史处理
- 历史处理sku为空的情况
- *(product)* 修复产品属性更新逻辑 - 在更新产品属性前，先删除 Lazada 平台的原有属性 - 使用 delete语句提高更新效率
- 历史处理.
- 历史处理,记录拉取记录
- *(pop-product-service)* 优化平台品类路径生成逻辑 - 修改平台品类路径的连接符为 ">"- 增加对平台品类路径为空的检查 - 更新错误信息提示 - 修正映射关系的唯一键生成逻辑
- 商品数据拉取
- 商品详情
- *(pop-product-service)* 修复删除产品价格规则组时未检查关联规则问题 - 在删除产品价格规则组前，先检查该组下是否有有效的规则 - 如果存在有效规则，则不删除规则组并返回 false - 优化了代码中的repository调用，使用正确的repository进行删除操作
- 拉取商品SKC问题
- 去除main方法
- 颜色获取问题
- 品牌查询
- 标准尺码
- 标准尺码修改
- Mq交换机
- 修复数据颜色
- 优化商品查询
- 优化商品查询,库存问题
- 优化商品查询,库存问题.
- *(product)* 优化图片下载异常处理 - 在 imageRepository 方法中增加了对图片下载失败的异常捕获和日志记录 -优化了错误日志的输出格式，增加了异常信息 -调整了部分代码缩进，提高了可读性
- 修复商品状态
- 修复错误数据.
- 修复错误数据.。
- 条码生成规则
- 条码生成规则。
- 条码生成规则,bycode
- 条码根据code生成
- 分页刷新状态
- 分页商品刷新状态.
- 拉取商品价格问题。
- 修复历史条码
- 修复商品条码
- 重发消息方法
- 拉取数据问题
- *(product)* 修复商品图片命名逻辑 - 修复主图命名从 0 开始的问题，改为从 1 开始 -优化详情图命名逻辑，使用递增的索引
- 刷新品类
- History api
- 拉取商品
- 导出无价格
- 拉取skc问题
- 包裹重量
- 导出包裹重量
- 导出包裹重量.
- 拉出数据取平台颜色
- 刷新条码分页
- 上架no item问题
- 上架 ID问题
- 上架 ID问题。
- 修复json比较存在null的情况
- 上架 ID设置
- 拉取商品SKU数据
- 导入条码问题
- 取消SKC上下架问题
- Redis 锁的问题
- *(product)* 修复商品数据错误时错误信息未重置的问题- 在更新商品数据时，将错误信息字段重置为空数组字符串 - 确保商品数据修复后，错误信息也能正确清空
- BUG FIX
- 取消尺码问题
- 条码复制sellerSku问题
- 条码复制sellerSku问题.
- 商品分页查询
- 下架问题
- 下架问题.
- 记录更新记录
- Offline
- 优化标记异常商品逻辑,内存分页改为数据库分页
- 优化企划品类价格入参
- 跳过没有sku的逻辑, 不中断
- Inner商品查询barcode取值问题.
- Inner商品查询barcode取值
- *(product)* 修复 SKU 信息更新逻辑 - 优化 SaleSku 更新条件，考虑空值情况
- 导入数据平台为空问题
- *(product)* 修复批量更新尺码信息时的空指针异常 - 在更新尺码信息前增加对 sellerSku 的空值检查 - 避免在 sellerSku 为空时执行数据库更新操作，提高代码健壮性
- *(product)* 修复 SKU 尺码更新逻辑 - 将 StringUtils.isEmpty() 替换为 StringUtils.isBlank()，以更准确地判断字符串是否为空 - 修正国家字段的更新条件，只在国家字段不为空时进行更新
- 优化导入上架商品
- 更新品类 颜色 tryon商品创建
- 导出导入失败商品
- 导出导入失败商品.
- 待上架导入bug
- 新增现货相关字段
- 升级sdk
- 升级sdk，现货tryon字段限制
- 条码spucode问题
- 待上架-发布失败商品数据导出
- 批量创建商品
- Skc操作更新枚举
- 更新采购价 尺码信息等
- 导入商品问题
- 导入商品优化
- 导入商品定时任务
- 上架同步店铺名
- 导入新增采购价 定价类型 现货类型
- 定价类型 现货类型 去空格
- 现货tryon去掉校验图包
- 现货tryon去掉校验图包。
- ODM 现货tryon去掉校验图包。
- 现货类型新增code字段
- 现货类型新增code字段.
- 现货类型判断
- 现货类型判断.
- 导入类型
- 导入库存为空问题
- 更新尺码信息。
- *(product)* 修复返单规则计算不正确应用品类最大值的问题 - 在产品定价类型为返单规则时，不应用品类最大值判断 - 对于其他定价类型，继续使用品类最大值判断 - 优化了代码结构，提高了可读性和可维护性
- 导入更信人问题
- 待上架强制重新计算
- 待上架强制重新计算.
- 编辑商品问题。
- 待上架导入新增导入标识
- 导入barcode问题
- 条码skc图更新
- 返回现货类型code，商品详情返回现货类型opsCode
- 创建商品SDK
- 操作日记返回
- 更新记录问题
- 优化尺码问题，和更新记录
- 推送商品SKC
- 推送商品SKC新增记录.
- 现货类型更换枚举
- 导入保存现货类型.
- 尺码并集问题
- 尺码并集问题.
- *(product)* 修复获取库存数量返回空列表的问题 -增加对 respDataResponse.getStockSpuQuantityList() 的非空检查 - 如果列表为空，返回 Collections.emptyList() 而不是 null
- 更新品类
- 更新品类.。
- 尺码修改
- 尺码更新问题。
- *(product)* 修复物流费用计算逻辑 - 增加对物流费用的空值判断，避免空指针异常 - 添加物流费用从 USD 到 CNY 的货币转换逻辑 - 优化代码结构，提高可读性和可维护性
- *(product)* 修复删除价格规则时公式明细未正确删除的问题- 增加根据规则 ID 查询相关公式 ID 的步骤 - 仅当存在相关公式 ID时执行删除操作 - 优化日志输出，增加无公式明细需要删除时的提示
- *(pop-product-service)* 优化图片服务 SPU 编码校验逻辑 - 在 ImageEditReq 类中添加 @NotBlank 注解，确保 SPU 编码不为空- 在 ImageRepositoryServiceImpl 中增加 SPU 编码前后空格处理 - 在 ImageSaveReq 类中将 @NotEmpty 改为 @NotBlank，提高校验精度
- 更新尺码 价格问题
- 更新尺码和sql脚本
- 更新价格问题。
- 颜色日志问题
- 导入现货类型
- 发布问题
- 尺码问题
- 标题问题
- 编译商品问题
- MQ记录消费时间
- 编辑状态
- Spu+shop获取product
- *(pop-product-service)* 修复根据 skc 获取产品信息的问题 加上逻辑删除
- 导入供货价校验问题
- 导入供货价校验问题。
- *(product)* 修复根据 productId 和 skc 查询产品 Skc 信息的问题 - 将查询条件中的 productSkcId 更改为 skc，以匹配正确的字段 -确保查询结果的准确性，避免返回错误的产品信息
- *(pop-product-service)* 修复 SKU 编辑时异步更新 Lazada 图片 URL 的逻辑错误- 将 updateLazadaImagesUrlsAsync 方法的第二个参数从 repository 改为 repositoryBySpuCode.get(editReq.getSpuCode()) - 更新版本号至 20250310
- *(product)* 优化成本价格设置逻辑 - 在设置新 SKU 的成本价格时，增加了对成本价格的检查 - 只有当成本价格大于零时，才将其设置为新 SKU 的成本价格- 这个改动可以避免将无效或零成本价格错误地设置到新 SKU 上
- *(product)* 修复价格计算服务未考虑供给方式为空的问题- 在价格计算逻辑开始处增加了对供给方式（supplyMode）的空了价格计算服务只在有效的供给方式存在时进行计算，提高了服务的健壮性
- 商品查询sku状态返回
- 初始化数据enable默认0
- 初始化默认尺码
- 拉取LZD数据-修复colorCode使用平台颜色改为字典颜色
- 不更新为空颜色
- 同步lzd-系统颜色
- 同步lzd-清空改为更新
- 同步lzd-删除sku(sellerSku不为空才删除)
- 同步lzd-匹配品类
- 任务的创建人id设置为定时任务会话id
- 通知商品创建
- 通知商品创建。
- 通知商品创建。.
- 尺码更新问题
- MQ配置
- 导出商品过滤取消SKC
- 商品推送问题
- 更新判断问题
- 逻辑删除改为使用其他状态处理
- 实体注解增加field修饰
- *(product)* 修复产品下线时 SaleGoods 为 null 的问题 - 修复了产品下线过程中 SaleGoods 可能为 null 的问题 - 增加了处理 SaleGoods 为 null时的逻辑，记录 SKU级别的错误日志 -优化了错误处理和日志记录的方法，提高了代码的可读性和可维护性 - 修改了查询产品是否存在时的字段名，从 shopId 改为 spuCode
- 修复异常任务导出时间字段,修复sku同步改为enable=1
- *(product)* 热修复分支 修复产品下线时 SaleGoods 为 null 的问题
- 导出同步失败
- 导入商品任务修复会话用户
- 更新商品判断sku上下架并更新goods
- *(product)* 修复产品下架时同步日志记录的问题 - 增加了在没有找到符合条件的 SKU 记录时的处理逻辑 - 优化了同步日志的记录方式，提高了日志的可读性和完整性 - 改进了产品发布状态的更新方法，提高了代码的可维护性
- *(product)* 修复商品成本价计算逻辑 - 修复 ODM 模式下商品成本价计算逻辑，增加对 purchasePrice 的非空判断 - 完善已上架商品价格信息日志输出，增加 supplyPrice 参数 -优化商品创建流程中的价格计算逻辑，提取 countryList 获取方法
- 拉取同步更新skuId
- 拉取商品数据-店铺不存在也更改任务状态
- 包名调整
- 移除空saleGoodsId的sku初始化
- *(pop-product-service)* 增加压缩文件非空校验- 在压缩文件后、上传前，增加对压缩文件列表的非空校验 - 如果压缩后文件为空，抛出 BusinessException 异常
- 添加 InnerFeign 注解，开启 token 中继
- 待上架列表
- *(pop-product-service)* 修复 sale_sku 查询中的拼写错误
- 修复 Lazada 商品追加刊登逻辑 - 优化商品刊登时的错误提示信息，使用更友好的描述- 修正销售 SKU 的发布状态逻辑，确保正确更新
- 添加产品首次发布人信息并优化查询功能 - 在 sale_goods 表中添加 publish_user_id 和 publish_user_name 字段- 在产品发布时记录首次发布人信息 - 更新产品查询接口，支持按首次发布人名称搜索 - 优化产品查询条件，支持多条件过滤 - 移除 ProductLazadaPageResp 中的冗余字段
- 修复 Lazada 商品首次发布用户名称字段映射错误 - 将错误映射的字段从 `latestPublishUserName` 修改为 `publishUserName` -确保正确获取首次发布商品的用户名称
- 修复 已上架商品 SKU 查询的关联关系错误 -将 SaleSkuMapper.xml 中的 sale_goods_id 关联条件修改为 sale_skc_id - 更新版本号至 20250416
- Lazada已上架 添加国家筛选条件- 在查询 saleGoods 数据时，增加了国家筛选条件
- 商品SKU列表查询-统计数量 -将 COUNT(DISTINCT ps.skc) 修改为 COUNT(DISTINCT ps.color_code) 以正确计算颜色数量 - 修正 sale_sku 和 sale_skc 表的连接条件，从 ss.sale_goods_id = ps.sale_goods_id 改为 ss.sale_skc_id = ps.sale_skc_id
- 商品SKU列表查询-统计数量 - 将颜色计数查询从 COUNT(DISTINCT ps.color_code) 修改为 COUNT(DISTINCT ps.sale_skc_id) - 这个修改更准确地反映了需要统计的不同销售 SKC 数量，而不是颜色代码
- 待上架-更新组合颜色
- 修复批量更新库存
- 校验 lzdAttrResp options null
- 解决onlineAeProduct冲突
- 在 AeUpdateProductComponent 中恢复产品状态更新
- 更新图像 URL 处理，以便在 AeUpdateProductComponent 中使用 OSS 图像 URL
- 更新 AeSaleSku 和 AeUpdateProductComponent 中的 SKU 处理逻辑，确保正确设置 shopSku 和 platformSkuId
- 重构图片上传和处理逻辑，优化异常信息，确保更清晰的错误提示
- 修复同步锁使用kt函数
- 导出数据-无数据判断提前弹窗
- 筛选异常数据
- 移除商品有效天数的硬编码，优化 AE 接口错误处理逻辑
- 优化 refCategoryAttribute 方法，增强属性值处理逻辑，确保正确更新和删除属性值
- 待上架导出-商品数据
- 异常筛选
- 上游更新ETA-没有更新Lazada
- 兼容旧的上游更新数据重算定价成本
- 上游更新ETA
- 修正版本号格式为0.0.11-SNAPSHOT
- 待上架-导出"导入失败"数据增加平台区分
- Test
- Null
- 修正BarCodeServiceImpl中sellerSku的设置逻辑
- AE更新商品, 不需要判断SKU上下架来更新SPU上下架状态
- 待上架-过滤空数据
- AE更新接口-多次update
- 导入处理空的属性值
- 已上架-AE更新-修复更新分组
- 已上架-详情-筛选skc可用
- AE状态回调任务-调用查询商品状态接口 > 商品详情接口
- 若skuList里存在skuId为空的数据, 则赋值associatedSku(新增sku才需要赋值)
- 修复问题 7c922bb - fix: 导入处理空的属性值 f8499b4 - fix: 已上架-AE更新-修复更新分组 bf8acab - feat: 待上架-AE-导出商品数据, 处理品类sheet字符规范
- AE-待上架-导出-sheet校验问题
- 优化cancle名称，优化更新categoryCode
- 更新商品SPU引用，修复日志信息中的SPU代码
- 修复未查询到有效平台属性信息时的处理逻辑
- ProductAeRegulatoryServiceImpl 添加SPU代码到图库查询
- 企划删除
- 优化lazada品类获取
- 更新商品ID的异常提示及响应字段
- 更新提示找不到lazada品类编译错误
- 更新长图后缀注释，添加营销图命名规则说明
- Sql
- 更新图片
- First改为firstOrNull
- AE导出批量更新-商品状态修复
- AE导出批量更新-只要上下架
- AE分页统计-修复状态筛选
- 解决 MqBaseListener 成员变量注入问题，Spring + Kotlin + RabbitListener 注入问题 代理创建机制：Spring 的 AOP 代理是通过子类化（CGLIB）实现的 初始化顺序问题：代理类可能在基类属性初始化前就需要访问这些属性
- 导入待上架更新初始化状态
- 修改供给数量和货盘数量的验证注解，确保不能为空的字段使用@NotNull
- 移除供给方式编码的必填验证注解
- 添加groupName（尺码组名称）不能为空的验证，更新校验注释以反映必要字段
- 修正submitExportTaskOffline方法的PostMapping路径，移除多余的反引号
- 移除事务后更新AliExpress图片地址的异步调用
- 导入价格
- 审批回调, 如果各个平台找不到则return
- 审批通过再打标
- AE构建详情HTML移除DEMO代码
- 超价-审批通过
- 审批流调整
- 重上
- 改价
- 修复A4 skc
- 审核不通过
- 审批打标
- 调整重上顺序
- 文案
- 清仓下架
- 应用更新-跳过两个成本价更新
- 系统更新导出
- 已上架-分页-标签展示
- 拉取AE商品, 兼容第三方接口异常msg为空情况
- 优化模板更新逻辑, 降低死锁
- 修复成本价内存缓存问题
- 自动化标题，保存或更新商品标题规则，修复删除旧规则详情时选择错误的字段ID
- 自动化标题，保存或更新商品标题规则，优化供给方式
- 自动化标题，保存或更新商品标题规则，优化供给方式，内部方法改名成supplyMode
- 自动化标题，规则，优化店铺信息dto，查询入参enable默认为空
- QC导出兼容新框架分页
- 上架AE，处理skc图片
- 增加AE图片为空校验
- 挑选变更的图片做重置失效bug, 使用全部重置
- 平台枚举
- 图片去重
- 拉取AE币种转换
- Ae商品同步，修正价格转换时的精度设置，确保正确的人民币价格计算
- 价格规则明细保存 更新运算符校验逻辑，确保第二个元素起运算符不能为空或有效
- AE速卖通创建/ 更新商品，保存颜色/尺码 valueId
- AE速卖通创建/ 更新商品，保存颜色/尺码 valueId，ae_sale_skc colorPropertyValueId赋值问题
- AE平台颜色长度不自动截取, 改为抛出错误
- AE拉取同步-使用colorCode匹配颜色
- 待上架-详情-店铺先拿product,再拿灵感
- 类型转换
- 模特列表
- 运费模版
- 尺码表
- 自动化标题，印花属性英文无翻译值的情况下，使用原英文值，判断要包含英文，避免出现纯字符情况
- 自动化标题，印花属性英文无翻译值的情况下，使用原英文值，判断是英文单词
- 自动化标题，印花属性英文无翻译值的情况下，使用原英文值，提取英文字母作为单词
- 审批人
- Collection first error
- 拉取temu商品的最新生命周期加入判空
- 拉取商品的最新库存加入判空
- 拉取商品的最新库存加入判空，删除不必要注释
- 拉取商品的最新库存加入判空，优化出参签名
- DAO 层 补充判空逻辑 判空逻辑应放在 DAO 层，并且应当直接返回空集合，而不是抛出异常。 Service 层应依赖于 DAO 层对参数边界的合理兜底，保证上层调用简洁、健壮。
- Log错误
- DAO 层 补充判空逻辑，优化方法签名/出参不可空类型
- 图库导出文件名
- 品类属性详情接口增加字段
- AE拉取数据, 兼容颜色属性
- 修复Temu初始化
- AE上架只查询可用skc
- PageAllSaleGoodsForSync deleted
- AE更新发货期判断审核中状态
- Temu统计状态数量
- *(product)* 修正商品SKU发布状态取值错误 修复ProductV2ServiceImpl中SKU发布状态取值逻辑，改为使用saleGoods的publishState
- *(test)* 禁用 MybatisTest 测试类 添加 @Disabled 注解以临时禁用 MybatisTest 测试类
- *(product)* 修复Lazada国家标题生成逻辑和map访问语法 refactor(product): 优化跨境类型判断逻辑和空安全处理 style(product): 统一代码格式和分号使用规范
- *(product)* 修复AE发货地处理逻辑中未过滤无效SKU的问题
- *(product)* 修复 AE 商品上下架相关问题 - 在 AeSaleUpdateServiceImpl 中为更新的 SKU 设置 stockQuantity 为 0 - 在 ProductManageServiceImpl 中注释掉部分冗余代码 - 更新版本号至 20250708
- *(product)* 完善 SKU匹配逻辑 - 在匹配本地 SKU 时增加了发货地属性的比较 - 从平台 SKU属性中获取发货地属性值 ID - 仅匹配卖家 SKU 和发货地属性值 ID 都相同的本地 SKU
- *(settings)* 移除硬编码租户ID并优化配置ID引用
- 移除ImagePackRuleHelper未使用的规则代码序列缓存键及相关方法
- *(ImageUtils)* 优化字符串比较使用 == 替代 equals
- *(ImageDisplayOrderEnum)* 允许fromCode接收nullable参数 refactor(AeUpdateProductComponent): 优化上传图片方法参数命名和顺序 refactor(ImageUtils): 优化图片过滤逻辑，使用序列处理 refactor(ImagePackCollectionHelper): 重构图片集合构建逻辑，优化参数顺序 feat(ImageConstants): 添加默认主图后缀常量
- *(MarketStyleComponent)* 修复多级字典返回逻辑错误 fix(ProductManageServiceImpl): 修正SPU匹配逻辑和样式名称更新条件 chore(versions.properties): 更新服务版本号至20250708
- *(product)* 修正StyleHisDataUploadExcelDTO中ExcelProperty的order值
- *(product)* 优化ProductManageServiceImpl的import
- *(ImagePackRuleRepository)* 替换硬编码启用状态为枚举值 fix(ProductTitleConfigServiceImpl): 移除标题数量减少时的清理逻辑
- *(product)* 优化商品标题生成逻辑及配置查询 - 移除英文单词校验方法及相关正则 - 调整标题配置查询逻辑，优先使用店铺配置 - 简化店铺关联配置的获取方式
- *(product)* 修复产品标题空值处理逻辑
- *(product)* 更新商品标题和名称处理逻辑 在Lazada、Temu和Ae平台的商品管理实现中，添加了对商品标题和名称的更新处理，确保导入时能正确更新这些字段
- *(pop-product)* 修复 SKU匹配逻辑 - 修改了 SKU 匹配条件，从判断 shopSku 是否为 null 改为与 platformSku.id 进行比较 - 更新了 platformSkuId 的匹配逻辑，从判断是否为 null 改为与 platformSku.skuId 进行比较 - 优化了日志输出，增加了更新商品前后的信息记录 - 调整了代码格式，统一了缩进和空行
- *(ProductAeRegulatoryServiceImpl)* 修正平台属性查询异常消息格式 修复BusinessException构造参数错误，优化异常消息格式
- *(settings)* 替换BusinessException为IllegalArgumentException 修改ProductTitleRuleServiceImpl、ProductTitleConfigServiceImpl和ImagePackRuleServiceImpl中的异常处理，将BusinessException替换为标准IllegalArgumentException，并调整相关校验逻辑。
- *(ImagePackRuleReq)* 设置displayOrder默认值为顺序显示
- *(ImagePackRuleReq)* 添加图包规则验证逻辑
- *(product)* 修复产品图包版本空值判断问题
- *(ImagePackRule)* 移除请求对象中渠道和平台名称字段 从ImagePackRuleReq请求对象中移除channelName和platformName字段，改为在ServiceImpl中通过枚举动态获取名称
- *(ProductTitleConfigService)* 修改保存模式为指定模式 将save方法改为明确指定SaveMode，避免潜在数据覆盖问题
- *(ImagePackRuleReq)* 修复首图规则验证逻辑 refactor(ImagePackCollectionHelper): 移除兜底逻辑改用异常处理 fix(ImagePackRuleServiceImpl): 修正默认规则设置条件 fix(ImagePackCollectionResp): 移除未使用的ruleDetails字段
- *(ProductTitleConfigService)* 修改保存模式为UPDATE_ONLY
- *(DownloadTaskTypeEnum)* 修正导出系统更新的组件名称 fix(ProductAeConfirmUpdateServiceImpl): 简化导出系统更新的任务名称格式
- *(image)* 移除ImagePackCollectionDTO中冗余的图片字段 重构图片相关DTO字段命名，移除mainImages/detailImages冗余字段，统一使用mainUrlList/detailImageList等新字段
- *(product)* 修正同步平台产品的图包版本判断条件
- *(product)* 修复SKU禁用逻辑及合并保存列表 1. 注释掉ProductAeServiceImpl中无效的批量禁用SKU代码 2. 合并saveSaleSkuList和updateSaleSkuList进行ID比对 3. 在ProductManageServiceImpl同步修改SKU列表合并逻辑
- *(ProductAeController)* 修改导出查询数据接口名称为提交查询任务 将exportQueryData接口更名为submitQueryDataTask，更准确反映功能用途
- *(ProductAeController)* 修改导出查询数据接口名称为提交查询注释
- *(controller)* 修正下载任务控制器的请求路径前缀
- *(AeSaleGoodsMapper)* 增加sku查询的deleted条件过滤
- *(product)* 优化条码查询逻辑中的空值处理
- *(product)* 修复多颜色组合时colorCode处理逻辑 将respList重命名为colorRespList，并优化单颜色时的colorCode拼接处理
- *(product)* 优化AE商品导入发货地处理逻辑 - 修改ProductManageServiceImpl中发货地比对逻辑 - 调整ProductTemplateAeSkuRepository查询接口，增加enableState参数 - 优化ImportAeProductDTO颜色分割处理
- *(product)* 优化颜色组合逻辑处理空值情况
- *(AeProductSkuSyncComponent)* 同步运费模板ID到商品信息
- *(ImagePackRule)* 将默认规则类型从YesOrNoEnum改为Bool
- *(ae/CallAeComponent)* 修复发货地属性值空指针问题
- *(ImagePackRuleRepository)* 移除未使用的count方法并优化分页查询格式
- *(product)* 修复商品价格库存更新时发货地条件查询问题 • 在查询saleSku时增加发货地条件判断 • 当发货地非空时添加shipsFromAttributeName条件 • 当发货地为空时查询shipsFromAttributeId为空的记录 此次修改确保在更新商品价格库存时能正确匹配不同发货地的SKU
- *(cache)* 优化ImagePackRuleHelper缓存逻辑并修复异常处理 refactor(repository): 在ImagePackRuleRepository添加根据ID查询方法
- *(product-update)* 修复发货地条件查询逻辑 • 将发货地查询条件从shipsFromAttributeName改为shipsFromAttributeValueName • 调整空发货地查询条件为检查shipsFromAttributeValueName是否为null • 确保当task.shipsFrom为空时正确查询无发货地记录的SKU 该修改解决了发货地查询条件与数据库字段不匹配的问题，确保能正确筛选指定发货地或无发货地的SKU记录
- *(ImagePackRuleReq)* 移除店铺ID列表的非空校验
- *(cache)* 修改AliexpressServiceHelper缓存过期时间为30分钟 将CACHE_EXPIRATION_HOURS改为ATTRIBUTE_CACHE_EXPIRATION_MINUTES，并调整相关日志输出

### 💼 Other

- MQ创建商品接口
- Common和sdk模块使用发布插件
- 波次字典更新
- 更新价格
- 企划更新日志调整字段
- 品类绑定, 约束内部品类只能绑定1个外部品类, 允许1个外部绑定多个内部
- 数量空给0
- 调整落坑数统计规则
- 异常订单增加字段
- 异常订单mq增加sku
- 异常订单分页增加sku
- 供给方式枚举code更新
- 异常订单增加取消人id字段
- 统计落坑数-增加shopId
- 企划品类
- 预设品类
- 调整预设品类接口
- 重新对接字典
- 清洗商品数据提供job接口
- 修改价格
- 修复商品数据,拆解属性逻辑(Lazada远程访问)
- 清洗商品数据-尺码转换
- 清洗商品数据-匹配聚水潭
- 清洗商品数据-处理已有skc的匹配barcode
- 优化企划品类价格保存-品类缓存
- 清洗barcode增加匹配规则
- Size 空
- 品类编辑关联, 放开限制
- 异常商品校验-移除barcode为空校验
- 平台颜色为空
- 批量更新-导出数据
- 平台id类型转换允许为空
- Config,component
- Core.json
- Convert
- 更新项目版本号 - 将项目版本从 0.0.3-SNAPSHOT 修改为 0.0.5
- *(pop-product-service)* 更新 ofp-order-sdk 依赖版本 -将 ofp-order-sdk 的版本从 0.0.2-SNAPSHOT 更新为 0.0.2-RELEASE - 此更新确保使用稳定版本的依赖，提高项目的可靠性
- *(pop-product-service)* 更新 Nacos 版本号 - 将 spring.cloud.nacos.discovery.metadata.version 从 20250307 修改为 20250311
- *(productactor)* 重构产品发布信息相关代码 - 移除了 ProductPublishInfoBO 中的 latestRevisedUserId 字段 - 更新了 ProductServiceImpl 中的产品发布信息提取逻辑 - 优化了 TaskServiceImpl 中的任务类型获取方式 - 修复了部分变量命名和代码格式问题
- 更新价格移除系统用户
- 0.0.7
- *(pop-product-service)* - 添加 aliexpress-sdk 依赖
- 商品内部接口-分页不走框架, 有限制数量
- *(pop-product-service)* 更新服务版本并优化 Kotlin 编译配置- 在 gradle.properties 中添加 Kotlin 编译守护进程的 JVM 参数，以提高编译性能
- 升级基础框架版本 3.0.4.1
- 移除未使用的配置
- 更新依赖版本并清理冗余依赖
- 更新及清理过时依赖
- *(pop-product-service)* 更新依赖并添加新功能 - 在 build.gradle.kts 中添加阿里速卖通 SDK 依赖 - 在 libs.versions.toml 中添加阿里速卖通 SDK 版本 - 在 SaleSku 实体类中添加组合商品标识字段
- 合并代码
- 合并 master 分支到 feature/0610
- *(pop-product-service)* 添加 Jimmer 依赖并更新配置 - 在 application.yml 中添加 Jimmer 配置文件引用 - 在 build.gradle.kts 中添加 Jimmer KSP 和 Spring Boot Starter 依赖
- *(pop-product-service)* 优化构建配置 - 移除了 blade.file 依赖，保留 blade.file.spring.boot.starter - 删除了以下依赖： - fastjson2-kotlin - jackson-module-kotlin - mapstruct 及其相关依赖 - jimmer-mapstruct-apt
- *(pop-product)* 更新数据库结构和版本号 - 在 product 表中添加 style_type、image_package_state 和 image_version_num 字段 - 在 image_repository 表中添加 version_num 字段 - 在 product_system_update_fail_log 表中添加 update_state 字段 - 创建 image_pack_rule、image_pack_rule_detail、image_pack_rule_shop、product_title_config、product_title_config_rule 和 product_title_config_shop 表 - 更新版本号至 20250716

### 🚜 Refactor

- *(product)* 添加批量下架商品事务注解并更新枚举类描述
- *(pop)* 修改品牌列表接口为 POST 请求
- *(pop-product)* 更新项目前缀路径并添加 OpenApi 注解 - 将项目前缀路径从 /pop-product 修改为 /pop-product-service - 在 PublishInnerController 和 ShopInnerController 的相关方法上添加 OpenApi 注解
- *(image)* 提交图片下载任务接口
- *(image)* 抽离静态内部类Image
- *(pop)* 修改渠道列表接口为 POST 请求
- *(pop-product-service)* 修改 SKU 代码生成规则中的连接符字段 - 将 SkuCodeRule 类中的 separator 字段重命名为 segmentSeparator - 更新 SkuCodeGenerateServiceImpl 中使用该字段的相关代码- 新增 SkuCodeGeneratorTest 测试类，验证自定义规则类型生成 SKU 代码的功能
- *(product)* 重构 SKU编码生成逻辑 - 更新 SkuCodeGenerateReq 构建方式，使用 Builder 模式 - 修改 SKU 编码生成请求参数的设置和传递方式 -优化 SkuCodeGenerateReq 类结构，增加 Builder 类
- *(image)* 移除了 ImageRepositoryServiceImpl 中的冗余代码和未使用的导入 - 将 createFileUploadDTO 方法从 ImageRepositoryServiceImpl 移动到 UploaderOssHelper 类中 - 优化了 UploaderOssHelper 中的文件上传逻辑，移除了不必要的错误处理
- *(product)* 重构价格计算逻辑 - 移除禁用上架相关逻辑，改为使用新标志位 - 新增 noRuleFound 和 noRuleSatisfied 标志位 - 更新价格计算服务实现，使用新逻辑处理无规则和无满足规则情况- 删除冗余代码，优化数据结构
- *(pop-product-service)* 重构产品价格计算逻辑，识别不了的枚举变量直接抛出异常
- *(product)* @PreCheckIgnore，内部调用暂时放开校验
- *(pop-product)* 解决easyapi插件兼容问题,调增kotlin接口路径不使用常量，直接用字面量
- *(pop-product)* 解决easyapi插件兼容问题,调增kotlin接口路径不使用常量，直接用字面量，移除未使用的常量类
- *(pop-product-service)* 重构汇率数据更新逻辑 - 移除 DateTimeUtils 中未使用的 HTTP_DATETIME_FORMATTER 相关代码- 在 RefExchangeRateMarketMapper 中添加物理删除方法 deleteByBaseCurrency - 在 RefExchangeRateMarketRepository 中添加 removeByBaseCurrency 方法- 在 RefRefExchangeRateMarketServiceImpl 中使用新的 removeByBaseCurrency 方法替换原有的删除逻辑
- *(settings)* 优化校验
- *(product)* 重构国家代码处理逻辑 - 移除 ProductBasicPriceRule 中的 JacksonTypeHandler 使用 - 添加 countryCodesStr 字段用于存储国家代码字符串 - 新增 convertCountryCodes 和 prepareCountryCodes 方法进行国家代码转换 - 在服务层中调用转换方法，确保数据正确处理
- *(pop-product-service)* 使用 JSONUtil 直接在服务层进行国家代码的序列化和反序列化
- *(pop)* - 将 SupplyModeEnum 中的 desc 属性改为 dictCode，并添加单独的 desc 属性
- *(pop)* - 将 SupplyModeEnum 增加dictCode列
- *(pop-product-service)* 优化产品价格规则组保存请求参数- 将多个 String 类型的字段改为可空类型（String?）
- *(pop-product-service)* 优化逻辑删除操作并添加 SKU 编码生成接口 - 将逻辑删除操作改为物理删除，简化代码结构 - 在 SkuCodeRuleController 中添加 generateSkuCode 方法 - 优化 ProductPriceRuleServiceImpl 中的删除逻辑 -调整 CurrencyExchangeRateServiceImpl 中的删除操作
- *(pop-product-service)* 优化枚举类实现并添加价格规则相关功能 - 将 values() 替换为 entries 以提高性能 - 优化枚举类中的 getByCode、isValidCode等方法实现 - 在相关枚举类中添加 validate 方法进行业务校验 - 在产品定价规则相关请求对象中添加 validate 方法进行参数校验 - 在产品定价规则服务实现类中调用 validate 方法进行业务校验
- *(pop-product-service)* - 删除了 handleUpdate 函数中对价格规则 ID 的存在性检查
- *(pop-product)* 重构价格规则保存逻辑 - 优化了产品基础价格规则和价格规则组的保存逻辑 - 改进了价格规则详情的验证方法 - 重构了逻辑删除相关代码，提高了删除操作的灵活性和准确性- 优化了批量保存或更新逻辑，减少了不必要的删除操作
- *(product)* 重构商品下架服务中的消息发送和状态更新逻辑 - 使用 IdHelper 替代 IdWorker 生成消息 ID- 优化了商品下架状态更新逻辑，提高批量处理能力- 修复了部分可能导致商品状态更新不一致的问题 - 优化了日志记录，增加了更多的上下文信息
- *(pop-product-service)* 优化文件上传逻辑 - 移除了不必要的文件路径设置 -简化了文件 ID 的生成逻辑 - 添加了文件名参数，避免 key 异常
- *(pop-product-service)* 移除 LazadaProperties 中未使用的导出限制属性 - 删除了 LazadaProperties 类中不再使用的 exportPublishProductSkcLimit 和 exportPublishProductSkcSizeNum 属性 - 这些属性可能在历史版本中用于导出上架商品的限制，但目前不再需要
- *(product)* 产品定价公式中保留小数位数可设置为 null - 将 roundingPrecision 字段从 Int 改为可空类型 Int? - 在价格计算服务中使用非空断言来处理可能为 null 的 roundingPrecision- 更新相关响应和请求对象中的 roundingPrecision 字段类型
- *(pop-product-service)* 修改产品价格公式中保留小数位数的默认值 - 将 ProductPriceFormula、ProductPriceFormulaResp 和 ProductPriceRuleGroupSaveReq 中的 roundingPrecision 字段默认值从 -1 修改为 2
- *(pop-product)* 重构 ShopService 实现类 - 移除了未使用的 Constant 类 - 在 LazadaConstants 中添加了 LAZADA_AUTH_CALLBACK_URL_KEY 常量 - 优化了 ShopServiceImpl 中的代码结构和变量命名- 删除了未使用的 convertToShopResp 方法 - 改进了回调 URL 的获取方式，使用新添加的常量
- *(pop-product-common)* 将 ShopResp 中的 platformId 类型从 String 改为 Long
- *(pop-product-common)* 解除商品价格计算对象的版本号限制
- *(pop-product-service)* 重构 CustomBigDecimalSerializer 并添加详细注释
- *(pop-product-service)* 重构产品定价相关请求对象
- *(pop-product-service)* 优化产品价格计算逻辑 - 重构了 calculatePricesForRule 方法，简化了价格计算流程 - 新增 shouldCalculateSalePriceFirst 方法，用于判断是否需要先计算售价 - 优化了代码结构，提高了代码可读性和可维护性 - 调整了异常处理和缓存使用方式，提升了代码稳定性
- *(pop-product-service)* 优化价格规则处理逻辑，特定国家不需要配置ISP/SP倍率等
- *(pop-product-service)* 优化 ProductUpdateLogResp 编译提示，添加@EqualsAndHashCode
- *(pop-product-service)* 优化价格规则缓存和日志逻辑 - 将规则缓存的类型从 ProductBasicPriceRule?改为 Optional<ProductBasicPriceRule>- 优化了日志输出逻辑，增加了调试级别的日志 - 修改了规则加载过程中的日志输出
- *(pop-product-service)* 移除图像处理中的花型处理逻辑
- *(pop-product-service)* 使用 fastjson2 替代 hutool-json
- *(pop-product-service)* 优化产品定价公式相关功能 -将小数位数配置类型字段设置为可空，以支持不同的配置方式 - 增加了对小数位数配置类型为必填项的验证 - 优化了错误提示信息，使其更加具体和友好 - 修复了保存产品定价公式时未设置小数位数配置类型的问题- 更新了版本号
- *(pop-product-service)* 将汇率缓存的键值对结构从 "fromCurrency:toCurrency" 修改为 "fromCurrency:toCountryCode"
- *(pop-product-service)* 修正同步状态枚举值并优化同步流程 - 将 PlatformSyncStateEnum 中的 FAILTURE 修改为 FAILURE，统一错误拼写 -优化产品创建、下线、更新价格等操作中的同步状态更新逻辑 - 引入 AtomicBoolean标记处理过程中的失败状态，精确控制同步状态更新 - 修改相关方法以适应新的枚举值和同步逻辑
- *(product)* 重构商品 SKU 和 SKC 列表查询接口 - 移除冗余代码，简化查询逻辑 - 新增 countryCodes 参数支持多站点查询- 优化 SQL 查询，提高性能 - 使用 MyBatis Plus 分页查询，简化分页逻辑
- *(product)* 重构商品下架功能 - 将 List替换为 Set 以提高性能 - 优化参数校验逻辑 - 重构商品和 SKU状态更新逻辑 - 优化错误处理 - 删除冗余代码并提高代码可读性
- *(product)* 优化产品同步状态更新方法 - 将 updateProductSyncState 方法的参数从单个 productId 改为 productId集合 - 这种改动可以支持一次性更新多个产品的同步状态，提高效率
- *(pop-product-service)* 移除 DeactivateProductRequest 中的无参构造函数
- *(pop-product-service)* 优化代码结构并添加下架消息消费接口 - 将 CountryEnum 中的 values() 方法替换为 entries属性，提高代码可读性- 在 ProductController 中添加 offline/consume 接口，用于消费下架消息 - 将 ProductSkcListReq 和 ProductSkuListReq 中的 countryCodes 字段重命名为 countryList，提升代码一致性
- *(pop-product-service)* 修改国家代码参数名称 - 在 ProductSkcMapper.xml 和 SaleSkuMapper.xml 中 - 将 req.countryCodes 替换为 req.countryList - 以适应前端传参变化，确保功能正常运行
- *(pop-product-service)* 优化查询逻辑并删除冗余代码- 移除了多个服务和存储库中的 isDeleted 字段查询条件 - 优化了部分查询逻辑,使用更合适的字段进行过滤
- *(pop-product-service)* 为后台任务处理添加系统用户身份认证 - 在 JobDownloadTaskController 和 JobRefExchangeRateMarketController 中添加 withSystemUser 函数
- *(pop-product-service)* 优化产品下架逻辑和数据处理- 移除了 JobDownloadTaskController 中未使用的注释 - 在 ProductOfflineServiceImpl 中添加了发送下架 MQ 消息的日志记录 - 优化了销售商品和 SKU 的查询逻辑，增加了空值检查和默认值处理 -改进了异常处理方式，确保在批量处理中捕获单个失败
- *(pop-product)* 优化发布商品导出功能 - 将请求参数校验逻辑移至 DTO 类中 - 优化 SKU列表查询条件，考虑发布状态 - 重构创建 Excel 数据对象的逻辑，提高可读性- 优化 SQL 查询，排除待上架商品
- *(product)* 重构商品发布状态查询逻辑 - 在 ProductPublishTaskExportServiceImpl 中使用 PublishConstants.PREVIOUSLY_PUBLISHED_STATES替代硬编码的发布状态集合 - 在 PublishConstants 中添加 PREVIOUSLY_PUBLISHED_STATES常量，用于存储上架过状态集合
- *(pop-product-service)* SaveErrorOrderListener 移除异常捕获逻辑
- *(pop-product-service)* 重构 MQ 消息头工具类- 优化 MqHeaderUtil 类结构，提高代码可读性和可维护性 - 添加更健壮的时间解析逻辑，使用 Hutool 库 - 在 MqThreadLocalUtil 中增加消息头验证和用户信息构建逻辑 - 使用 Lombok 注解简化代码
- *(pop-product-service)* 优化价格计算条件逻辑 - 添加对条件操作符和变量值的非空校验 - 优化异常处理，提高错误信息的准确性和可读性 - 新增 getByCodeOrThrow 方法，简化代码并提高可维护性 - 删除冗余的异常处理代码
- *(pop-product-service)* 为 ErrorOrderSaveMqDto 中的日期字段添加 JSON 格式注解
- *(pop-product-service)* 将 ErrorOrderInfo 中的 shopId 字段改为 shopShortCode - 修改了 ErrorOrderInfo 类中的 shopId 字段为 shopShortCode，类型从 Long 改为 String - 更新了对应的 ErrorOrderPageResp、ErrorOrderSaveMqDto等相关类中的字段 - 修改了 ErrorOrderInfoMapper.xml 中的 SQL 语句，以适应新的 shopShortCode 字段 -优化了 ErrorOrderServiceImpl 中的相关逻辑
- *(pop-product-service)* 优化 ErrorOrderSaveMqDto 校验逻辑
- *(pop-product-service)* 优化文件上传时文件名处理 - 使用 uploaderOssClient.buildFullyFileName 方法构建唯一文件名 -确保文件名的唯一性和安全性，避免潜在的文件冲突
- *(pop-product-service)* 优化字典客户端使用和错误订单处理
- *(pop-product-service)* 重构 BarcodeController 中的数据响应
- *(product)* 优化条码导出任务提交接口返回类型- 将 submitBarcodeExportTask 方法的返回类型从 void 改为 DataResponse<Void> - 添加了 ok() 方法返回成功的 DataResponse 对象
- *(pop-product-service)* 重构图片下载功能 - 修改 ImageDownloadReq 类，将 spuCodes 字段改为 imageRepositoryIds -移除 SPU 校验逻辑，改为在调用方进行校验 - 更新日志信息，使用新的字段名称 - 优化代码结构，提高可读性和可维护性
- *(pop-product-service)* 优化批量修改价格功能 - 新增数据校验和过滤方法，确保请求数据的有效性 -增加价格关系校验，防止产生逻辑错误 - 移除未使用的图片仓库记录获取方法- 优化产品服务中的批量更新价格逻辑
- *(pop-product-service)* 重构 Lazada 价格更新逻辑 - 新增 LazadaProperties 配置类，添加更新价格批次大小设置 - 重构 UpdateLazadaPriceListener 类，优化价格更新流程 - 新增分批处理逻辑，提高处理效率 -增加异常处理和日志记录，提升系统稳定性
- *(pop-product-service)* 优化 Lazada 价格更新逻辑 - 添加日志记录，提高代码可追踪性 - 使用事务处理错误日志和最终状态更新，确保数据一致性 - 优化异常处理逻辑，确保所有异常都能被妥善处理 - 重构代码结构，提高可读性和可维护性
- *(pop-product-service)* 优化SKU 价格更新逻辑 - 修改方法名称，提高代码可读性和维护性 - 优化代码结构，明确 SKU 价格更新的步骤和目的 - 增加方法注释，解释 SKU 价格更新的整个流程
- *(pop-product-service)* 重构价格计算相关代码 - 修改 ProductCountryPriceCalcInputBO 中的 competitorRetailPrice 和 competitorSalePrice 类型，去掉可空性 - 更新 ProductPriceCalcBO 中的字段类型，将可空类型改为非空类型，并添加 Builder 类 -调整 ProductPriceCalcServiceImpl 中的相关方法，以适应新的非空字段 - 优化部分代码结构，提高可读性和可维护性
- *(pop-product-service)* 优化产品价格计算异常提示信息 - 将 supplyType 方法的异常提示信息从英文改为中文，明确供货价必须大于 0 - 更新 countries 方法的异常提示信息，强调需要计算价格的国家列表不能为空
- *(pop-product-service)* 优化仿款价格校验逻辑 - 简化了竞品价格校验逻辑，直接判断划线价和售价同时小于等于零的情况 - 修改了错误信息，更准确地描述了价格不能同时小于等于零的要求
- *(pop-product-service)* 优化仿款供应方式的价格校验逻辑
- *(product)* 优化产品下架相关逻辑 - 在查询和更新销售商品和 SKU 时，增加了对平台商品 ID 和平台 SKU ID 的非空判断 -优化了批量更新 SKU 发布状态的逻辑，仅更新已上架的 SKU - 重构了部分方法，提高了代码的可读性和维护性
- *(pop-product-service)* 优化 DownloadTaskPageVo 中 JSON 数据处理 - 使用委托属性替换原有的 getResultFiles 方法，实现更灵活的 JSON 转换 - 优化 resultFiles 字段的序列化和反序列化逻辑
- *(pop-product-service)* 重命名 CategoryMappingExcelVO 为 CategoryMappingExcelDTO - 将 CategoryMappingExcelVO 类从 resp 包移动到 dto 包 -重命名 CategoryMappingExcelVO 为 CategoryMappingExcelDTO - 更新相关引用和文件名 - 删除空的 ImportMappingRequest 类
- *(pop-product-service)* 用 EasyPOI 替代 Alibaba Excel 实现 QC 记录导出功能
- *(pop-product-service)* 编译报错
- *(pop-product-service)* 优化代码格式和逻辑 -调整代码缩进和空格，提高可读性 -优化 if-else 结构，使逻辑更清晰 - 移除不必要的大括号，简化代码
- *(lazada)* 重构获取品牌列表接口并优化相关逻辑 - 优化 getBrandList 方法，增加异常处理和日志记录 - 引入 LazadaBaseResp 类作为响应基础类 - 更新 LzdBrandResp 继承 LazadaBaseResp - 新增 LazadaBrandRepository 方法检查品牌是否存在 - 优化 ShopServiceImpl 中的品牌保存逻辑
- *(pop-product-service)* 优化品牌数据保存逻辑 - 新增 batchSaveIfNotExists 方法实现批量保存不存在的记录 - 重构 ShopServiceImpl 中的品牌数据处理逻辑，使用新方法进行批量保存 - 优化了性能，减少了数据库查询次数，提高了数据处理效率
- *(pop-product-service)* 优化代码格式和结构 -调整代码缩进和空格，提高可读性 -修复部分代码格式问题，如 if 语句、异常抛出等 - 优化部分变量命名，提高代码清晰度 - 移除冗余的空行，使代码更加紧凑
- *(pop-product-service)* 重构图片下载功能并优化 SKU 图片处理 - 重构 ImageDownloadHelper 类，增加通用的图片下载方法 - 优化 ProductPublishTaskExportServiceImpl 中的 SKU 图片处理逻辑 - 更新 ImageRepositoryServiceImpl 中的图片下载调用 -调整 SkuImageExcelDTO 中 SKU 编码的字段名称- 在 SaleGoodsMapper.xml 中增加对 platform_product_id 的非空判断
- *(pop-product-service)* 优化代码结构和导入 - 移除了未使用的导入项 - 添加了必要的 BaseBizException 导入 - 删除了无用的空行和注释
- *(product)* 重构处理产品属性逻辑 - 优化了 saleGoods 和 token 的获取逻辑，提高代码可读性和性能 - 使用 Optional 替代原始的空检查，使代码更加简洁 -移除了不必要的注释和冗余代码，提高了代码的可维护性 - 统一了错误处理方式，增加了日志记录的详细信息
- *(pop-product-service)* 调整平台属性缓存初始化逻辑 - 将 initPlatformAttrCacheIfNeeded 方法移至 fixProduct 方法下方 - 添加注释解释方法用途 - 优化代码结构，提高可读性
- *(pop-product-service)* 移除 TODO 注释 - 删除了 FixProductServiceImpl 类中的 TODO 注释 - 保留了属性设置和错误列表处理的逻辑
- *(product)* 重构平台属性缓存结构 - 修改 PLATFORM_ATTR_MAP 的结构，从三级嵌套改为两级嵌套 - 更新 getPlatformAttr 方法，简化参数并适应新的缓存结构 -调整日志输出，增加类目数量和属性总数的统计
- *(pop-product-service)* 优化类目映射导入服务 - 将 Lazada 属性值转换为小写以确保匹配一致性- 更新版本号至 20241231
- *(pop-product-service)* 优化图片处理逻辑 - 重构了 imageRepository 方法，优化了图片下载和上传流程 - 新增 downloadAndUploadImage 方法，统一处理图片下载和上传逻辑 - 新增 findExistingImage 方法，用于查找已存在的图片 - 优化了主图、SKU 图片和描述图片的处理逻辑 - 移除了不必要的注释和冗余代码
- *(pop-product-service)* 优化图片命名和处理逻辑 - 使用 String.format 优化图片命名格式，确保两位数字填充 -调整详情图命名规则，从 0 开始编号 -修复详情图数量不匹配的问题
- *(product)* 优化 SKU 查询和数据修复功能 - 将 createdAfter 字段类型从 String改为 LocalDateTime，提高类型安全性- 优化查询条件判断，使用 Objects.nonNull 替代 StringUtils.isNotBlank - 修改数据修复逻辑，只更新必要的字段
- *(product)* 优化店铺和卖家映射关系的处理逻辑- 移除了删除操作，目前暂不执行删除 - 优化了日志输出，提高可读性 - 调整了代码结构，提高可维护性
- *(pop-product-service)* 优化条码查询接口参数校验逻辑 - 新增代码以处理 SPU、SKC、SKU 编码过滤条件 - 优化时间参数校验逻辑，支持不同场景下的时间验证 - 提高错误提示的准确性和用户体验
- *(product)* 优化条码查询接口 - 重构 setSizeTypeName 方法，提高查询效率 - 添加 sizeTypeInfoList 方法，封装用户信息 - 新增 SYSTEM_CODE常量，统一系统编码 - 修改 DictClientExternal 中的 consumerCode 使用常量 - 优化 ExportProductBarcodeDto 中的字段注释
- *(pop-product-service)* 优化 SizeTypeInfoVo 数据处理逻辑 - 增加对 listDataResponse 对象的非空检查，提高代码健壮性 - 优化条件判断逻辑，确保在处理数据之前进行必要的检查
- *(product)* 优化商品数据修复功能 - 重构了商品数据修复的更新逻辑，提高了代码的可读性和维护性 - 新增了商品数据修复状态的更新，修复完成后自动标记为非错误状态 - 优化了 SKU 尺码信息的更新方式，采用批量更新替代了之前的单条插入 - 移除了不必要的商品信息更新操作，减少了数据库交互次数 - 增加了日志记录，提高了系统的可监控性和故障排查效率
- *(pop-product-service)* 重构商品上下架请求模型 -移除 OfflineSkuSubmitReq 类 - 更新 OfflineSubmitReq 和 OnlineSkuListReq 类，增加新的请求参数 - 添加参数校验逻辑，确保请求数据的完整性- 优化数据结构，提高请求处理效率
- *(product)* 重构批量下架商品功能 - 移除分批查询逻辑，改为一次性查询所有数据 - 优化查询请求构建方式，直接在方法内设置参数 - 删除冗余的中间方法，简化代码结构 - 更新版本号至 20250109
- *(product)* 下架商品成功后删除变更失败记录 - 在产品下架成功后，新增删除变更失败记录的逻辑 - 实现了删除商品变更失败记录的私有方法 deleteProductChangeFailures - 该方法根据销售 SKU 列表、店铺信息、操作类型和国家来删除对应的失败记录 - 异常情况下会记录错误日志，成功情况下会记录信息日志
- *(pop-product-service)* 重命名 OnlineCountResp 为 OnlineSkuCountResp - 修改了以下文件中的类名和引用： - OnlineCountResp.java 重命名为 OnlineSkuCountResp.java
- *(product)* 优化下架商品日志记录和错误处理- 为 OfflineSubmitReq 和 OnlineSkuListReq 添加 excludeSkuCodes 字段，并增加校验- 优化 ProductOfflineServiceImpl 中的错误处理逻辑，增加平台名称和店铺信息记录 - 在 PublishPlatformRepository 中添加 loadPublishPlatform 方法，使用缓存优化查询性能 - 更新 SaleSkuMapper.xml，支持排除指定 SKU 的查询 - 更新版本号至 20250115
- *(product)* 重构上架商品导出功能 - 新增通用模板导出和花型图导出两种处理逻辑 -优化了数据处理流程，提高了代码可读性和可维护性 - 增加了对不同国家价格的处理 - 重构了Excel文件生成逻辑
- *(pop-product-service)* 修改 ProductLazadaSyncReq 中平台商品 ID 类型 - 将 platformProductIds 字段从 Set<String> 改为 Set<Long> - 更新字段名为 productIds，以适应新的数据类型
- *(product)* 重构商品相关实体和Mapper - 将 Java 实体类转换为 Kotlin 数据类
- *(pop)* 重构平台商品同步任务相关代码 - 将 ProductSyncTask 相关类名改为 PlatformProductPullTask - 更新相关 mapper 和 repository 接口名称 - 优化 CategoryMappingImportServiceImpl 中的代码 - 在 PlatformProductPullTask 中添加 platformId 和 channelId 字段
- *(pop-product-service)* 重构拉取商品功能 - 重命名 TaskStatusEnum 为 PlatformProductPullTaskStatusEnum - 重命名 TaskTypeEnum 为 PlatformProductPullTaskTypeEnum - 更新 ProductLazadaController 中的接口路径和方法名称 - 更新 ProductLazadaService 接口中的方法名称- 实现 ProductLazadaServiceImpl 中的方法名称和逻辑 -为 ProductLazadaPullReq 和 ProductLazadaSyncReq 添加 size 校验
- *(pop-product-service)* - 为 PlatformProductPullTask 实体类添加字段注释，提高代码可读性 - 更新 taskType 字段的注释，引用对应的枚举类 - 优化 PlatformProductPullTaskPageQueryReq 和 PlatformProductPullTaskPageVo 中 taskType 字段的注释
- *(pop-product-service)* 将 CountryTypeEnum 从 Java 转换为 Kotlin - 删除了 Java 版本的 CountryTypeEnum 类- 使用 Kotlin 枚举类重新实现了 CountryTypeEnum - 保留了原有的代码逻辑和数据
- *(pop-product-service)* 重构商品图片处理逻辑 - 重构 getImgByno 方法，改为 getImgByNo，返回类型从 List<String> 改为 List<ImageAniVo> - 更新 ProductDetailResp 中图片列表的类型，从 List<String> 改为 List<ImageAniVo> - 修改 setImages 方法，适应新的图片列表类型 - 优化图片处理逻辑，提高代码可读性和扩展性
- *(pop-product-service)* 优化商品拉取任务导出 Excel样式和查询 SQL -为 PlatformProductPullTaskExcelDTO 类添加列宽和行高注解 - 修正 PlatformProductPullTaskMapper.xml 中的 SQL 查询条件- 调整 PublishProductGeneralTemplateExcelDTO 类的行高和列宽
- *(product)* 重构商品同步任务创建逻辑 - 按 productId 和 country 分组处理商品信息 - 优化平台商品 ID 的获取逻辑，提高代码可读性和性能 - 重构任务创建流程，明确任务信息来源 - 新增 buildTaskName 函数统一任务名称生成逻辑
- *(product)* 更新异常数据，入参校验；更新或新增销售SKU信息考虑删除原有sizeName
- *(pop-product-service)* 优化平台商品导入 Excel 样式- 移除 PlatformProductPullTaskExcelDTO 中的 retryCount 字段 - 注释掉 ProductDataRepairServiceImpl 中的参数校验
- *(settings)* 移除费用配置中的状态字段 -从 FeeConfigResp、FeeConfigSaveReq、FeeConfiguration 和 FeeConfigurationServiceImpl 中移除了 status 字段 - 此次重构简化了费用配置的数据结构，移除了不必要的状态字段
- *(pop-product-service)* 更新控制器类的文档注释 - 在 CategoryPriceRangeController.kt 中更新类注释为"商品销售定价-基础配置-品类价格区间"- 在 FeeConfigurationController.kt 中添加类注释为"商品销售定价-基础配置-费用"
- *(pop-product-service)* 优化数据结构和字段命名 - 在 CategoryPriceRangeQueryReq、CategoryPriceRangeResp、CategoryPriceRangeSaveReq 中添加了品类编码、品类名称等字段的注释 - 在 FeeConfigQueryReq、FeeConfigResp、FeeConfigSaveReq 中添加了费用类型、国家代码、币种类型等字段的注释 - 为 BigDecimal 类型的字段添加了 @BigDecimalFormat 注解，用于统一格式化小数 -优化了部分字段的顺序和命名，提高了数据结构的可读性和一致性
- *(pop-product-service)* 优化品类价格区间和费用配置服务接口定义 - 为 CategoryPriceRangeService 和 FeeConfigurationService 接口添加了详细的函数注释 - 明确了函数的参数、返回值和可能抛出的异常 - 优化了函数命名，增加了 query、saveOrUpdate、batchSaveOrUpdate 等前缀 - 调整了部分参数类型，使用 List 替代数组- 引入了 BusinessException 异常类
- *(pop-product-service)* 修改字段名称为上次划线价 - 在 EditProductReq、SaleSku 和 SaleSkuResp 类中- 将字段 lastRetailPrice 的注释从"上次常规划线价"修改为"上次划线价" - 此修改统一了字段的描述，提高了代码的可读性和一致性
- *(pop-product-service)* 批量更改价格优化，设置上次价格
- *(product)* 重构商品成本价计算逻辑 - 新增 ProductSkcRepository 依赖，用于查询产品 SKU 信息 - 添加 SupplyModeEnum 枚举引用，用于处理不同供给模式下的价格计算- 修改 calculateWaitProductCost 和 calculateTryOnCost 函数，增加 isCbType 参数 - 重构 calculateMaxSupplyPrice 函数，根据不同供给模式和跨境类型计算最大供货价 - 更新 calculateActiveProductCost函数，增加对 OEM 供给模式的处理
- *(product)* 重构商品价格计算逻辑 - 移除 ProductCostPricingRequestBO 中的 existingCostPrice 字段 - 更新 ProductCostPricingServiceImpl 中的 calculateCostPrice 方法逻辑 - 重构 ProductCreateServiceImpl 中的 autoCalPrice 方法，将价格计算逻辑移至新的 ProductPriceManagementService 接口- 新增 ProductPriceManagementServiceImpl 实现类，负责商品价格计算管理 - 优化价格计算逻辑，支持批量计算定价成本和设置价格
- *(pop-product-service)* 在 ProductCostPricingServiceImpl 类中，将 categoryPriceRangeRepository 和 productSkcRepository 的 lambdaQuery 方法替换为 ktQuery 方法
- *(product)* - 在 StockDetailResp 中添加了上次销售价和上次划线价字段，丰富了库存详情信息
- *(pop-product-common)* - 在 ProductImageBatchQueryReq 类中，将 spuCodes 字段的类型从 List 改为 Set- 更新 spuCodes 字段的注释，明确其含义和限制
- *(product)* 重构自动计算价格功能 - 新增 AutoCalPriceRequestBO 作为自动计算价格的请求对象
- *(pop-product-service)* 移除 ProductPriceManagementService 中的原来的autoCalPrice
- *(product)* 重构商品价格更新功能 - 将价格更新相关代码从 ProductService 移动到 ProductPriceManagementService - 优化了批量更新和单个更新价格的逻辑 - 重构了价格验证和更新数据准备过程 - 移除了冗余代码，提高了代码可读性和维护性
- *(pop-product-service)* 优化品类价格区间批量保存逻辑 - 移除了对品类编码唯一性的限制，支持批量处理多个品类- 增加了对条件运算符的校验，确保只使用有效值 - 实现了按品类分组处理，提高了数据处理的灵活性和效率
- *(product)* 优化品类价格区间缓存处理逻辑 -将 CacheLoader 的返回类型从 CategoryPriceRange? 改为 Optional<CategoryPriceRange> - 在获取缓存值时使用 orElse(null)来处理未找到的值 - 优化了代码结构，提高了可读性和安全性
- *(pop-product-service)* 优化费用配置缓存处理逻辑 - 将 LoadingCache 的值类型从 FeeConfiguration? 改为 Optional<FeeConfiguration> - 在 CacheLoader 的 load 方法中使用 Optional.ofNullable包装返回值 - 在 getFeeConfigWithCache 方法中使用 orElse(null) 处理 Optional 类型
- *(pop-product-service)* 重构品类价格区间配置的保存逻辑- 移除了按品类分组处理的逻辑，改为统一处理所有请求数据 - 优化了删除逻辑，使用全量删除替代按品类删除，提高效率 - 使用 Spring 的 BeanUtils.copyProperties 方法替代手动属性赋值，提高代码可读性和维护性 - 统一了日志信息的输出格式，移除了不必要的品类代码信息
- *(pop-product-service)* 修改产品现货类型描述 - 将 "TRY_ON" 类型的描述从 "现货 try on" 修改为 "反季现货" - 将 "I_FASHION" 类型的描述从 "iFashion" 修改为 "ifashion"
- *(pop-product-service)* 将 Java 代码迁移到 Kotlin - 将 ImportProductRecord 类从 Java 转换为 Kotlin - 将 ImportProductRecordMapper 接口从 Java 转换为 Kotlin - 将 MessageConsumerRecordRepository、MessageConsumerRecordRepositoryV2 和 MessageRecordRepository 从 Java转换为 Kotlin - 更新版本号至 20250303
- *(pop-product-service)* 将 Java 代码迁移到 Kotlin - 将多个 Java 文件转换为 Kotlin 文件 - 更新包名以符合 Kotlin 的命名规范 - 优化部分代码结构以利用 Kotlin 的特性
- *(pop-product-service)* 重构 SaleSkuRepository 并优化相关逻辑 - 将 SaleSkuRepository 从 Java 重构为 Kotlin - 优化了多个方法的参数类型，使用更安全的非空类型 - 简化了部分代码逻辑，提高了可读性 - 更新了相关测试代码
- *(product)* 修改产品成本定价逻辑 - 将 OEM 模式改为 ODM 模式- 更新对应的产品供应模式判断条件 - 修改 getSkuSupplyPrice函数参数名称
- *(product)* 重构成本价计算逻辑 - 将获取供货价的逻辑移至 map 函数外部，提高代码可读性- 修改 calculateIfashionCost 函数，增加供货价和 isCbType 参数 - 优化物流费用获取逻辑，简化代码结构
- *(pop-product-service)* 将 Java 代码迁移到 Kotlin - 将 ProductSkcRepository 和 SaleGoodsRepository 从 Java 重构为 Kotlin - 更新了相关的组件和服务以使用新的 Kotlin 版本 - 修复了代码中的空指针异常问题
- *(product)* 重构产品定价相关代码 - 将 ProductSpotTypeEnum 替换为 SpotTypeOpsEnum - 修改 spotType 字段为 spotTypeCode，类型从 Int 改为 String - 更新相关 BO 和服务实现类中的字段和方法 - 优化部分代码结构，提高可读性和可维护性
- *(pop-product-service)* 重构产品相关代码 - 将 Java 代码迁移到 Kotlin -优化了产品查询和更新相关的逻辑 - 调整了数据类型，如 Integer 改为 Int - 移除了不必要的代码，提高了代码可读性和维护性
- *(pop-product-service)* 将 Java 文件转换为 Kotlin 文件 - 将多个 Java 文件转换为 Kotlin 文件 - 优化了部分代码结构，提高了代码可读性- 保持了原有的功能和逻辑不变
- *(pop-product-service)* 将 Java DTO 类转换为 Kotlin 数据类 - 将多个 Java DTO 类转换为 Kotlin 数据类，包括： - ProductChangeFailureExcelDTO - ProductCreateParamWrapperDto - ProductErrorInfoDto - ProductOfflineMqDto 和 ProductOfflineMqV2Dto - ProductPublishDto - PublishProductGeneralTemplateDTO 和 PublishProductGeneralTemplateExcelDTO - SkuImageExcelDTO - SkuInfoDto - UpdateProductStockDto - 优化了部分代码结构，如使用可为 null 的类型和 Kotlin 的属性注解- 保留了原有的功能和逻辑
- *(product)* 移除产品导入标识相关的代码 - 删除了与导入商品相关的代码分支 - 移除了 createImportSupplyPriceResult 函数 - 注释掉了查询 SKC 时排除导入商品的条件
- *(product)* 优化试穿模式下自动计算价格的逻辑- 重构了试穿模式下的现货类型检查逻辑，提高了代码可读性和维护性 - 使用常量集合 TRY_ON_HANDLE_SET 存储需要处理的现货类型，避免了硬编码 - 优化了日志输出，增加了空值检查以提高代码健壮性
- *(product)* 修正 I_FASHION 类型产品成本计算逻辑 - 将 calculateIfashionCost 方法的第二个参数从 supplyPrice 修改为 maxSupplyPrice - 更新 createMaxSupplyPriceResult 方法的参数名，从 maxPrice 改为 maxSupplyPrice - 这些修改统一了 I_FASHION 类型产品成本计算的参数使用，提高了代码的一致性和可维护性
- *(product)* 重构商品创建服务中的价格管理逻辑 -优化了商品 SKC 的获取方式，新增了根据 productId 和 skc 获取有效 SKC 的方法 - 改进了价格更新逻辑，使用新的 SKC 对象进行更新，避免修改原始对象 - 优化了批量更新 SKC 成本价的逻辑，仅在有成功计算价格的 SKC 时才执行更新 - 重构了商品价格管理中的部分代码，提高了可读性和可维护性
- *(pop-product-service)* 优化商品创建和更新逻辑 -移除了 BatchProductBasicPriceRuleSaveReq 中的 Size 注解 - 优化了 ProductCreateServiceImpl 中的商品更新逻辑 - 改进了 ProductServiceImpl 中的商品保存逻辑 -统一了对象比较方式，使用 Objects.equals 替代直接 equals 方法 - 使用 Stream API 优化了部分代码结构，提高可读性
- *(pop-product-service)* 移除 ProductPriceCalcServiceImpl 中的对 OBM_REPLICA 供应类型中竞品划线价和售价的冗余检查
- *(product)* 重构产品创建和价格管理功能 - 优化数据类型：将 Integer 改为 int，提高性能和一致性 - 简化代码结构：使用 forEach 替代 stream().forEach，增强可读性 - 提高效率：使用 Lists.newArrayList 创建空列表，避免不必要的流操作 -重构价格设置逻辑：将价格更新操作重构为更清晰的步骤，提高代码可维护性 -优化批量更新操作：集中批量更新操作，减少数据库交互次数
- *(product)* 优化产品价格管理逻辑 - 移除不必要的空值检查和冗余代码- 优化了强制更新和非强制更新的逻辑处理 -简化了 isLoginNum 方法的实现
- *(pop-product-service)* 优化产品价格规则保存请求的校验逻辑 - 在 ProductPriceRuleGroupSaveReq 和 ProductPriceSaveReq 中添加了对公式列表的非空校验 - 实现了清理公式列表中空数据的功能 - 增加了自定义异常处理，提高了错误信息的可读性
- *(pop-product-service)* 移除产品价格公式保存请求中的非空校验 - 移除了 ProductPriceFormulaSaveReq 类中 formulaType 和 roundingPrecisionType 字段的 @NotBlank 注解
- *(pop-product-service)* 优化图片更新逻辑 - 创建新的 ImageRepository 对象用于更新，而不是直接修改原始对象 - 新增必要的字段赋值，确保数据完整性- 提高代码的可读性和安全性
- *(product)* 重命名计算发布时间的方法 - 将 calculate 方法重命名为 calculatePublishTime，以提高代码可读性 - 在 ProductServiceImpl 类中更新了相关调用
- *(product)* 优化价格计算逻辑 - 增加对 SKC 列表为空的判断，如果为空则直接返回
- *(product)* 移除产品首次上架时间相关逻辑 - 移除了 ProductDetailResp 和 ProductPageResp 中的首次上架时间字段 - 移除了 ProductPublishInfoBO 中的首次上架时间属性 - 更新了 ProductMapper.xml，添加了 publish_time 字段的查询 - 优化了 extractProductPublishInfo 方法，移除了首次上架时间的计算逻辑
- *(product)* 优化批量查询产品图片服务 - 重构了 batchQueryProductImages 方法，优化了产品和 SKC 的查询逻辑- 新增 queryActiveProducts 和 queryActiveSkcMap 方法，用于查询有效的产品和 SKC 信息- 优化了日志输出，增加了更多详细的日志信息 - 重构了部分代码结构，提高了可读性和可维护性
- *(pop-product-service)* 重构 LazadaImageUtil.getImgByNo 方法 - 优化了空列表和无效文件名的处理逻辑- 改进了文件名截取和验证的逻辑，增加了错误日志输出 - 重构了过滤和排序的实现方式，提高了代码可读性和安全性
- *(pop-product-service)* 优化图片 QC 记录的反序列化逻辑 - 在 ImageQcRecordPageVO 中使用 @JsonIgnore 和 @JsonProperty 注解来控制序列化行为 - 将 problemCategories、problemImages 和 qcUploadedImages 字段标记为 @JsonIgnore - 新增对应的属性来返回反序列化后的对象列表 - 更新 ImageQcRecordServiceImpl 中的相关代码，使用新的反序列化后属性
- *(pop-product-service)* 使用线程池异步保存操作日志 - 在 ProductOfflineServiceImpl 中添加 asyncExecutor 字段，注入异步执行器 - 使用 TtlWrapper.runAsync 在异步线程中执行日志保存操作，以传递用户上下文 - 修改 ThreadPoolConfig 中的 getAsyncExecutor 方法，返回 ExecutorService 类型 - 优化线程池配置，使用相同的配置参数
- *(pop-product-service)* 优化产品操作日志记录功能 - 新增通用的日志记录方法 saveProductOperateLog - 在各个操作环节中添加更详细、结构化的日志记录 - 优化日志内容，包含更多上下文信息（如产品ID、变更详情等） - 使用异步方式记录日志，减少对主线程的影响
- *(pop-product-service)* 重构产品更新逻辑并优化日志记录功能 - 优化了产品品类、颜色、定价类型、采购价和尺码信息的更新逻辑- 新增详细的操作日志记录功能，包括更新前后的对比信息 - 重构了自动计算价格的方法，提高代码复用性- 使用异步方式保存操作日志，提高系统性能
- *(pop-product)* 优化商品更新逻辑和日志记录 - 在 PlatformSynOpeatorTypeEnum 和 ProductUpdateTypeEnum 中添加 findByCode 方法 - 优化 ProductCreateServiceImpl 中的商品更新逻辑 - 改进错误日志记录，增加更多上下文信息 - 重构操作日志记录方法，提高可读性和可维护性
- *(pop-product-service)* 重构图片服务异步处理逻辑 - 引入 TtlWrapper 用于异步执行任务 - 优化图片 URL 更新逻辑 - 重构异步方法，提高可维护性 - 优化数据库操作，减少冗余代码
- *(pop-product-service)* 移除 imageRepositoryServiceImpl.java 注释
- 异常商品检测
- LazadaApiFixService合并到LazadaApiService
- *(pop-product-service)* 优化线程池信息日志输出逻辑 - 重构 showThreadPoolInfo 方法，使用字符串模板替代手动拼接，提高可读性 -移除未使用的 execute(Runnable, Long) 重载方法，简化代码结构 - 优化日志输出格式，统一使用逗号分隔各项信息
- *(pop-product-service)* 重构 Java 代码并迁移到 Kotlin- 将多个 Java 文件迁移到 Kotlin，包括： - AttributesMapperUtils - CategoryUtils - ExcelUtil - OperatePublishAttributeEvent - 更新相关服务和 DAO 的引用 - 修正 PublishPlatformAttributeDTO 类的位置和类型
- *(pop-product-service)* 重构工具类并更新版本号 - 将 DateTimeUtils、FileExportUtils 和 LongUtils 从类改为 Kotlin 对象 - 优化了工具类的结构，提高了代码的可读性和可维护性
- *(product)* 优化产品更新逻辑和任务处理 -调整产品更新类型处理逻辑，优化代码结构 - 添加平台 ID 和渠道 ID 设置，完善产品发布信息 - 重构任务类型列表，提高代码可维护性 - 优化任务查询条件，提升查询效率
- *(product)* 移除冗余代码并优化商品下架功能 - 删除了 ProductOfflineServiceImpl 中的 fixOldDataSkuId 方法调用，减少了冗余代码 - 移除了 ProductServiceImpl 中的空行，提高了代码可读性
- *(pop-product-service)* 重构任务查询相关代码 - 优化了任务类型处理逻辑，使用 code 属性替代枚举类型 - 重构了任务信息获取逻辑，提取公共代码到新方法 taskUpdateProgressPageVo - 添加了图片更新任务类型的支持 -简化了代码结构，提高了可维护性和可读性
- *(pop-product-service)* 重构产品创建逻辑 - 优化了 SKC 创建和更新的处理流程 - 重构了条码创建和 SKU 初始化的逻辑 -改进了产品创建成功后的消息发送机制 - 优化了自定义尺寸列表的处理方式 -重构了部分方法，提高了代码可读性和维护性
- *(pop-product-service)* 优化产品成本价计算和日志记录 - 重构成本价计算逻辑，提高代码可读性和维护性 - 增加日志记录，以便于问题排查和性能监控 - 优化 SKC 对象更新流程，提高数据库操作效率 - 修复了一些潜在的 bug 和性能问题
- *(product)* 优化价格计算日志输出 - 在计算价格前日志中，使用 JSON.toJSONString() 方法序列化 request 对象- 提高了日志的可读性和调试的便利性
- *(pop-product-service)* 重构 Lazada 图片处理逻辑 - 将 LazadaImgSyncUtil 类中的图片处理方法迁移到 LazadaImageUtil 类 - 优化图片处理方法，提高代码可读性和性能 - 更新相关服务类中的图片处理调用，使用新的 LazadaImageUtil 类 - 删除冗余代码，简化代码结构
- *(pop-product-service)* 商品创建服务- 重命名部分变量和方法，提高代码可读性 - 优化部分代码结构 -移除冗余代码和注释
- *(pop-product-service)* 优化 LazadaImageUtil 中的图片名称处理逻辑 - 改进了图片 URL 的有效性检查 - 优化了文件名的提取和处理逻辑，支持不同格式的 URL - 重构了 getSkuImgList 方法，提高了代码可读性和性能- 增加了对空值和边缘情况的处理，提升了代码的健壮性
- *(pop-product-service)* 重构商品相关查询接口 - 将 getSkcByProductId 方法重命名为 getByProductId - 将 listByIds 方法替代 lambdaQuery().in().list() 的使用 - 优化部分代码结构，提高可读性和维护性
- *(product)* 重构商品资料更新逻辑 - 移除冗余代码，简化商品属性和 SKC 查询逻辑 - 添加商品编辑操作日志记录功能 -优化代码结构，提高可读性和维护性
- *(product)* 移除商品编辑日志中的详细修改点- 删除了对品牌、标题和属性修改的详细记录 -保留了基础的日志记录功能，仅记录"更新商品资料" - 简化了日志记录逻辑，提高了代码可读性
- *(pop-product-service)* 优化商品相关服务的更新操作 - 在多个服务实现类中添加用户信息和时间戳字段更新 - 优化部分方法的实现，使用实体对象批量更新替代条件更新- 修复一些潜在的bug，如未设置修改时间等问题
- *(product)* 重构产品下架逻辑 - 优化了产品下架过程中的错误处理和日志记录 - 引入异步处理机制，提高处理效率- 完善了 SKU 同步日志记录 - 修复了一些潜在的 bug 和性能问题
- *(dao)* 重构 DAO 层查询逻辑 - 使用 ktQuery() 替代 KtQueryWrapper 构建查询条件 - 使用链式调用替代多个方法调用，简化代码结构 - 移除冗余的条件判断，优化查询效率 - 统一使用列表批量操作，提高数据处理能力 - 引入缓存机制，提升频繁查询性能
- *(pop-product-service)* 重构 SaleSkuRepository 中的查询逻辑 - 使用 ktQuery() 替代 KtQueryWrapper(SaleSku::class.java) 构建查询条件
- *(pop-product-service)* - 将 utils 包下的多个辅助类移动到 helper 包中 - 更新相关服务实现类中的导入路径 - 重命名 SpuCodeGenerator 和 SpuColorCodeGenerator 类的位置
- *(logging)* 重构日志记录方式 - 使用 Slf4j 注解替代 LoggerFactory -采用 log 伴生对象简化日志记录 - 统一日志记录格式 - 移除冗余代码
- *(pop-product-service)* 更新图片仓库服务和版本号 - 在更新图片仓库信息时，保留修订者 ID 和名称
- *(pop-product-service)* 重命名工具类和包 -将 PageRespHelper 从 utils包移动到 helper 包 -工具类变更为 xxxUtils （此规则参考 spring的框架结构）
- *(pop-product-service)* - 将日志输出改为使用 lambda 表达式，提高性能- 优化代码结构，提高可读性
- *(pop-product-service)* 优化字段填充逻辑并更新版本号- 修改字段填充方法，只对 null 值进行填充 - 在 ImageRepositoryService 中添加修订时间字段更新
- *(product)* 优化产品下架失败的错误处理逻辑 - 增加对历史数据存在 SKU ID - 优化错误信息的传递和展示 - 改进代码结构，提高可维护性
- Product的shop不用作真实sku
- Product使用SPU查询唯一
- *(pop-product-service)* 优化下载任务处理服务和文件压缩工具 - 修改 DownloadTaskHandleServiceImpl 中的任务状态更新逻辑，提高并发处理能力 - 更新 FileCompressUtils 中的 getZipFiles 方法，确保所有文件正确关闭
- *(product)* 优化商品相关数据结构 - 将 ProductDetailResp 中的 isUpdate 字段重命名为 update - 移除 ProductDetailResp 和 ProductPublishReq 中的冗余 getter 和 setter 方法 - 删除 ProductServiceImpl 中未使用的 Stream导入
- *(pop-product-service)* 重构 AliexpressService 接口并优化相关实现 - 修改 queryCategoryTreeList 方法签名，使用 Long 类型替代 String 类型 - 引入 AliexpressCategoryTreeListRequest 和 AliexpressCategoryTreeListResponse 类- 优化方法实现，使用 apply 函数链式设置请求参数- 更新单元测试，使用 Objects.nonNull 替代 StringUtils.isNotBlank - 调整 ProductSkuSyncComponent 中的依赖注入顺序 - 更新版本号至 20250328
- Java > kt
- *(pop-product-service)* 将 TtlWrapper.runAsync 替换为静态导入的 runAsync 方法
- *(pop-product-service)* 重构产品基础价格规则相关实体和 Mapper - 将 ProductBasicPriceRuleGroup 重命名为 ProductBasicPriceRuleCondition - 将 ProductBasicPriceCondition 重命名为 ProductBasicPriceRuleConditionDetail
- *(pop-product-service)* 重构销售 SKC 相关逻辑- 修正 CallLazadaComponent 中的拼写错误 - 更新 SaleSkc 实体类，将 saleGoodId 改为 saleGoodsId - 修改 SaleSkuMapper 中的 SQL 查询，使用 sale_skc 表替代 product_skc表 - 更新版本号至20250402
- 移除BeanUtils.copy
- 删除无引用属性
- Import
- *(product)* 移除商品下架相关冗余代码 - 删除了 ProductController 中的旧版 SKU 和 SKC 列表查询接口 - 移除了 ProductOfflineService 接口中的冗余方法 - 删除了 ProductOfflineServiceImpl 中的旧版下架逻辑实现- 清理了与旧版下架功能相关的辅助方法和逻辑
- *(product)* 移除未使用的 ProductSkcRepository 依赖- 删除了 ProductOfflineServiceImpl 类中的 ProductSkcRepository 成员变量- 移除了与之相关的构造函数参数 - 删除了 fetchActiveProductSkcIds 私有方法，该方法使用了 ProductSkcRepository
- *(product)* 移除未使用的函数和冗余代码 - 删除了未使用的 sendOfflineMessage函数 - 移除了未使用的 retryGetLzdProductDetailByCount 函数 - 清理了冗余的空行和注释
- *(product)* 移除冗余的 SKU 列表查询功能 - 删除了 ProductOfflineServiceImpl 中的冗余注释 -移除了 SaleSkuMapper 接口中的 pageProductSkuListByProductId 方法 - 删除了 SaleSkuMapper.xml 中对应的 SQL 查询语句 - 移除了 SaleSkuRepository 中的 pageProductSkuListByProductId 方法 这个改动移除了不再使用的 SKU 列表查询功能，简化了代码结构，提高了代码的可维护性。
- *(product)* 移除产品导入相关代码 - 删除了 ProductService 接口中的 doImportProduct 方法 - 移除了 ProductServiceImpl 类中与产品导入相关的大量代码和逻辑 - 删除了与产品导入相关的辅助方法和字段
- *(pop-product-service)* 优化代码结构和注释 - 删除了不必要的空行和注释- 统一了商品管理相关接口的命名方式 - 为 TaskController 添加了任务调度的注释
- *(product)* 优化产品 SKC 更新流程 - 在更新平台颜色时，增加了记录已更新 SKC 编码的功能 - 在 SKC 更新完成后，调用同步方法将更新后的数据同步到 SaleSkc
- *(product)* 重构商品 SKC 相关逻辑 - 修改了 ProductManageServiceImpl 中获取 SKC 的方法，使用 getActiveByProductIdAndSkc 替代 getByProductIdAndSkcCode - 移除了 ProductSkcMapper 中的 listAllColorByProductId 方法 - 更新了 ProductSkcRepository 中的相关方法，简化了 SKC 查询逻辑- 调整了 ProductSkuSyncComponent 和 ProductUpdateComponent 中使用新的 SKC 查询方法 - 优化了 SaleSkcSyncComponent 中的批量同步逻辑，增加了通过 productSkcId 查找 saleSkc 的步骤
- *(product)* 优化产品下架日志信息 -增加站点信息记录 -修复版本号错误
- 迁移 javax 到 jakarta
- 迁移 mq 已废弃的 api
- 手动重写老框架提供的 sdk
- Blade 自动配置，不需要扫描加载
- *(pop-product-service)* 解决冲突
- *(pop-product-service)* 重构 Lazada 相关的响应类和请求类 - 将 Lazada 相关的响应类和请求类移动到单独的包中 - 更新相关的服务接口和实现类中的导入路径 - 新增 ProductLazadaDetailReq 和 ProductLazadaDetailResp 类 - 在 ProductLazadaService 接口中添加 detail 方法 - 在 ProductLazadaServiceImpl 类中实现 detail 方法
- *(pop-product-service)* 优化时间戳获取方式，毫秒，改成秒
- *(aliexpress)* 重构公共开发组配置获取方式 - 移除 AliexpressProperties 中的公共开发组配置 - 新增 SaasSsoSdkProperties 配置类 - 修改 AliexpressServiceImpl 中获取公共开发组配置的方式- 更新单元测试
- *(pop-product-service)* 重构阿里速卖通相关配置和接口调用 - 在 AliexpressProperties 中添加 domain 字段，用于配置速卖通平台域名 - 修改 AliexpressServiceImpl 中的 IopClient 实例化方式，使用新的 domain 配置 - 更新 AliexpressServiceTest 中的测试 token 为示例值
- *(product)* 重构店铺 token 刷新功能 - 优化了 AliExpress 和 Lazada 的 token刷新逻辑 - 提高了代码的可读性和可维护性- 增加了错误处理和日志记录 - 改进了异常处理机制
- *(pop-product-service)* 移除 Lazada 产品相关未实现接口的 TODO 标记 - 将 detail、page 和 update函数中的 TODO("Not yet implemented") 替换为空实现或默认值 - detail 函数返回空的 ProductLazadaDetailResp 对象 - page 函数返回空的 PageVo 对象 - update函数的实现已添加，但内部为空
- *(product)* 优化商品图片处理逻辑 - 改进图片名称匹配逻辑，使用精确比较替代 contains 方法
- *(product)* 优化条码生成逻辑- 移除了批量生成条码的逻辑，改为单个生成
- *(product)* 优化产品数据修复服务中的条码生成逻辑- 修改属性复制方式，提高代码可读性和性能
- *(product)* 优化产品发布任务导出服务 - 改进成本价计算逻辑，考虑店铺类型和供货模式 - 添加最大跨境价字段 - 更新数据查询以包含新字段
- *(product)* 修改商品详情接口名称
- *(product)* 优化条码导出功能 - 添加数据量校验逻辑，限制最大导出数量为20万条 - 重构条码查询逻辑，提高查询效率 - 优化数据处理流程，提升导出性能 - 更新版本号至 20250411
- *(pop-product-service)* 优化 Lazada 组件代码 - 使用 val 替代 var 声明不可变变量 - 移除不必要的空格和换行- 使用 Kotlin 风格的集合操作，如 flatten 替代 flatMap - 优化条件判断，使用 Elvis 操作符 - 移除冗余的分号 - 使用 Kotlin 范围函数，如 run
- *(product)* 重构商品详情页面的 SKC 和 SKU 数据处理逻辑 - 按颜色对 SKC 数据进行分组，一个颜色可能对应多个销售站点的 SKC - 将 SKU按 SKC ID 分组，以便快速查找 - 以颜色为单位聚合 SKC 信息，使用每种颜色的第一条 SKC记录作为代表 - 收集每种颜色下所有 SKC 对应的 SKU，并按尺码分组
- *(pop-product-service)* 重构 Lazada 商品更新逻辑 -优化了 SaleSkc 和 SaleSku 的处理逻辑 - 新增确定发布状态的方法 determinePublishState- 增加了更新 SaleGoods 信息的方法 updateSaleGoodsList - 改进了 SKU 状态更新流程，先设置为删除状态再批量禁用 -集成了 LazadaUpdateProductComponent以更新商品状态
- *(pop)* 重构平台枚举并添加新平台支持 - 将 PlatformEnum 从 pop-product-service 移动到 pop-product-common 模块 - 在 PlatformEnum 中添加 TIK_TOK、T_MALL、TAO_BAO等新平台 - 更新相关组件和服务以使用新的 PlatformEnum- 添加 ChannelEnum 枚举类，用于定义不同的销售渠道
- *(pop)* 重构 Lazada 相关组件中的频道配置- 将固定的 channelId 替换为 ChannelEnum.ALIBABA 枚举 - 更新相关组件中的 channelId 引用，使用 channelEnum.channelId 替代硬编码值
- 将 ProductManagePlatformUpdateReq 重命名为 ProductManageLazadaUpdateReq
- 将 CallLazadaComponent 中的 updateOrAddLazadaCountry 方法重命名为 updateOrAddCountry
- 将 CallLazadaComponent 中的 updateOrAddCountry 方法重命名为 publishExistingOrAddCountry
- 将硬编码的 "0" 替换为 LazadaConstants.LAZADA_API_SUCCESS_CODE
- 优化产品 SKU 同步组件中的代码 - 移除了未使用的参数等
- 移除 ProductSkuSyncComponent 中的 SaleSkcSyncComponent 依赖- 删除了 ProductSkuSyncComponent 类中的 SaleSkcSyncComponent 注入 - 移除了与 SaleSkcSyncComponent 相关的异常日志处理代码
- 重构 Lazada 商品同步逻辑 -优化了 SaleGoods 的查询逻辑，提高数据同步的准确性和效率- 重构了商品信息的更新流程，简化了代码结构 - 引入 StringUtils 工具类，提升了字符串操作的安全性 - 优化了品类映射的处理逻辑，增强了数据的关联性
- 更新AE店铺认证逻辑 - 将查询条件从 platformSellerId 改为 shortCode- 在更新店铺信息时添加 platformSellerId 字段 - 优化代码结构，提高可读性
- 优化ProductSkuSyncV2Component，spu, skc, sku, saleGoods 更新新增
- 优化已上架更新保存发布 - 移除了未使用的函数 shouldUpdatePublishState - 优化了产品信息更新的逻辑，提高了代码可读性和效率 - 使用 ObjectUtils.notEqual 替代 !Objects.equals 进行比较 - 添加了对 publishState 的更新逻辑
- 优化 Lazada 商品上架逻辑 - 增加了对组合 SKC 的判断和处理 - 优化了日志信息，增加了 SPU 相关的日志 - 调整了事务处理方式，提高了数据一致性
- 上架lazada新站点优化 SKU SellerSku创建逻辑
- 重构商品下架逻辑 - 修改分页查询请求类，移除继承的 PageReq - 增加 ProductOfflineMqV2Dto 类的 shopIds 字段 - 重构 ProductOfflineServiceImpl 类中的下架逻辑 - 优化 SaleSkuMapper 和 SaleSkuRepository 接口
- ProductOfflineService Java 转 kt
- 重构商品下架相关代码- 将 ProductOfflineService 重命名为 ProductLazadaOfflineService
- 修改导出下架失败数据任务类型描述和名称
- 优化代码结构和可读性
- 重构商品发布逻辑 - 移除 flagFrontend 字段，改为直接使用 publishState - 重命名 lazadaSkcId 为 saleSkcId，lazadaSkuId 为 saleSkuId - 优化 SKU 数据处理逻辑，提高代码可读性和维护性 - 调整商品发布状态更新逻辑，确保状态一致性
- 重构 Lazada 商品详情和列表响应 - 在 ProductLazadaDetailResp 中添加 shopId 字段 - 更新 ProductLazadaPageResp 中的 countryList 含义为上架状态站点列表 - 移除 ProductLazadaPageResp 中的 publishTime 字段 - 在 ProductLazadaServiceImpl 中优化商品列表查询逻辑 - 更新 ProductPublishInfoBO，增加创建人相关信息 - 修改 SaleGoodsMapper.xml 中的查询条件
- ProductLazadaPageResp - 删除了 tagAvailableInv 和 tagRegClear两个未使用的字段 -简化了数据结构，提高了代码的可读性和维护性
- 优化商品发布信息提取逻辑，简化代码结构
- 优化下架SKU处理逻辑，简化参数传递和查询方法
- 优化Lazada分页商品数据查询逻辑
- 优化lazada商品发布逻辑，增加记录上架时间，上架人
- 批量更新saleSku enable = 0
- 增强异常日志记录，包含spuCode和saleGoodsId信息
- 更新AliExpress类目树列表查询测试，调整token和日志记录方式
- 代码规范
- 重命名 AE category attributes invalidation methods
- 简化 enum entries
- 优化AE速卖通响应对象
- 重命名AliexpressFreightTemplateQueryReq.kt为ProductAeConfigQueryReq.kt
- 重命名ProductAeFreightTemplateQueryReq等
- 简化queryPromiseTemplates等方法的参数格式
- 更新字段注解格式，移除不必要的空格和修正版本号
- 更新CreateProductDto和Image类，调整字段类型及注解，移除冗余代码
- 修改CreateProductDto中的materialImageList字段为可变类型
- 优化产品图片处理逻辑，提取设置材料图片的方法并简化文件名生成
- 更新Image类，调整文件名注释并移除文件名非空校验
- 更新Image类，修改字段注解以使用@NotBlank替代@NotEmpty
- 修改字段注解以使用@NotBlank替代@NotEmpty
- 引入ProductPictureService并更新产品材料处理逻辑
- 使用isNotBlank替代isNullOrBlank以优化SPU代码过滤逻辑
- ProductPublishAeHelper移除未使用的异步执行器的注入
- Improve CategoryEntity 缓存
- ExtendInfo
- 优化AE查询监管属性
- 优化selectRegulatoryAttributesOptions
- 优化selectRegulatoryAttributesOptions出参
- 更新海关监管属性
- 添加税号及监管属性相关字段到多个表和请求对象
- 修改监管属性相关字段名称及类型，更新相关数据类
- 添加shopId字段到商品请求，更新海关监管属性相关数据结构
- 添加extendInfo字段到多个请求对象，优化共享参数准备逻辑
- 移除ImageCollectionDTO中的无用方法，优化ProductAeRegulatoryServiceImpl和ProductPublishAeHelper中的图片处理逻辑
- 修改prepareRegulatoryAttributesCommonParams方法注释
- 修改 ProductManageController updateLazada method to updateAe
- Lazada主图兼容
- 解决冲突 处理 associatedSku LazadaUpdateProductComponent
- 优化监管属性查询逻辑，简化代码结构并提升可读性
- 修改监管属性查询方法，优化参数类型并简化异常处理逻辑
- 更新平台和渠道枚举引用，简化代码逻辑并提升可读性
- 移除冗余平台枚举引用，简化代码逻辑并统一平台ID使用
- 更新平台枚举引用，简化代码逻辑并提升可读性
- 优化拉取ae商品品类处理
- 添加去重功能以优化主图获取逻辑
- 更新已上架商品导出策略，以支持 AE 平台和正确的 SQL 查询
- 优化lazada品类异常提示
- 导出上架商品 数码印花 直接上传Excel文件（不再导出花型原图后，不需要压缩）
- 使用LazadaConstants简化前台链接生成逻辑，移除冗余代码
- 优化前台链接生成逻辑，使用格式化字符串替代硬编码模板
- 使用国家枚举简化前台链接生成逻辑，移除冗余模板
- 调整Lazada 前台 URL 模板 countryUrlTemplates注释
- 优化SKU校验逻辑，简化条件判断并增加销售价格校验
- 移除版型相关调用 prototypeBasicService
- 优化图片工具类
- 优化Lazada商品参数组装逻辑，简化图片和SKU校验流程
- 优化filterImagesBySkc
- 优化saleSkc，pictures校验、使用
- 增加注释延迟设置paramWrapperDto其他参数
- 改名 ProductCreationTaskService to ProductCreateTaskService
- 统一SKC颜色检查逻辑，优化变量命名以提高可读性
- 优化数据列表循环，简化对productUpdateDto的访问
- 重命名copyAttributesWithTransaction为copyAttributes，简化方法调用
- 移除不必要的 tagId 赋值逻辑
- 移除未使用的CountryEnum导入
- 跟前端沟通后部分productService移除废弃代码
- 缓存代码 QC
- 优化不可空类型，优化部分Repository内部封装
- 移除废弃代码，ProductOnlineService
- 调整ProductManageController接口路径
- 跟前端沟通后部分productService移除废弃代码，/pop-product-service/web/v1/product/update-price
- 移除ProductCreateService中不再被调用的方法
- 移除ProductCreateService
- 移除ProductService
- 移除ProductDataRepairService
- 优化ProductLazadaService，移除未使用的成员变量
- .gitignore 增加 .kotlin
- TemuSaleGoodsMapper selectByIdsPage 加上 where
- ProductPublishAeHelper aeImages 去重
- ProductPublishAeHelper aeImages originalAeImagesSize 判断优化
- ProductPublishAeHelper aeImages originalAeImagesSize 判断优化, 用AE原始数量进行对比
- 控制AE速卖通商品更新同步行为的开关参数
- *(pop-product-service)* 移除 AeSaleGoods 类中未使用的函数 - 删除了 AeSaleGoods 类中的 setParsedShipsFromPropertyValueItem 函数 - 该函数主要用于设置发货地属性值，但根据代码审查结果，发现该函数未被使用
- *(product)* 迁移商品尺码修复功能 - 移除 FixDataService 接口和 FixDataServiceImpl 类 - 在 FixProductService 接口中添加 updateSaleSkuSizeNames 方法 - 在 FixProductServiceImpl 类中实现 updateSaleSkuSizeNames 方法 - 更新 ProductSkuSyncComponent 和 TestInnerController，使用新的 FixProductService 接口 - 删除 ProductFixComponent 类，清理冗余代码
- *(product)* 优化产品发布/下架失败任务导出服务 - 移除了不必要的 @Autowired 注解，改为直接在类定义中声明依赖 - 优化了类结构，删除了未使用的依赖 - 统一了日志框架的使用 - 调整了变量命名，提高了代码可读性
- *(settings)* 合并图包规则相关请求类至ImagePackRuleReq.kt 删除ImagePackRuleApplyShopReq、ImagePackRulePageQueryReq、ImagePackRuleStatusReq独立文件，将其内容合并至重命名的ImagePackRuleReq文件中
- *(pop-product)* 优化项目配置 - 更新了 pop-product-common、pop-product-sdk 和 pop-product-service 模块的构建配置- 添加了 API 版本生成插件 - 调整了依赖项的版本和范围
- *(settings)* 移除未实现的下拉框查询功能及相关代码
- *(ae-product)* Ae更新接口冗余代码移除
- *(ImageUtils)* 移除未使用的图片处理方法
- *(pop-product-service)* 移除 SaleSkuRepository 中的未使用函数 - 删除了多个未使用的函数，包括 list、listByProductIdsAndCountryCodes、getBySkcAndCountryAndSizeName等 - 优化了代码结构，提高了代码的可读性和维护性 - 这些更改不影响现有功能，仅移除了冗余代码
- *(image)* 将ImagePackCollectionDTO重命名为ImagePackCollectionResp并移动至resp包 修改相关文件引用，统一使用新的类名和路径
- *(image)* 重命名 ImagePackInnerCollectionDTO 为 ImagePackCollectionDTO 主要变更： - 将 ImagePackInnerCollectionDTO 重命名为 ImagePackCollectionDTO - 更新相关引用文件和常量映射 - 修改版本号至 20250709
- *(ImagePackCollectionHelper)* 重构图片集合处理逻辑，移除冗余代码 - 移除ImagePackCollectionDTO.toImageCollectionDTO方法 - 重构updateProductImagesAndBuildInnerCollection为updateProductImagesAndBuildCollection - 优化图片分类填充逻辑，使用规则处理结果直接填充 - 移除fetchImagesByType方法，简化图片分类处理
- *(AeUpdateProductComponent)* 修改updateImagePackRuleFields方法，使用ProductSyncContext替代saleGoods参数
- *(component/service)* 统一asyncExecutor注入位置到构造函数
- *(pop-product-service)* 优化 SKU 更新逻辑并添加运费模板查询功能 - 注释掉 AeUpdateProductComponent 中的 platformSkuId 相关代码 - 在 AliexpressService 接口中添加 getFreightSettingByTemplateQuery 方法 - 在 AliexpressServiceImpl 中实现 getFreightSettingByTemplateQuery 方法 - 添加 AliexpressFreightTemplateDetailResponse 数据类用于处理运费模板查询结果 - 更新版本号至 20250710
- *(product)* 重命名ProductTitleGenerationDTO为ProductTitleConfigGenerationDTO 修改相关实体类和服务的DTO引用，保持命名一致性
- *(pop-product-service)* 调整 MybatisPlusConfiguration 包名 将 MybatisPlusConfiguration 类所在的包名从 tech.tiangong.pop.config 改为 tech.tiangong.pop.core，并删除了未使用的导入语句 ApplicationMetaObjectHandler。
- *(mybatis)* 优化 Mybatis 相关代码配置,移除KT模板分号
- *(pop-product-service)* 移除 PublishGroupAttrService 中的未使用方法 - 删除了 PublishGroupAttrService 接口中的 statisticAttrByGroupId 方法 - 移除了 PublishGroupAttrServiceImpl 类中的对应实现方法 - 优化了代码结构，提高了代码的可读性和维护性
- *(product)* 优化产品服务相关逻辑 - 移除了未使用的 AeProductPriceAlertComponent 依赖 - 过滤了已禁用的 SKU - 优化了 SKU 初始化逻辑，包括条码处理和属性赋值 - 调整了部分代码格式和命名，提高了可读性
- *(pop-product-service)* 重构产品属性处理逻辑 -简化了原产地属性和发货地属性的处理逻辑 - 使用 apply 和 copy 函数替代了复杂的 when 表达式 - 分离了属性 ID 和名称的处理
- *(ImagePackRule)* 查询添加排序和limit限制 - 在ImagePackRuleRepository中替换YesOrNoEnum为Bool枚举 - 修改DDL.sql中表字段定义
- *(dao)* 优化查询存在性检查逻辑 将limit(1).execute().isNotEmpty()简化为exists()方法调用，提升代码可读性
- *(constant)* 重构Redis常量类使用统一前缀
- *(constant)* 重构Redis常量类使用统一前缀，移除多余的冒号
- *(ImagePackRuleRepository)* 优化仓库类查询方法，使用Fetcher简化代码 - 在ImagePackRuleRepository等仓库类中引入Fetcher - 移除冗余查询方法，统一使用findByIds和findById - 优化ProductTitleConfigRepository查询逻辑
- *(docs)* 优化数据库表结构定义 - 在 product_title_config_rule 表中为 product_title_config_rule_id 添加 PRIMARY KEY 约束 - 在 product_title_config_shop 表中为 product_title_config_shop_id 添加 PRIMARY KEY 约束

### 📚 Documentation

- 销售定价，图片管理，上架商品编码规则，选款管理，sql
- 销售定价 sql
- *(settings)* 商品销售定价表结构调整
- *(settings)* 参考货币添加币种国家code
- 调整接口注释
- *(product)* 新增产品基础价格计算规则相关表
- Shop 增加token失效时间 DDL
- 移除版型表 sql
- 自动化标题, DDL脚本text类型默认不能为null

### ⚡ Performance

- 清除SaaS相关代码
- *(product)* 优化商品下架逻辑 - 调整了 SPU、SKC 和 SKU 下架逻辑的执行顺序 -仅在必要时获取店铺信息，减少数据库查询次数 - 对于空的结果集，不再执行后续的处理逻辑 - 优化了代码结构，提高了可读性和维护性
- 企划相关逻辑删除改为物理删除
- 优化运营处理状态枚举
- *(pop-product-service)* 优化属性映射导入性能 - 移除了 AttributeMappingExcelDTO 中的 shouldSkip 函数 - 新增 buildSkipLazadaAttrMap 和 shouldSkipLazadaAttrRow 函数，用于构建跳过缓存- 在导入过程中使用缓存来判断是否需要跳过行，提高处理效率
- *(pop-product)* 优化获取最早上架记录的 SQL 查询 - 使用窗口函数 ROW_NUMBER() 替代原先的 GROUP BY 和 JOIN操作，提高查询效率
- *(pop-product)* 已上架数据导出优化花型图片获取
- 移除不引用的代码
- Sdk > kt
- Java > kt
- 移除kt的lombok
- 优化产品同步性能 - 在更新产品信息时，仅更新必要的字段，避免全量更新导致的性能问题- 创建临时对象用于更新特定字段，减少数据库操作的开销

### 🎨 Styling

- Java代码改为lombok
- 异常订单-批量处理
- 异常订单-列表分页和导出(未接入下载管理)
- 异常订单controller
- 企划管理
- Commit
- 调整 ->
- 移除不用的import
- 代码检查
- Idea代码分析调整
- 修正单词
- 更新包路径
- 大小写
- 注入标量放在前面
- Equals
- 统一CollectionUtils包名
- 统一CollectionUtils和StringUtils包名
- *(pop-product-service)* 修复 ProductMapper.xml 中的 SQL 查询语句 - 在产品信息查询中，将 publish_user_name 字段的格式调整为与其他字段一致 - 删除了 publish_user_name 字段后的逗号，确保 SQL 语句的正确性
- 移除方法
- 移除fix相关代码
- 调整产品更新类型代码格式
- 删除多余的括号 - 在 ProductLazadaController.kt 文件中，将 "/apply-product}" 的 URL 路径中的多余括号删除
- 移除海关监管属性查询的时候共享参数数据类多余空格
- 修复代码格式，调整条件判断语句的空格
- 删除ProductSyncParamWrapper多余空格
- BarCodeService
- Listener -> listeners
- *(pop-product-service)* 规范代码格式 - 移除代码中的多余空行和分号 - 统一使用 Kotlin 的编码风格 - 简化部分代码逻辑，提高可读性
- *(pop-product-service)* 优化日志打印和异常处理 - 移除多余的花括号，使用简洁的字符串模板

### 🧪 Testing

- 添加商品定价相关数据- 插入 category_price_range表的数据 - 插入 fee_configuration 表的数据 - 插入 product_basic_price_rule 及其 detail 表的数据 - 插入 product_price_rule_group 表的数据 - 插入 product_price_rule 及其 condition、formula 和 formula_detail 表的数据
- 移除java的test目录
- 测试log.error输出
- Disable AliexpressServiceTest
- Disable
- 审批流单例测试
- 审批流
- 品类属性和创建规格属性(颜色)

### ⚙️ Miscellaneous Tasks

- 升级版本为 0.0.12
- 升级版本为 0.0.13.1
- 版本号0.0.14.1正式版，sdp-sample-clothes-sdk使用正式版
- *(gradle)* 更新sdp-design-sdk版本至1.0.19
- *(gradle)* 更新项目版本号为0.0.16

### ◀️ Revert

- 回滚AliexpressServiceTest.batchSetGroups提交

📝 Generated by <strong>aikero-ci-robot</strong> with ❤️
