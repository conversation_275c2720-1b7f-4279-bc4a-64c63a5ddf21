# AE价格重算工具使用说明

## 功能概述

由于生产环境使用了错误的毛利率算价，导致上架数据存在问题。此工具用于：
1. 根据Excel导入的商品信息调用生产环境价格计算API重新算价
2. 生成更新`ae_sale_sku`表的SQL语句
3. 输出为Excel文件，通过工单执行SQL避免直接操作生产数据库

## 使用方法

### 1. 准备输入Excel文件

Excel文件需要包含以下列：
- `sale_goods_id`: 销售商品ID  
- `spu_code`: SPU编码
- `product_id`: 产品ID
- `shop_id`: 店铺ID

示例数据：
```
sale_goods_id           | spu_code      | product_id          | shop_id
7355876258233722291     | PG2508040003  | 7350758581072832105 | 7331214356094396319
```

### 2. 调用API接口

**接口地址**: `POST /web/inner/test-test/ae-price-recalculation`

**请求参数**:
- `file`: Excel文件（MultipartFile）
- `authToken`: 生产环境的认证Token（从生产环境获取）

**cURL示例**:
```bash
curl -X POST "http://localhost:8080/web/inner/test-test/ae-price-recalculation" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@input.xlsx" \
  -F "authToken=YOUR_PRODUCTION_TOKEN"
```

### 3. 响应说明

**成功响应**:
```json
{
  "status": "success",
  "message": "AE价格重算完成",
  "resultFile": "/tmp/ae-price-recalculation/ae-recalc-result-20250820123456.xlsx",
  "timestamp": "20250820123456"
}
```

**失败响应**:
```json
{
  "status": "error", 
  "message": "处理失败: 具体错误信息"
}
```

### 4. 输出Excel文件说明

输出文件包含以下列：
- 原始输入数据（sale_goods_id, spu_code, product_id, shop_id）
- `api_status`: API调用状态（SUCCESS/FAILED）
- `new_sale_price`: 新销售价
- `new_retail_price`: 新划线价  
- `new_national_quote_config`: 新区域价格配置JSON
- `error_message`: 错误信息（如果失败）
- `update_sql`: 生成的UPDATE SQL语句

## SQL语句说明

生成的SQL语句格式：
```sql
UPDATE ae_sale_sku SET 
  last_sale_price = sale_price,
  sale_price = {新销售价},
  last_retail_price = retail_price,
  retail_price = {新划线价},
  national_quote_config = '{新区域价格配置}',
  revised_time = NOW(),
  reviser_name = '202508201'
WHERE 
  sale_goods_id = {销售商品ID}
  AND ships_from_attribute_value_id = 201336100
  AND deleted = 0;
```

## 技术实现要点

### 1. 价格计算API
- **API地址**: `https://prod-nest-api.tiangong.tech/pop-product-service/web/product/manage/v2/ae/pricing/calculate`
- **发货地限制**: 只处理中国发货地（ID: 201336100）
- **认证方式**: Bearer Token
- **响应格式**: `DataResponse<AePricingCalResp>` (标准Blade框架包装器结构)

### 2. 数据处理逻辑
- 按`product_id`和`shop_id`分组，避免重复API调用
- 异步并发处理，控制最大并发数为10
- 只对成功的API响应生成SQL语句
- 一个`sale_goods_id`对应一条SQL（同发货地SKU价格相同）

### 3. 错误处理
- API调用失败会记录具体错误信息
- 继续处理其他商品，不会因单个失败终止整个流程
- 最终统计成功/失败数量

## 注意事项

1. **生产环境Token**: 需要从生产环境获取有效的认证Token
2. **数据备份**: 执行SQL前请务必备份相关数据
3. **工单流程**: 生成的SQL必须通过工单系统执行，不要直接执行
4. **批量处理**: 建议分批处理大量数据，避免单次处理过多商品
5. **验证数据**: 执行SQL前请验证生成的价格数据是否合理

## 相关文件

- 服务类: `tech.tiangong.pop.service.ae.AePriceRecalculationService`
- 控制器: `tech.tiangong.pop.controller.product.inner.TestInnerController#recalculateAePrices`
- 参考实现: `tech.tiangong.pop.service.product.PriceGenerateExcelService`

## 依赖组件

- OkHttp: HTTP客户端调用生产API
- EasyExcel: Excel文件读写
- 现有价格计算模型: `AePricingCalculateReq`, `AePricingCalResp`
- 区域价格配置模型: `AeNationalQuoteConfigDto` (复用现有DTO)
- 并发处理: 异步执行器控制并发数量

## 代码复用说明

- **AeNationalQuoteConfigDto**: 复用现有的区域价格配置DTO，与CallAeComponent保持一致
- **价格计算API**: 复用现有的V2版本定价接口和请求响应模型
- **DataResponse处理**: 正确解析Blade框架的标准API响应包装器
- **JSON序列化**: 使用Blade框架的toJson/parseJson扩展方法，支持TypeReference泛型解析
- **异步处理**: 复用项目的asyncExecutor线程池

## 技术细节说明

### API响应解析
由于生产环境API返回`DataResponse<AePricingCalResp>`包装器结构，代码中使用Jackson的TypeReference正确处理泛型解析：

```kotlin
val dataResponse = responseBody.parseJson(object : TypeReference<DataResponse<AePricingCalResp>>() {})
val apiResponse = dataResponse.data
```

### 区域价格配置生成
复用现有DTO构建national_quote_config JSON：

```kotlin
val nationalQuoteList = chinaResults.map { result ->
    AeNationalQuoteConfigDto().apply {
        this.shipToCountry = result.shipTo
        this.price = result.retailPrice
        this.defaultFLag = if (result.isDefaultShipToForShipFrom) 1 else 0
    }
}
```