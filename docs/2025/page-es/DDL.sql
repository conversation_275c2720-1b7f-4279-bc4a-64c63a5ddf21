CREATE TABLE es_sync_record
(
    id           BIGINT      NOT NULL COMMENT '主键' PRIMARY KEY,
    table_name   VARCHAR(32) NOT NULL COMMENT '业务表名',
    table_id     BIGINT      NOT NULL COMMENT '业务表ID',
    created_time DATETIME    NOT NULL COMMENT '创建时间',
    revised_time DATETIME      NOT NULL COMMENT '更新时间',
    CONSTRAINT uniq_key unique (table_name, table_id)
) comment 'ES同步记录';
CREATE INDEX idx_revised_time ON es_sync_record(revised_time);

CREATE TABLE es_sync_failed_log
(
    id                    BIGINT        NOT NULL COMMENT '主键' PRIMARY KEY,
    table_name            VARCHAR(32)   NOT NULL COMMENT '业务表名',
    table_id              BIGINT        NOT NULL COMMENT '业务表ID',
    exception_message     VARCHAR(200) COMMENT '异常信息',
    exception_stack_trace VARCHAR(2000) NOT NULL COMMENT '异常堆栈',
    created_time          DATETIME      NOT NULL COMMENT '创建时间'
) comment 'ES同步失败日志';

CREATE TABLE sale_goods_aggregation_es_sync_failed_log
(
    id                    BIGINT        NOT NULL COMMENT '主键' PRIMARY KEY,
    product_id            BIGINT        NOT NULL COMMENT '产品ID',
    shop_id               BIGINT        NOT NULL COMMENT '店铺ID',
    exception_message     VARCHAR(200) COMMENT '异常信息',
    exception_stack_trace VARCHAR(2000) NOT NULL COMMENT '异常堆栈',
    created_time          DATETIME      NOT NULL COMMENT '创建时间'
) comment 'sale_goods聚合索引ES同步失败日志';
