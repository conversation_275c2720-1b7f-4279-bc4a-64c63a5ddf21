package tech.tiangong.pop.common.dto

class SpuBaseInfoDto {

    /**
     * 款式风格名称
     */
    var clothingStyleName: String? = null

    /**
     * 款式风格编码
     */
    var clothingStyleCode: String? = null
    /**
     * 企划类型(1:企划内/2:企划外)
     */
    var planningType: Int? = null

    /**
     * 市场编码
     */
    var marketCode: String? = null

    /**
     * 市场系列编码
     */
    var marketSeriesCode: String? = null
    /**
     * 波次
     */
    var waves: String? = null
    /** 货盘类型
     */
    var goodsRepType: String? = null
    /**
     * 织造方式code
     */
    var weaveModeCode: String? = null
    /**
     * 织造方式
     */
    var weaveMode: String? = null
    /**
     * 品质等级
     */
    var qualityLevel: String? = null

    /**
     * 品质等级编号
     */
    var qualityLevelCode: String? = null
    /**
     * 商品主题code
     */
    var productThemeCode: String? = null

    /**
     * 商品主题name
     */
    var productThemeName: String? = null
    /** 店铺ID
     */
    var shopId: Long? = null

    /** 店铺名称
     */
    var shopName: String? = null

    /** 国家站点（多站点-分割）
     */
    var countrys: List<String>? = null
    /** 现货类型【1现货 try on(反季)；2 iFashion】
     */
    var spotType: Int? = null

    /** 现货类型 OPS 编码
     */
    var spotTypeCode: String? = null

    /**
     * 买手id
     */
    var buyerId: Long? = null

    /**
     * 买手名称
     */
    var buyerName: String? = null
    /**
     * 商品链接
     */
    var productLink: String? = null
    /**
     * 商品名（中文）
     */
    var spuName: String? = null
    /**
     * 商品名翻译
     */
    var spuNameTrans: String? = null
}