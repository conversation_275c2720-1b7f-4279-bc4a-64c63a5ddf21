package tech.tiangong.pop.common.enums

/**
 * 平台枚举
 */
enum class PlatformEnum(
    val platformId: Long,
    val platformName: String,
    val channel:ChannelEnum
) {
    LAZADA(1, "Lazada",ChannelEnum.ALIBABA),
    AE(3, "AE",ChannelEnum.ALIBABA),
    TIK_TOK(4, "TikTok",ChannelEnum.BYTEDANCE),
    T_MALL(5, "天猫",ChannelEnum.ALIBABA),
    TAO_BAO(6, "淘宝",ChannelEnum.ALIBABA),
    YI_LIU_BA_BA(7, "1688",ChannelEnum.ALIBABA),
    TEMU(8, "Temu",ChannelEnum.OTHER),
    SHOPEE(9, "Shopee",ChannelEnum.OTHER),

    OTHER(2, "其他",ChannelEnum.OTHER),
    ;


    companion object {
        fun getByPlatformId(platformId: Long): PlatformEnum? {
            return entries.find { it.platformId == platformId }
        }

        fun getByPlatformName(platformName: String): PlatformEnum? {
            return entries.find { it.platformName == platformName }
        }

        fun getByName(name: String): PlatformEnum? {
            return entries.find { it.name == name }
        }
    }
}
