package tech.tiangong.pop.common.enums

/**
 * 店铺业务类型枚举
 */
enum class ProductShopBusinessType(val value: Int, val description: String) {
    POP(1, "POP"),
    SEMI_MANAGED(2, "半托"),
    FULLY_MANAGED(3, "全托管"),
    OVERSEAS_HOSTING(4, "海外托管"),
    UNKNOWN(0, "未知业务类型");

    companion object {
        /**
         * 根据值获取对应的枚举
         */
        fun fromValue(value: Int?): ProductShopBusinessType {
            if (value == null) return UNKNOWN
            return entries.find { it.value == value } ?: UNKNOWN
        }
    }
}
