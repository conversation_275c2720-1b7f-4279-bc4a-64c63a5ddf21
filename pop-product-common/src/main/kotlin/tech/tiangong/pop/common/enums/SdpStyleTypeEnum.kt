package tech.tiangong.pop.common.enums

import java.util.*

/**
 * 款类型: 1-设计款; 2-现货款; 3-数码印花款
 * @create 2025/07/03 15:46
 */
enum class SdpStyleTypeEnum(val code: Int, val desc: String) {
    UNKNOWN(-1,"未知类型"),
    DESIGN(1, "设计款"),   // AI时装
    SPOT(2, "现货款"), // AI现货
    DIGITAL_PRINTING(3, "数码印花款"), // AI POD
    ;


    companion object {
        /**
         * 根据code获取枚举
         *
         * @param desc
         * @return
         */
        @JvmStatic
        fun getByCode(code: Int?): SdpStyleTypeEnum? {
            return entries.firstOrNull { e: SdpStyleTypeEnum -> Objects.equals(e.code, code) }
        }

        /**
         * 根据desc获取枚举
         *
         * @param desc
         * @return
         */
        @JvmStatic
        fun getByDesc(desc: String): SdpStyleTypeEnum? {
            return entries.firstOrNull { e: SdpStyleTypeEnum -> Objects.equals(e.desc, desc) }
        }
    }
}
