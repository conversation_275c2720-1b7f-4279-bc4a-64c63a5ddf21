package tech.tiangong.pop.service.category;

import org.springframework.web.multipart.MultipartFile;
import team.aikero.blade.core.protocol.PageVo;
import tech.tiangong.pop.req.category.*;
import tech.tiangong.pop.resp.category.PublishAttributeGroupVo;
import tech.tiangong.pop.resp.category.PublishAttributeVo;
import tech.tiangong.pop.resp.category.PublishGroupWithAttributeVo;
import tech.tiangong.pop.resp.category.GroupAttributeResp;

import java.util.List;

/**
 * @Author: lhj
 * @DateTime: 2023/6/14 11:22
 * @Description: 上架分组属性
 */
public interface PublishGroupAttrService {

    /**
     * 保存分组
     * @param req
     */
    void saveGroup(SavePublishAttributeGroupReq req);

    /**
     * 查询分组列表
     * @param req
     * @return
     */
    List<PublishAttributeGroupVo> listGroup(PublishAttributeGroupQueryReq req);

    /**
     * 删除分组
     * @param groupIds
     */
    void deleteGroup(List<Long> groupIds);

    /**
     * 保存分组属性
     * @param req
     */
    void saveAttribute(SavePublishAttributeReq req);

    /**
     * 分页查询分组属性列表
     * @param req
     * @return
     */
    PageVo<PublishAttributeVo> pageAttribute(PublishAttributePageQueryReq req);

    /**
     * 分组查询属性列表
     * @param req
     * @return
     */
    List<PublishGroupWithAttributeVo> listGroupWithAttribute(PublishAttributeQueryReq req);

    /**
     * 删除分组属性
     */
    void removeAttribute(List<Long> attributeIds);

    /**
     * 导入属性
     * @param file
     */
    List<String> importAttribute(MultipartFile file);

    /**
     * 修改属性组状态
     */
    void modifyAttributeGroupState(ModifyAttributeGroupStateReq req);

    /**
     * 修改属性状态
     */
    void modifyAttributeState(ModifyAttributeStateReq req);

    /**
     * 统计分组下的属性数量
     * @param groupIds
     */
    void updateAttrAmount(List<Long> groupIds);

    List<PublishAttributeVo> listAttributeWithValueByIds(List<Long> attrIds);

    List<GroupAttributeResp> listAttributesByGroupName(String groupName,Integer listEnable);
}
