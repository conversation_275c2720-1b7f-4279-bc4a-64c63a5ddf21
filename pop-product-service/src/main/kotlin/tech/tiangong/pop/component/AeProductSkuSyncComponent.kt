package tech.tiangong.pop.component

import org.apache.commons.collections4.CollectionUtils
import org.springframework.stereotype.Service
import team.aikero.blade.core.enums.Bool
import team.aikero.blade.core.exception.BusinessException
import team.aikero.blade.core.toolkit.isNotBlank
import team.aikero.blade.core.toolkit.isNotEmpty
import team.aikero.blade.core.toolkit.isNotNull
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.sequence.id.IdHelper
import team.aikero.blade.util.json.parseJson
import team.aikero.blade.util.json.parseJsonList
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.common.constant.ProductConstant
import tech.tiangong.pop.common.enums.ChannelEnum.ALIBABA
import tech.tiangong.pop.common.enums.CountryEnum
import tech.tiangong.pop.common.enums.CurrencyEnum
import tech.tiangong.pop.common.enums.PlatformEnum.AE
import tech.tiangong.pop.common.enums.ProductAePublishStateEnum
import tech.tiangong.pop.common.enums.ProductPublishStateEnum
import tech.tiangong.pop.config.AliexpressProperties
import tech.tiangong.pop.config.CommonProperties
import tech.tiangong.pop.dao.entity.*
import tech.tiangong.pop.dao.repository.*
import tech.tiangong.pop.dto.AeNationalQuoteConfigDto
import tech.tiangong.pop.dto.AePlatformNationalQuoteConfigDto
import tech.tiangong.pop.enums.PlatformSyncStateEnum
import tech.tiangong.pop.enums.PriceCalculateRuleEnum
import tech.tiangong.pop.enums.ae.AeAlwaysPushStatus
import tech.tiangong.pop.helper.PlatformCategoryHelper
import tech.tiangong.pop.helper.cache.AliexpressServiceHelper
import tech.tiangong.pop.req.product.FillProductStyleTypeReq
import tech.tiangong.pop.req.product.RefreshProductImagePackageStateReq
import tech.tiangong.pop.resp.category.LzdCategoryVO
import tech.tiangong.pop.resp.sdk.aliexpress.QueryProductDetailResult
import tech.tiangong.pop.service.product.ProductManageService
import tech.tiangong.pop.service.product.RepairProductService
import tech.tiangong.pop.service.settings.CurrencyExchangeRateService
import java.math.RoundingMode
import java.util.*

/**
 * 商品同步-sku
 * <AUTHOR>
 * @date 2025-5-3 17:55:24
 */
@Slf4j
@Service
class AeProductSkuSyncComponent(
    private val productRepository: ProductRepository,
    private val productSkcRepository: ProductSkcRepository,
    private val aeSaleGoodsRepository: AeSaleGoodsRepository,
    private val aeSaleSkcRepository: AeSaleSkcRepository,
    private val aeSaleSkuRepository: AeSaleSkuRepository,
    private val shopRepository: ShopRepository,
    private val aliexpressProperties: AliexpressProperties,
    private val sizeMapRepository: SizeMapRepository,
    private val publishCategoryMappingRepository: PublishCategoryMappingRepository,
    private val publishCategoryRepository: PublishCategoryRepository,
    private val productBarCodeRepository: ProductBarCodeRepository,
    private val colorComponent: ColorComponent,
    private val repairProductService: RepairProductService,
    private val aliexpressServiceHelper: AliexpressServiceHelper,
    private val currencyExchangeRateService: CurrencyExchangeRateService,
    private val productManageService: ProductManageService,
    private val commonProperties: CommonProperties,
) {
    /**
     * 同步本地商品
     *
     * @param shopId
     * @param platformProductId
     * @param aeSpu        解析AE商品SPU数据结构: product_id + shop_id 唯一
     * @param aeSkuList    解析AE商品SKU数据结构: sku_id唯一
     */
    fun syncSku(
        shopId: Long,
        platformProductId: String,
        aeSpu: AeOriginalProductSpu,
        aeSkuList: List<AeOriginalProductSku>,
    ) {
        log.info { "开始处理商品" }

        val shop = shopRepository.getById(shopId)
        if (shop == null) {
            log.error { "店铺不存在 shop_id: $shopId" }
            throw IllegalArgumentException("店铺不存在 shop_id: $shopId")
        }
        var product: Product? = null
        var saleGoods: AeSaleGoods? = null
        val existSaleGoods = aeSaleGoodsRepository.getByProductIdAndShopId(platformProductId.toLong(), shopId)
        if (existSaleGoods == null) {
            // 无saleGoods
            // 检查SPU是否存在
            val existProduct = productRepository.getBySpuCode(aeSkuList.first().extractSpuCode!!)
            if (existProduct != null) {
                // 有product
                product = existProduct
                saleGoods = aeSaleGoodsRepository.getByProductIdAndShopId(product.productId!!, shopId)
            }
        } else {
            // 有saleGoods
            saleGoods = existSaleGoods
            product = productRepository.getById(saleGoods.productId)
        }

        var isNewProduct = false
        if (product == null) {
            product = Product()
            product.productId = IdHelper.getId()
            product.shopId = shop.shopId
            product.shopName = shop.shopName
            product.brandName = shop.brandName
            product.channelId = ALIBABA.channelId.toString()
            product.platformId = AE.platformId.toString()
            product.initTemplate = Bool.NO.code
            product.productTitle = aeSpu.title
            product.isHistory = Bool.YES.code
            product.publishState = ProductPublishStateEnum.ACTIVE.code
            product.isSyncPlatform = Bool.YES.code
            product.packageDimensionsHeight = aeSpu.packageHeight
            product.packageDimensionsWidth = aeSpu.packageWidth
            product.packageDimensionsLength = aeSpu.packageLength
            product.packageWeight = aeSpu.packageWeight
            product.platformSyncState = PlatformSyncStateEnum.SUCCESS.code
            product.sizeGroupName = "字母码"
            product.spuCode = aeSkuList.first().extractSpuCode
            product.sizeGroupCode = ProductConstant.SOURCE_GROUP_CODE
            val images = aeSpu.webDetailImages?.parseJsonList(String::class.java)
            if (CollectionUtils.isNotEmpty(images)) {
                product.mainImgUrl = images?.first()
            }
            product.categoryId = aeSpu.categoryId
            productRepository.save(product)
            isNewProduct = true
        }

        if (saleGoods == null) {
            // 新增sale goods
            saleGoods = AeSaleGoods()
            saleGoods.saleGoodsId = IdHelper.getId()
            saleGoods.productId = product.productId
            saleGoods.publishState = ProductAePublishStateEnum.getByPlatformStateValue(aeSpu.productStatusType!!)?.code
            saleGoods.platformProductId = aeSpu.productId
            saleGoods.channelId = 1
            saleGoods.platformId = AE.platformId
            saleGoods.isHistory = Bool.NO.code
            saleGoods.productTitle = aeSpu.title
            saleGoods.spuCode = product.spuCode
            saleGoods.publishTime = aeSpu.gmtCreate
            saleGoods.createdTime = aeSpu.gmtCreate
            saleGoods.latestPublishTime = aeSpu.gmtModified
            saleGoods.revisedTime = aeSpu.gmtModified
            saleGoods.shopId = shop.shopId
            saleGoods.shopName = shop.shopName
            saleGoods.brandId = shop.brandId?.toLong()
            saleGoods.platformSyncState = PlatformSyncStateEnum.SUCCESS.code
            saleGoods.alwaysPushStatus = AeAlwaysPushStatus.NONE
            saleGoods.platformCategoryId = aeSpu.categoryId?.toString()
            saleGoods.packageWeight = aeSpu.packageWeight
            saleGoods.packageDimensionsLength = aeSpu.packageLength
            saleGoods.packageDimensionsHeight = aeSpu.packageHeight
            saleGoods.packageDimensionsWidth = aeSpu.packageWidth
            saleGoods.taxType = aeSpu.taxType
            saleGoods.promiseTemplateId = aeSpu.promiseTemplateId?.toLong()
            saleGoods.freightTemplateId = aeSpu.freightTemplateId?.toLong()
            saleGoods.msrId = aeSpu.msrEuId?.toLong()
            saleGoods.manufactureId = aeSpu.manufacturerId?.toLong()

            // 获取品类关系
            val categoryMapping: PublishCategoryMapping = publishCategoryMappingRepository.getByPlatformCategoryId(
                aeSpu.categoryId.toString(),
                AE.platformId,
                ALIBABA.channelId
            ) ?: throw BusinessException("找不到品类映射信息")

            saleGoods.platformCategoryName = categoryMapping.platformCategoryName
            // 获取所有品类
            val allCategoryList = publishCategoryRepository.listWithCache()
            // key=getPublishCategoryId
            val allCategoryMap = allCategoryList.associateBy { it.publishCategoryId }
            saleGoods.categoryCode = PlatformCategoryHelper.splicingCategoryCode(categoryMapping.publishCategoryId, allCategoryMap)

            saleGoods.priceCalculateRule = PriceCalculateRuleEnum.V2.code
            aeSaleGoodsRepository.save(saleGoods)

            // 更新product
            productRepository.updateById(Product().apply {
                this.productId = product.productId
                this.categoryId = categoryMapping.publishCategoryId
                this.categoryCode = saleGoods.categoryCode
                this.categoryName = PlatformCategoryHelper.splicingCategoryName(categoryMapping.publishCategoryId, allCategoryMap)
            })
        } else {
            // spu逻辑
            var isUpdateSpu = false
            if (saleGoods.productTitle != aeSpu.title) {
                saleGoods.productTitle = aeSpu.title
                isUpdateSpu = true
            }
            val status = ProductAePublishStateEnum.getByPlatformStateValue(aeSpu.productStatusType!!)
            if (status != null) {
                if (saleGoods.publishState != status.code) {
                    saleGoods.publishState = status.code
                    isUpdateSpu = true
                }
            }
            if (aeSpu.freightTemplateId.isNotBlank() && saleGoods.freightTemplateId != aeSpu.freightTemplateId?.toLong()) {
                saleGoods.freightTemplateId = aeSpu.freightTemplateId?.toLong()
                isUpdateSpu = true
            }
            if (isUpdateSpu) {
                aeSaleGoodsRepository.updateById(saleGoods)
            }
        }

        // 处理商品
        existProductHandler(product, shop, saleGoods, aeSpu, aeSkuList)

        // 匹配barcode 更新skc表skc,sku表barcode,barcode表sellerSku
        try {
            updateSkcAndBarcode(saleGoods)
        } catch (e: Exception) {
            log.error(e) { "匹配barcode失败 product_id: ${product.productId}" }
        }

        // 标记异常检查
        try {
            repairProductService.aeCheckError(saleGoods)
        } catch (e: Exception) {
            log.error(e) { "异常商品检查标记失败 product_id: ${product.productId}" }
        }

        if (product.styleType == null) {
            try {
                val req = FillProductStyleTypeReq()
                req.productIds = listOf(product.productId!!)
                productManageService.fillStyleType(req)
            } catch (e: Exception) {
                log.error(e) { "同步商品款式类型失败 product_id: ${product.productId}" }
            }
        }
        if (product.imagePackageState == null) {
            try {
                val req = RefreshProductImagePackageStateReq()
                req.productIds = listOf(product.productId!!)
                productManageService.refreshImagePackageState(req)
            } catch (e: Exception) {
                log.error(e) { "同步商品图包状态失败 product_id: ${product.productId}" }
            }
        }
        log.info { "结束处理商品" }
    }


    /**
     * 处理已存在的商品
     *
     * @param product
     * @param shop
     * @param aeSpu
     * @param aeSkuList
     */
    private fun existProductHandler(
        product: Product,
        shop: Shop,
        saleGoods: AeSaleGoods,
        aeSpu: AeOriginalProductSpu,
        aeSkuList: List<AeOriginalProductSku>,
    ) {

        // 颜色字典
        val colorList = colorComponent.getColorMap()
        // 按平台颜色分组skc
        val saleSkcList = aeSaleSkcRepository.findBySaleGoodsId(saleGoods.saleGoodsId!!)
        val saleSkcPlatformColorMap = saleSkcList.associateBy { it.colorCode }.toMutableMap()

        // 处理AE币种
        val aliexpressCurrencyType = aliexpressServiceHelper.getCachedSellerRelations(shop.shopId!!).sellerRelationList?.firstOrNull()?.channelCurrency
            ?: aliexpressProperties.aePlatform.currencyCode // 默认CNY

        // 提取区域定价
        val resultSpu = aeSpu.sourceDetailJson?.parseJson<QueryProductDetailResult>()
        val nationalQuoteConfigList = resultSpu?.nationalQuoteConfig?.configurationData?.parseJsonList(AePlatformNationalQuoteConfigDto::class.java)

        // 循环aeSku
        aeSkuList.forEach { aeSku ->
            // 字典
            // 1. 颜色属性 匹配 colorCode
            var dictColor = colorList.firstOrNull { it.colorCode == aeSku.colorPropertyValueName }
            if (dictColor == null) {
                // 若找不到, sellerSku解析的颜色 匹配 colorCode
                dictColor = colorList.firstOrNull { it.colorCode == aeSku.color }
            }
            if (dictColor == null) {
                // 若找不到, sellerSku解析的颜色 匹配 简写
                dictColor = colorList.firstOrNull { it.colorAabbr == aeSku.color }
            }

            // 匹配平台颜色
            var saleSkc = saleSkcPlatformColorMap[dictColor?.colorCode ?: aeSku.color]
            if (saleSkc == null) {
                saleSkc = AeSaleSkc().apply {
                    this.saleSkcId = IdHelper.getId()
                    this.saleGoodsId = saleGoods.saleGoodsId
//                    this.productSkcId = skc?.productSkcId
//                    this.skc = skc?.skc
                    this.color = dictColor?.color ?: aeSku.color
                    this.colorPropertyValueId = aeSku.colorPropertyValueId
                    this.colorCode = dictColor?.colorCode ?: aeSku.color
                    this.colorAbbrCode = dictColor?.colorAabbr ?: aeSku.color
                    this.platformColor = aeSku.colorPropertyValueName ?: dictColor?.colorCode ?: aeSku.color
//                    this.pictures = newPictures
                    this.state = Bool.YES.code
                    this.combo = Bool.NO.code
//                    this.cbPrice = skc?.cbPrice
//                    this.localPrice = skc?.localPrice
//                    this.purchasePrice = skc?.purchasePrice
//                    this.costPrice = skc?.costPrice
                }
                aeSaleSkcRepository.save(saleSkc)
                saleSkcPlatformColorMap[dictColor?.colorCode ?: aeSku.color] = saleSkc
            } else {
                // 如果平台颜色不一致则修改
                if (aeSku.colorPropertyValueName != null && saleSkc.platformColor != aeSku.colorPropertyValueName) {
                    val updateSkc = AeSaleSkc().apply {
                        this.saleSkcId = saleSkc.saleSkcId
                        this.platformColor = aeSku.colorPropertyValueName
                    }
                    aeSaleSkcRepository.updateById(updateSkc)
                }
            }

            val saleSku = aeSaleSkuRepository.getBySaleGoodsIdAndSkuId(saleGoods.saleGoodsId!!, aeSku.skuId!!.toString())
            if (saleSku == null) {
                // 新增sale sku
                val newSku = AeSaleSku().apply {
                    this.saleSkuId = IdHelper.getId()
                    this.shopSku = aeSku.id
                    this.platformSkuId = aeSku.skuId?.toString()
                    this.productId = product.productId
                    this.productSkcId = saleSkc.productSkcId
                    this.saleGoodsId = saleGoods.saleGoodsId
                    this.saleSkcId = saleSkc.saleSkcId
                    this.stockQuantity = aeSku.quantity?.toLong()
                    this.sizeName = sizeMapRepository.getByLzdSize(aeSku.size ?: "")

                    // 解析区域定价
                    val saleNationalQuoteConfigList = mutableListOf<AeNationalQuoteConfigDto>()
                    nationalQuoteConfigList?.forEach { na ->
                        na.absoluteQuoteMap?.forEach { (aeSkuId, aePrice) ->
                            if (aeSku.id == aeSkuId && aePrice.isNotBlank()) {
                                saleNationalQuoteConfigList.add(AeNationalQuoteConfigDto().apply {
                                    this.shipToCountry = na.shiptoCountry
                                    this.price = aePrice.toBigDecimal()
                                })
                            }
                        }
                    }

                    if (CurrencyEnum.USD.code.equals(aliexpressCurrencyType, ignoreCase = true)) {
                        // 如果是美元，则需要将价格转换为人民币
                        val exchangeRate = currencyExchangeRateService.getExchangeRate(CurrencyEnum.CNY.code, CountryEnum.US.code) ?: throw BusinessException("获取USD兑换CNY汇率失败")
                        val newPrice = aeSku.skuPrice!!.divide(exchangeRate, 2, RoundingMode.HALF_UP)
                        this.salePrice = newPrice
                        this.retailPrice = newPrice
                        // 解析区域定价, 并按汇率转换
                        if (saleNationalQuoteConfigList.isNotEmpty()) {
                            this.nationalQuoteConfig = saleNationalQuoteConfigList.map { naConfigTmp ->
                                // 如果是美元，则需要将价格转换为人民币
                                AeNationalQuoteConfigDto().apply {
                                    this.shipToCountry = naConfigTmp.shipToCountry
                                    this.price = naConfigTmp.price!!.divide(exchangeRate, 2, RoundingMode.HALF_UP)
                                }
                            }.toJson()
                        }
                    } else {
                        this.salePrice = aeSku.skuPrice
                        this.retailPrice = aeSku.skuPrice
                        if (saleNationalQuoteConfigList.isNotEmpty()) {
                            this.nationalQuoteConfig = saleNationalQuoteConfigList.toJson()
                        }
                    }

                    this.enableState = Bool.YES.code
                    this.sellerSku = aeSku.skuCode
                    this.platformProductId = aeSku.productId
                    this.platformCategoryId = saleGoods.platformProductId?.toString()
                    this.platformCategoryName = saleGoods.platformCategoryName
                    this.shopName = shop.shopName
                    this.brandId = saleGoods.brandId
                    this.brandName = saleGoods.brandName
                    this.publishState = ProductAePublishStateEnum.ACTIVE.code
                    this.colorPropertyValueId = aeSku.colorPropertyValueId
                    this.sizePropertyValueId = aeSku.sizePropertyValueId
                    this.shipsFromAttributeId = aeSku.shipsFromAttributeId
                    this.shipsFromAttributeName = aeSku.shipsFromAttributeName
                    this.shipsFromAttributeValueId = aeSku.shipsFromAttributeValueId
                    this.shipsFromAttributeValueName = aeSku.shipsFromAttributeValueName
                    if (aeSku.shipsFromAttributeValueId == null) {
                        this.shipsFromAttributeId = aliexpressProperties.aePlatform.shipsFromPropertyId
                        this.shipsFromAttributeName = aliexpressProperties.aePlatform.shipsFromPropertyName
                        this.shipsFromAttributeValueId = aliexpressProperties.aePlatform.shipsFromPropertyValueId
                        this.shipsFromAttributeValueName = aliexpressProperties.aePlatform.shipsFromPropertyValueName
                    }
                }
                aeSaleSkuRepository.save(newSku)
            } else {
                // 更新sale sku
                // 比较并更新主要字段
                var isUpdate = false
                if (saleSku.productSkcId != saleSkc.productSkcId) {
                    saleSku.productSkcId = saleSkc.productSkcId
                    isUpdate = true
                }
                if (saleSku.saleSkcId != saleSkc.saleSkcId) {
                    saleSku.saleSkcId = saleSkc.saleSkcId
                    isUpdate = true
                }
                if (saleSku.shopSku != aeSku.id) {
                    saleSku.shopSku = aeSku.id
                    isUpdate = true
                }
                var salePrice = aeSku.skuPrice
                var retailPrice = aeSku.skuPrice
                if (CurrencyEnum.USD.code.equals(aliexpressCurrencyType, ignoreCase = true)) {
                    // 如果是美元，则需要将价格转换为人民币
                    val exchangeRate = currencyExchangeRateService.getExchangeRate(CurrencyEnum.CNY.code, CountryEnum.US.code) ?: throw BusinessException("获取USD兑换CNY汇率失败")
                    val newPrice = aeSku.skuPrice!!.divide(exchangeRate, 2, RoundingMode.HALF_UP)
                    salePrice = newPrice
                    retailPrice = newPrice
                }
                if (saleSku.salePrice != salePrice) {
                    saleSku.salePrice = salePrice
                    isUpdate = true
                }
                if (saleSku.retailPrice != retailPrice) {
                    saleSku.retailPrice = retailPrice
                    isUpdate = true
                }
                if (saleSku.stockQuantity != aeSku.quantity?.toLong()) {
                    saleSku.stockQuantity = aeSku.quantity?.toLong()
                    isUpdate = true
                }
                if (saleSku.publishState != saleGoods.publishState) {
                    saleSku.publishState = saleGoods.publishState
                    isUpdate = true
                }
                if (saleSku.enableState != Bool.YES.code) {
                    saleSku.enableState = Bool.YES.code
                    isUpdate = true
                }
                if (aeSku.colorPropertyValueId.isNotNull() && saleSku.colorPropertyValueId != aeSku.colorPropertyValueId) {
                    saleSku.colorPropertyValueId = aeSku.colorPropertyValueId
                    isUpdate = true
                }
                if (aeSku.sizePropertyValueId.isNotNull() && saleSku.sizePropertyValueId != aeSku.sizePropertyValueId) {
                    saleSku.sizePropertyValueId = aeSku.sizePropertyValueId
                    isUpdate = true
                }
                if (aeSku.shipsFromAttributeId.isNotNull() && saleSku.shipsFromAttributeId != aeSku.shipsFromAttributeId) {
                    saleSku.shipsFromAttributeId = aeSku.shipsFromAttributeId
                    isUpdate = true
                }
                if (aeSku.shipsFromAttributeName.isNotNull() && saleSku.shipsFromAttributeName != aeSku.shipsFromAttributeName) {
                    saleSku.shipsFromAttributeName = aeSku.shipsFromAttributeName
                    isUpdate = true
                }
                if (aeSku.shipsFromAttributeValueId.isNotNull() && saleSku.shipsFromAttributeValueId != aeSku.shipsFromAttributeValueId) {
                    saleSku.shipsFromAttributeValueId = aeSku.shipsFromAttributeValueId
                    isUpdate = true
                }
                if (aeSku.shipsFromAttributeValueName.isNotNull() && saleSku.shipsFromAttributeValueName != aeSku.shipsFromAttributeValueName) {
                    saleSku.shipsFromAttributeValueName = aeSku.shipsFromAttributeValueName
                    isUpdate = true
                }
                if (isUpdate) {
                    aeSaleSkuRepository.updateById(saleSku)
                }
            }
        }

        /*
        AE SKU更新逻辑: (sku_id: AE平台全局唯一; id: 属性id拼接, 可能会重复)
        1. 通过销售属性拼接成id: 格式如"14:193;5:361386;200007763:201336100", 通过分号区分三个属性(尺码, 颜色, 发货地)
        2. 更新SKU不需要传sku_id/id, AE接口会通过属性拼接成id, 自动判断是否新增还是更新
        3. 如果id是新增的, AE接口会自动生成新的SKU, 如果更新报文没有已存在的id, AE会直接删除

        本地逻辑:
        1. 需要先把同步回来的sku_id upsert入库(上面的逻辑已经完成)
        2. 再把差集的sku_id, 本地禁用(enable=0, 其实是假删除, 因为其他逻辑入口已经不会恢复enable=1; 不使用deleted=1是因为可能下游会查询使用, 所以使用enable模拟假删除)
         */
        // 提取AE的sku_id
        val aeSkuIds = aeSkuList.map { it.skuId.toString() }.distinct()
        // 获取本地的sku_id(enable=1)
        val localSkus = aeSaleSkuRepository.findBySaleGoodsId(saleGoods.saleGoodsId!!).filter { it.enableState == Bool.YES.code }
        // 找出localSkuIds不存在于aeSkuIds的sku
        val diffSkus = localSkus.filter { !aeSkuIds.contains(it.platformSkuId) }
        // 禁用这些sku
        if (CollectionUtils.isNotEmpty(diffSkus)) {
            val updateSkus = diffSkus.map {
                AeSaleSku().apply {
                    this.saleSkuId = it.saleSkuId
                    this.enableState = Bool.NO.code
                }
            }
            aeSaleSkuRepository.updateBatchById(updateSkus)
        }
    }

    /**
     * 递归方法：根据 categoryId 查找 LzdCategoryVO
     *
     * @param categories
     * @param categoryId
     * @return
     */
    private fun findCategoryById(categories: List<LzdCategoryVO>, categoryId: Long?): LzdCategoryVO? {
        for (category in categories) {
            if (Objects.equals(category.categoryId, categoryId)) {
                return category  // 找到匹配的 categoryId
            }
            // 如果有子节点，递归查找子节点
            if (category.children != null && category.children!!.isNotEmpty()) {
                val result = findCategoryById(category.children!!, categoryId)
                if (result != null) {
                    return result  // 在子节点中找到了匹配的 categoryId
                }
            }
        }
        return null  // 没有找到
    }


    // Method to find the category path by categoryId
    private fun findCategoryPathById(categories: List<LzdCategoryVO>, categoryId: Long): String? {
        for (category in categories) {
            // Check if the current category is the one we're looking for
            if (category.categoryId == categoryId) {
                return category.name // Base case: the category itself
            }

            // Recursively search in the children
            val categoryChildren = category.children
            if (!categoryChildren.isNullOrEmpty()) {
                val path = findCategoryPathById(categoryChildren, categoryId)
                if (path != null) {
                    return category.name + ">" + path // Append the current category name to the path
                }
            }
        }
        return null // Return null if the categoryId is not found
    }

    /**
     * 匹配barcode 更新skc表skc,sku表barcode,barcode表sellerSku
     * @param saleGoods
     */
    private fun updateSkcAndBarcode(saleGoods: AeSaleGoods) {

        // 找到所有skc
        val saleSkcList = aeSaleSkcRepository.findBySaleGoodsId(saleGoods.saleGoodsId!!)
        if (saleSkcList.isEmpty()) {
            return
        }
        // 找到所有sku
        val skcIds = saleSkcList.mapNotNull { it.saleSkcId }
        val saleSkuList = aeSaleSkuRepository.findBySaleSkcIds(skcIds).filter { it.enableState == Bool.YES.code }
        if (saleSkuList.isEmpty()) {
            return
        }
        // map key=skcId
        val skcMap = saleSkcList.associateBy { it.saleSkcId }
        saleSkuList.forEach { sku ->

            val skc = skcMap[sku.saleSkcId] ?: return@forEach

            // 拿到barcode/更新skc
            if (saleGoods.spuCode.isNotBlank()
                && sku.sizeName.isNotBlank()
                && skc.color.isNotBlank()
            ) {
                // 常规格式匹配
                var barcode = productBarCodeRepository.getBySpuCodeAndSizeAndColor(saleGoods.spuCode!!, sku.sizeName!!, skc.color!!)
                if (barcode == null && skc.skc != null) {
                    // 找不到, 使用特殊格式: skc-尺码
                    barcode = productBarCodeRepository.getBySkcAndSize(skc.skc!!, sku.sizeName!!)
                }
                if (barcode != null) {
                    // 更新sku的barcode
                    if (sku.barcode.isNullOrBlank()) {
                        sku.barcode = barcode.barcode
                        aeSaleSkuRepository.updateById(sku)
                    }
                    // 更新skc的skc
                    if (skc.skc.isNullOrBlank()) {
                        skc.skc = barcode.skc
                        aeSaleSkcRepository.updateById(skc)
                    }
                    // 更新barcode的sellerSku
                    if (sku.sellerSku.isNotBlank()) {
                        val currentSellerSkus = barcode.sellerSku?.split(",")?.toMutableList() ?: mutableListOf()
                        if (!currentSellerSkus.contains(sku.sellerSku)) {
                            currentSellerSkus.add(sku.sellerSku!!)
                            barcode.sellerSku = currentSellerSkus.joinToString(",")
                            productBarCodeRepository.updateById(barcode)
                        }
                    }

                }
            }
            if (skc.skc.isNotBlank() && skc.productSkcId.isNotNull()) {
                val pc = productSkcRepository.getById(skc.productSkcId!!)
                if (pc != null && pc.skc.isNullOrBlank()) {
                    productSkcRepository.updateById(ProductSkc().apply {
                        this.productSkcId = pc.productSkcId
                        this.skc = skc.skc
                    })
                }
            }

            if (sku.colorPropertyValueId != null && sku.colorPropertyValueId != skc.colorPropertyValueId) {
                skc.colorPropertyValueId = sku.colorPropertyValueId
                aeSaleSkcRepository.updateById(AeSaleSkc().apply {
                    this.saleSkcId = skc.saleSkcId
                    this.colorPropertyValueId = skc.colorPropertyValueId
                })
            }
        }
    }
}
