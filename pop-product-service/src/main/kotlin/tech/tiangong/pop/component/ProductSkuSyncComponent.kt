package tech.tiangong.pop.component

import cn.hutool.core.date.LocalDateTimeUtil
import com.alibaba.fastjson2.JSON
import org.apache.commons.collections4.CollectionUtils
import org.springframework.stereotype.Service
import team.aikero.blade.core.enums.Bool
import team.aikero.blade.core.toolkit.isNotBlank
import team.aikero.blade.core.toolkit.isNotEmpty
import team.aikero.blade.core.toolkit.isNotNull
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.sequence.id.IdHelper
import team.aikero.blade.util.json.parseJson
import team.aikero.blade.util.json.parseJsonList
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.common.constant.ProductConstant
import tech.tiangong.pop.common.enums.CountryEnum
import tech.tiangong.pop.common.enums.PlatformEnum
import tech.tiangong.pop.common.enums.ProductPublishStateEnum
import tech.tiangong.pop.config.CommonProperties
import tech.tiangong.pop.dao.entity.*
import tech.tiangong.pop.dao.repository.*
import tech.tiangong.pop.enums.LazadaProductStateEnum
import tech.tiangong.pop.enums.LazadaSkuStateEnum
import tech.tiangong.pop.enums.PlatformSyncStateEnum
import tech.tiangong.pop.enums.PriceCalculateRuleEnum
import tech.tiangong.pop.helper.PlatformCategoryHelper
import tech.tiangong.pop.req.product.FillProductStyleTypeReq
import tech.tiangong.pop.req.product.RefreshProductImagePackageStateReq
import tech.tiangong.pop.req.product.SaleSkuFetchReq
import tech.tiangong.pop.resp.category.LzdCategoryVO
import tech.tiangong.pop.resp.product.ProductTitleResp
import tech.tiangong.pop.resp.sdk.lazada.LazadaProductItemDetailResp.ProductData.Attributes
import tech.tiangong.pop.service.ProductPublishStoreService
import tech.tiangong.pop.service.lazada.LazadaApiService
import tech.tiangong.pop.service.product.FixProductService
import tech.tiangong.pop.service.product.ProductManageService
import tech.tiangong.pop.service.product.RepairProductService
import java.time.LocalDateTime
import java.util.*

/**
 * 商品同步-sku
 * <AUTHOR>
 * @date 2025-2-14 14:37:12
 */
@Slf4j
@Service
class ProductSkuSyncComponent(
    private val productRepository: ProductRepository,
    private val productSkcRepository: ProductSkcRepository,
    private val saleGoodsRepository: SaleGoodsRepository,
    private val saleSkuRepository: SaleSkuRepository,
    private val shopRepository: ShopRepository,
    private val repairProductService: RepairProductService,
    private val lazadaApiService: LazadaApiService,
    private val lazadaBrandRepository: LazadaBrandRepository,
    private val fixProductService: FixProductService,
    private val publishCategoryMappingRepository: PublishCategoryMappingRepository,
    private val publishCategoryRepository: PublishCategoryRepository,
    private val productBarCodeRepository: ProductBarCodeRepository,
    private val colorComponent: ColorComponent,
    private val saleSkcRepository: SaleSkcRepository,
    private val productManageService: ProductManageService,
    private val commonProperties:CommonProperties,
    private val productPublishStoreService:ProductPublishStoreService,
) {

    /**
     * 同步本地商品
     *
     * @param shopId
     * @param productId
     * @param platformProductId
     * @param country
     * @param lzdSpu        解析lzd商品SPU数据结构
     * @param lzdSkuList    解析lzd商品SKU数据结构
     */
    fun syncSku(
        shopId: Long,
        productId: Long?,
        platformProductId: String,
        country: String,
        lzdSpu: LazadaOriginalProductSpu,
        lzdSkuList: List<LazadaOriginalProductSku>,
    ) {
        log.info { "开始处理商品" }

        val shop = shopRepository.getById(shopId)
        var product = if (productId != null) {
            productRepository.getById(productId)
        } else {
            // 无productId
            // shop + pid查询 product
            val checkSaleGoods = saleGoodsRepository.getByShopIdAndPidAndCountry(shopId, platformProductId, country)
            if (checkSaleGoods != null) {
                productRepository.getById(checkSaleGoods.productId)
            } else {
                productRepository.getBySpuCode(lzdSkuList.first().extractSpuCode!!)
            }
        }

        if (product == null) {
            val newProduct = Product()
            newProduct.productId = IdHelper.getId()
            newProduct.shopId = shop.shopId
            newProduct.shopName = shop.shopName
            newProduct.brandName = shop.brandName
            newProduct.channelId = "1"
            newProduct.platformId = "1"
            newProduct.initTemplate = Bool.NO.code
            newProduct.productTitle = lzdSpu.title
            newProduct.isHistory = Bool.YES.code
            newProduct.publishState = ProductPublishStateEnum.ACTIVE.code
            newProduct.isSyncPlatform = Bool.YES.code
            newProduct.packageDimensionsHeight = lzdSkuList.first().packageHeight?.toString()
            newProduct.packageDimensionsWidth = lzdSkuList.first().packageWidth?.toString()
            newProduct.packageDimensionsLength = lzdSkuList.first().packageLength?.toString()
            newProduct.packageWeight = lzdSkuList.first().packageWeight?.toString()
            newProduct.platformSyncState = PlatformSyncStateEnum.SUCCESS.code
            newProduct.sizeGroupName = "字母码"
            newProduct.spuCode = lzdSkuList.first().extractSpuCode
            newProduct.sizeGroupCode = ProductConstant.SOURCE_GROUP_CODE
            val images = lzdSpu.images?.parseJsonList(String::class.java)
            if (CollectionUtils.isNotEmpty(images)) {
                newProduct.mainImgUrl = images?.first()
            }
            val categoryTreeByCountryCode = lazadaApiService.getCategoryTreeByCountryCode(country)
            if (categoryTreeByCountryCode.isSuccess()) {
                val categoryList = JSON.parseArray(JSON.toJSONString(categoryTreeByCountryCode.data), LzdCategoryVO::class.java)
                val categoryById = findCategoryById(categoryList, lzdSpu.primaryCategory)
                if (Objects.nonNull(categoryById)) {
                    newProduct.categoryId = categoryById?.categoryId
                }
            }
            productRepository.save(newProduct)
            product = newProduct
        }

        // 处理商品
        existProductHandler(product, shop, country, lzdSpu, lzdSkuList)

        // 转换异常尺码
        try {
            val req = SaleSkuFetchReq()
            req.productIds = mutableSetOf(product.productId!!)
            fixProductService.updateSaleSkuSizeNames(req)
        } catch (e: Exception) {
            log.error(e) { "转换异常尺码失败 product_id: ${product.productId}" }
        }

        // 匹配barcode 更新skc表skc,sku表barcode,barcode表sellerSku
        try {
            updateSkcAndBarcode(product)
        } catch (e: Exception) {
            log.error(e) { "匹配barcode失败 product_id: ${product.productId}" }
        }

        // skc下没有sku, 则标记skc状态为取消
        try {
            checkSkuStats(product)
        } catch (e: Exception) {
            log.error(e) { "检查skc状态失败 product_id: ${product.productId}" }
        }

        // 标记异常检查
        try {
            val saleGoodsList = saleGoodsRepository.getByProduct(product.productId!!)
            if (saleGoodsList.isNotEmpty()) {
                saleGoodsList.forEach { saleGoods ->
                    repairProductService.lazadaCheckError(saleGoods)
                }
            }
        } catch (e: Exception) {
            log.error(e) { "异常商品检查标记失败 product_id: ${product.productId}" }
        }

        if (product.styleType == null) {
            try {
                val req = FillProductStyleTypeReq()
                req.productIds = listOf(product.productId!!)
                productManageService.fillStyleType(req)
            } catch (e: Exception) {
                log.error(e) { "同步商品款式类型失败 product_id: ${product.productId}" }
            }
        }
        if (product.imagePackageState == null) {
            try {
                val req = RefreshProductImagePackageStateReq()
                req.productIds = listOf(product.productId!!)
                productManageService.refreshImagePackageState(req)
            } catch (e: Exception) {
                log.error(e) { "同步商品图包状态失败 product_id: ${product.productId}" }
            }
        }
        log.info { "结束处理商品" }
    }


    /**
     * 处理已存在的商品
     *
     * @param product
     * @param shop
     * @param lzdSpu
     * @param lzdSkuList
     */
    private fun existProductHandler(
        product: Product,
        shop: Shop,
        country: String,
        lzdSpu: LazadaOriginalProductSpu,
        lzdSkuList: List<LazadaOriginalProductSku>,
    ) {

        // 拿到sale_goods, 如果空则新增
        var saleGoods = saleGoodsRepository.getByProductIdAndPid(product.productId!!, lzdSpu.itemId.toString())
        if (saleGoods != null) {

            // spu逻辑
            var isUpdateSpu = false
            if (saleGoods.productTitle != lzdSpu.title) {
                saleGoods.productTitle = lzdSpu.title
                isUpdateSpu = true
                // product也要处理
                setProductTitle(product, country, lzdSpu.title!!)
            }
            if (LazadaProductStateEnum.ACTIVE.code == lzdSpu.lzdStatus) {
                if (saleGoods.publishState != ProductPublishStateEnum.ACTIVE.code) {
                    saleGoods.publishState = ProductPublishStateEnum.ACTIVE.code
                    isUpdateSpu = true
                }
            }
            if (LazadaProductStateEnum.IN_ACTIVE.code == lzdSpu.lzdStatus) {
                if (saleGoods.publishState != ProductPublishStateEnum.IN_ACTIVE.code) {
                    saleGoods.publishState = ProductPublishStateEnum.IN_ACTIVE.code
                    isUpdateSpu = true
                }
            }
            if (LazadaProductStateEnum.Deleted.code == lzdSpu.lzdStatus) {
                if (saleGoods.publishState != ProductPublishStateEnum.DELETED.code) {
                    saleGoods.publishState = ProductPublishStateEnum.DELETED.code
                    isUpdateSpu = true
                }
            }
            if (isUpdateSpu) {
                saleGoodsRepository.updateById(saleGoods)
            }
        } else {
            // 新增sale goods
            saleGoods = SaleGoods()
            saleGoods.saleGoodsId = IdHelper.getId()
            saleGoods.productId = product.productId
            if (LazadaProductStateEnum.ACTIVE.code == lzdSpu.lzdStatus) {
                saleGoods.publishState = (ProductPublishStateEnum.ACTIVE.code)
            }
            if (LazadaProductStateEnum.IN_ACTIVE.code == lzdSpu.lzdStatus) {
                saleGoods.publishState = (ProductPublishStateEnum.IN_ACTIVE.code)
            }
            if (lzdSpu.lzdStatus.equals(LazadaProductStateEnum.PendingQC.code)) {
                saleGoods.publishState = ProductPublishStateEnum.IN_ACTIVE.code
            }
            if (lzdSpu.lzdStatus.equals(LazadaProductStateEnum.Suspended.code)) {
                saleGoods.publishState = ProductPublishStateEnum.IN_ACTIVE.code
            }
            if (lzdSpu.lzdStatus.equals(LazadaProductStateEnum.Deleted.code)) {
                saleGoods.publishState = ProductPublishStateEnum.DELETED.code
            }
            val attr = lzdSpu.attributes?.parseJson<Attributes>()
            if (attr != null) {
                saleGoods.brandName = attr.brand
            }
            saleGoods.platformProductId = lzdSpu.itemId.toString()
            saleGoods.channelId = product.channelId?.toLong()
            saleGoods.platformId = product.platformId?.toLong()
            saleGoods.isHistory = Bool.NO.code
            saleGoods.productTitle = lzdSpu.title
            saleGoods.spuCode = product.spuCode
            saleGoods.country = lzdSpu.country
            saleGoods.publishTime = lzdSpu.lazadaCreatedTime
            saleGoods.latestPublishTime = lzdSpu.lazadaCreatedTime
            saleGoods.shopId = shop.shopId
            saleGoods.shopName = shop.shopName
            saleGoods.brandId = getBrandIdByShopBrandId(shop.brandId?.toLong())
            saleGoods.brandName = attr?.brand
            saleGoods.platformSyncState = PlatformSyncStateEnum.SUCCESS.code
            saleGoods.platformCategoryId = lzdSpu.primaryCategory?.toString()
            saleGoods.packageWeight = lzdSkuList.firstOrNull()?.packageWeight?.toString()
            saleGoods.packageDimensionsLength = lzdSkuList.firstOrNull()?.packageLength?.toString()
            saleGoods.packageDimensionsHeight = lzdSkuList.firstOrNull()?.packageHeight?.toString()
            saleGoods.packageDimensionsWidth = lzdSkuList.firstOrNull()?.packageWidth?.toString()
            saleGoods.createdTime = lzdSpu.lazadaCreatedTime
            saleGoods.revisedTime = lzdSpu.lazadaUpdatedTime
            val categoryTreeByCountryCode = lazadaApiService.getCategoryTreeByCountryCode(country)
            if (categoryTreeByCountryCode.isSuccess()) {
                val categoryList =
                    JSON.parseArray(JSON.toJSONString(categoryTreeByCountryCode.data), LzdCategoryVO::class.java)
                val categoryById = findCategoryById(categoryList, lzdSpu.primaryCategory)
                if (Objects.nonNull(categoryById)) {
                    saleGoods.platformCategoryName = findCategoryPathById(categoryList, categoryById!!.categoryId)
                    if (saleGoods.platformCategoryId.isNotBlank()
                        && saleGoods.platformId.isNotNull()
                        && saleGoods.channelId.isNotNull()
                        && saleGoods.country.isNotBlank()
                    ) {
                        val mappingList = publishCategoryMappingRepository.getByPlatformCategoryId(
                            saleGoods.platformCategoryId!!,
                            saleGoods.platformId!!,
                            saleGoods.channelId!!,
                            saleGoods.country!!
                        )
                        if (mappingList != null) {
                            // 获取所有品类
                            val allCategoryList = publishCategoryRepository.listWithCache()
                            // key=getPublishCategoryId
                            val allCategoryMap = allCategoryList.associateBy { it.publishCategoryId }
                            saleGoods.categoryCode = PlatformCategoryHelper.splicingCategoryCode(
                                mappingList.publishCategoryId,
                                allCategoryMap
                            )

                            // 更新product
                            product.categoryId = mappingList.publishCategoryId
                            product.categoryCode = saleGoods.categoryCode
                            product.categoryName = PlatformCategoryHelper.splicingCategoryName(
                                mappingList.publishCategoryId,
                                allCategoryMap
                            )
                            productRepository.updateById(Product().apply {
                                this.productId = product.productId
                                this.categoryId = product.categoryId
                                this.categoryCode = product.categoryCode
                                this.categoryName = product.categoryName
                            })
                        }
                    }
                }
            }
            saleGoodsRepository.save(saleGoods)

        }
        //记录商品在对应店铺的上架（在线）状态
        productPublishStoreService.saveBySaleGoodsId(saleGoods.saleGoodsId!!, PlatformEnum.LAZADA,LazadaProductStateEnum.getByCode(lzdSpu.lzdStatus)?.desc)

        // 脏数据场景: 以本地sku数据为基准, 判断如果lzd上面有没本地的seller_sku, 删除本地sku数据
        val getSaleSkuList = saleSkuRepository.getByProductIdSaleGoodsId(product.productId!!, saleGoods.saleGoodsId!!)
        if (getSaleSkuList.isNotEmpty()) {
            val lzdSellerSkuList = lzdSkuList.map { it.sellerSku }
            getSaleSkuList
                .filter { it.sellerSku.isNotBlank() }   // sellerSku不为空
                .filter { !lzdSellerSkuList.contains(it.sellerSku) }
                .forEach {
                    it.publishState = ProductPublishStateEnum.DELETED.code
                    saleSkuRepository.updateById(it)
                }
        }


        // 颜色字典
        val colorList = colorComponent.getColorMap()

        val skcList = productSkcRepository.getByProductId(product.productId!!)
        // 提取平台颜色
        val platformColorMap = skcList.associateBy { it.platformColor }
        // 颜色分组
        val lzdColorMap = lzdSkuList.groupBy { it.colorFamily }
        lzdColorMap.forEach { (platformColor, platformSkuList) ->

            var skc = platformColorMap[platformColor]

            // 字典
            var dictColor = colorList.firstOrNull { it.colorCode == platformColor }
            if (dictColor == null) {
                // 若找不到, 使用缩写匹配
                dictColor = colorList.firstOrNull { it.colorAabbr == platformColor }
            }
            // 提取图片
            var newPictures: String? = null
            val imageList = platformSkuList.first().images?.parseJsonList(String::class.java)
            if (!imageList.isNullOrEmpty()) {
                newPictures = imageList.distinct().joinToString(",")
            }

            if (skc == null) {
                // 可能是更新了颜色, 不是新增, 根据seller_sku找到skc, 如果有值则更新, 没值则新增
                val skcSkuList = saleSkuRepository.getBySellerSku(product.productId!!, platformSkuList.first().sellerSku!!)
                if (skcSkuList.isNotEmpty()) {
                    val skcId = skcSkuList.map { it.productSkcId }.distinct().first()
                    if (skcId != null) {
                        val updateSkc = productSkcRepository.getById(skcId)
                        if (updateSkc != null) {
                            if (dictColor != null) {
                                updateSkc.color = dictColor.color ?: updateSkc.color
                                updateSkc.colorCode = dictColor.colorCode ?: updateSkc.colorCode
                                updateSkc.colorAbbrCode = dictColor.colorAabbr ?: updateSkc.colorAbbrCode
                            }
                            updateSkc.platformColor = platformColor
                            productSkcRepository.updateById(updateSkc)
                            skc = updateSkc
                        }
                    }
                }
                if (skc == null) {
                    // 新增颜色
                    val newSkc = ProductSkc()
                    newSkc.productSkcId = IdHelper.getId()
                    newSkc.productId = product.productId
                    newSkc.color = dictColor?.color.orEmpty()
                    newSkc.colorCode = dictColor?.colorCode.orEmpty()
                    newSkc.colorAbbrCode = dictColor?.colorAabbr.orEmpty()
                    newSkc.platformColor = platformColor
                    newSkc.state = Bool.YES.code
                    newSkc.pictures = newPictures
                    productSkcRepository.save(newSkc)
                    skc = newSkc
                }
            }

            var saleSkc: SaleSkc?
            saleSkc = saleSkcRepository.getBySaleGoodsIdAndProductSkcId(saleGoods.saleGoodsId!!, skc.productSkcId!!)
            if (saleSkc == null && platformColor != null) {
                // 组合商品, 没有product_skc
                saleSkc = saleSkcRepository.getBySaleGoodsIdAndPlatformColor(saleGoods.saleGoodsId!!, platformColor)
            }
            if (saleSkc == null) {
                saleSkc = SaleSkc().apply {
                    this.saleSkcId = IdHelper.getId()
                    this.saleGoodsId = saleGoods.saleGoodsId
                    this.productSkcId = skc.productSkcId
                    this.skc = skc.skc
                    this.color = dictColor?.color ?: platformColor
                    this.colorCode = dictColor?.colorCode ?: platformColor
                    this.colorAbbrCode = dictColor?.colorAabbr ?: platformColor
                    this.platformColor = platformColor
                    this.pictures = newPictures
                    this.state = Bool.YES.code
                    this.combo = Bool.NO.code
                    this.cbPrice = skc.cbPrice
                    this.localPrice = skc.localPrice
                    this.purchasePrice = skc.purchasePrice
                    this.costPrice = skc.costPrice
                }
                saleSkcRepository.save(saleSkc)
            }

            // 处理SKU
            for (lazadaOriginalProductSku in platformSkuList) {
                /*
                 product id + pid + seller_sku 匹配:
                 - 存在 判断颜色skc, 是否需要修正颜色, 并更新其他数据()
                 - 不存在 新增
                 */

                // sku逻辑 (脏数据场景: 多个相同seller_sku, 绑不同的skc颜色, 需要删除不匹配的sku)
                val saleSkuList = saleSkuRepository.getByProductIdSaleGoodsIdAndSellerSku(product.productId!!, saleGoods.saleGoodsId!!, lazadaOriginalProductSku.sellerSku!!)
                val saleSku: SaleSku? = if (saleSkuList.isNotEmpty()) {
                    if (saleSkuList.size == 1) {
                        saleSkuList.first()
                    } else {
                        // 多个, 只拿匹配颜色的一个
                        val tmp: SaleSku? = saleSkuList.find { Objects.equals(saleSkc.saleSkcId, it.saleSkcId) }
                        // 删除颜色不一致的
                        saleSkuList
                            .filter { !Objects.equals(saleSkc.saleSkcId, it.saleSkcId) }
                            .forEach {
                                it.enable = Bool.NO.code
                                saleSkuRepository.updateById(it)
                            }
                        tmp
                    }
                } else {
                    null
                }

                if (saleSku == null) {
                    // 新增
                    val newSku = SaleSku()
                    newSku.country = country
                    newSku.shopSku = lazadaOriginalProductSku.shopSku
                    newSku.platformSkuId = lazadaOriginalProductSku.skuId?.toString()
                    newSku.productId = product.productId
                    newSku.productSkcId = saleSkc.productSkcId
                    newSku.saleGoodsId = saleGoods.saleGoodsId
                    newSku.saleSkcId = saleSkc.saleSkcId
                    newSku.stockQuantity = lazadaOriginalProductSku.quantity?.toLong()
                    newSku.sizeName = lazadaOriginalProductSku.size ?: ""
                    newSku.salePrice = lazadaOriginalProductSku.specialPrice
                    newSku.retailPrice = lazadaOriginalProductSku.price
                    newSku.enable = Bool.YES.code
                    newSku.sellerSku = lazadaOriginalProductSku.sellerSku
                    newSku.platformProductId = lazadaOriginalProductSku.itemId
                    newSku.platformCategoryId = saleGoods.platformProductId
                    newSku.platformCategoryName = saleGoods.platformCategoryName
                    newSku.shopName = shop.shopName
                    newSku.brandId = saleGoods.brandId
                    newSku.brandName = saleGoods.brandName
                    if (LazadaSkuStateEnum.ACTIVE.code == lazadaOriginalProductSku.lzdStatus) {
                        newSku.publishState = (ProductPublishStateEnum.ACTIVE.code)
                    }
                    if (LazadaSkuStateEnum.IN_ACTIVE.code == lazadaOriginalProductSku.lzdStatus) {
                        newSku.publishState = (ProductPublishStateEnum.IN_ACTIVE.code)
                    }
                    saleSkuRepository.save(newSku)
                } else {
                    // 比较并更新主要字段
                    var isUpdate = false
                    if (saleSku.productSkcId != saleSkc.productSkcId) {
                        saleSku.productSkcId = saleSkc.productSkcId
                        isUpdate = true
                    }
                    if (saleSku.saleSkcId != saleSkc.saleSkcId) {
                        saleSku.saleSkcId = saleSkc.saleSkcId
                        isUpdate = true
                    }
                    if (saleSku.shopSku != lazadaOriginalProductSku.shopSku) {
                        saleSku.shopSku = lazadaOriginalProductSku.shopSku
                        isUpdate = true
                    }
                    if (saleSku.retailPrice != lazadaOriginalProductSku.price) {
                        saleSku.retailPrice = lazadaOriginalProductSku.price
                        isUpdate = true
                    }
                    if (saleSku.salePrice != lazadaOriginalProductSku.specialPrice) {
                        saleSku.salePrice = lazadaOriginalProductSku.specialPrice
                        isUpdate = true
                    }
                    if (saleSku.stockQuantity != lazadaOriginalProductSku.quantity?.toLong()) {
                        saleSku.stockQuantity = lazadaOriginalProductSku.quantity?.toLong()
                        isUpdate = true
                    }
                    if (LazadaSkuStateEnum.ACTIVE.code == lazadaOriginalProductSku.lzdStatus) {
                        saleSku.publishState = (ProductPublishStateEnum.ACTIVE.code)
                        isUpdate = true
                    }
                    if (LazadaSkuStateEnum.IN_ACTIVE.code == lazadaOriginalProductSku.lzdStatus) {
                        saleSku.publishState = (ProductPublishStateEnum.IN_ACTIVE.code)
                        isUpdate = true
                    }
                    if (saleSku.enable != Bool.YES.code) {
                        saleSku.enable = Bool.YES.code
                        isUpdate = true
                    }
                    if (!Objects.equals(saleSku.platformSkuId, lazadaOriginalProductSku.skuId.toString())) {
                        saleSku.platformSkuId = lazadaOriginalProductSku.skuId?.toString()
                        isUpdate = true
                    }
                    if (isUpdate) {
                        saleSkuRepository.updateById(saleSku)
                    }
                }

            }
        }
    }

    /**
     * 更新product站点标题
     *
     * @param product
     * @param country
     * @param newTitle
     */
    fun setProductTitle(product: Product, country: String, newTitle: String) {
        var isUpdate = false
        val allCountryTitle = product.allCountryTitle
        if (!allCountryTitle.isNullOrBlank()) {
            val productTitleResp: List<ProductTitleResp> = allCountryTitle.parseJsonList(ProductTitleResp::class.java)
            if (productTitleResp.isNotEmpty()) {
                // 国家分组map
                val countryTitleMap = productTitleResp.associateBy { it.country }.toMutableMap()
                if (countryTitleMap.containsKey(country)) {
                    // 存在, 更新
                    val dto = countryTitleMap[country]
                    if (!dto?.title.equals(newTitle)) {
                        dto?.title = newTitle
                        isUpdate = true
                    }
                } else {
                    // 不存在, 新增
                    countryTitleMap[country] = ProductTitleResp().apply {
                        this.country = country
                        this.title = newTitle
                        this.countryName = CountryEnum.getCountryCurrency(country)?.name
                    }
                    isUpdate = true
                }
            }
            if (isUpdate) {
                product.allCountryTitle = productTitleResp.toJson()
                productRepository.updateById(product)
            }
        }
    }

    /**
     * 递归方法：根据 categoryId 查找 LzdCategoryVO
     *
     * @param categories
     * @param categoryId
     * @return
     */
    private fun findCategoryById(categories: List<LzdCategoryVO>, categoryId: Long?): LzdCategoryVO? {
        for (category in categories) {
            if (Objects.equals(category.categoryId, categoryId)) {
                return category  // 找到匹配的 categoryId
            }
            // 如果有子节点，递归查找子节点
            if (category.children != null && category.children!!.isNotEmpty()) {
                val result = findCategoryById(category.children!!, categoryId)
                if (result != null) {
                    return result  // 在子节点中找到了匹配的 categoryId
                }
            }
        }
        return null  // 没有找到
    }

    /**
     * 店铺表中的品牌id获取Lazada的品牌id
     *
     * @param shopBrandId
     * @return
     */
    private fun getBrandIdByShopBrandId(shopBrandId: Long?): Int? {
        if (shopBrandId == null) {
            return null
        }
        return lazadaBrandRepository.getById(shopBrandId)?.brandId
    }


    // Method to find the category path by categoryId
    private fun findCategoryPathById(categories: List<LzdCategoryVO>, categoryId: Long): String? {
        for (category in categories) {
            // Check if the current category is the one we're looking for
            if (category.categoryId == categoryId) {
                return category.name // Base case: the category itself
            }

            // Recursively search in the children
            val categoryChildren = category.children
            if (!categoryChildren.isNullOrEmpty()) {
                val path = findCategoryPathById(categoryChildren, categoryId)
                if (path != null) {
                    return category.name + ">" + path // Append the current category name to the path
                }
            }
        }
        return null // Return null if the categoryId is not found
    }

    /**
     * 匹配barcode 更新skc表skc,sku表barcode,barcode表sellerSku
     * @param product
     */
    private fun updateSkcAndBarcode(product: Product) {

        val saleGoodsList = saleGoodsRepository.getByProduct(product.productId!!)
        if (saleGoodsList.isEmpty()) {
            return
        }

        // 找到所有skc
        val saleSkcList = saleSkcRepository.findAllBySaleGoodsIds(saleGoodsList.mapNotNull { it.saleGoodsId })
        if (saleSkcList.isEmpty()) {
            return
        }
        // 找到所有sku
        val skcIds = saleSkcList.mapNotNull { it.saleSkcId }
        val saleSkuList = saleSkuRepository.getByEnableSaleSkcId(skcIds.toSet())
        if (saleSkuList.isEmpty()) {
            return
        }
        // map key=skcId
        val skcMap = saleSkcList.associateBy { it.saleSkcId }
        saleSkuList.forEach { sku ->

            val skc = skcMap[sku.saleSkcId] ?: return@forEach

            // 拿到barcode/更新skc
            if (!product.spuCode.isNullOrBlank()
                && !sku.sizeName.isNullOrBlank()
                && !skc.color.isNullOrBlank()
            ) {
                // 常规格式匹配
                var barcode = productBarCodeRepository.getBySpuCodeAndSizeAndColor(product.spuCode!!, sku.sizeName!!, skc.color!!)
                if (barcode == null && skc.skc != null) {
                    // 找不到, 使用特殊格式: skc-尺码
                    barcode = productBarCodeRepository.getBySkcAndSize(skc.skc!!, sku.sizeName!!)
                }
                if (barcode != null) {
                    // 更新sku的barcode
                    if (sku.barcode.isNullOrBlank()) {
                        sku.barcode = barcode.barcode
                        saleSkuRepository.updateById(sku)
                    }
                    // 更新skc的skc
                    if (skc.skc.isNullOrBlank()) {
                        skc.skc = barcode.skc
                        saleSkcRepository.updateById(skc)
                    }
                    // 更新barcode的sellerSku
                    if (sku.sellerSku.isNotBlank()) {
                        val currentSellerSkus = barcode.sellerSku?.split(",")?.toMutableList() ?: mutableListOf()
                        if (!currentSellerSkus.contains(sku.sellerSku)) {
                            currentSellerSkus.add(sku.sellerSku!!)
                            barcode.sellerSku = currentSellerSkus.joinToString(",")
                            productBarCodeRepository.updateById(barcode)
                        }
                    }
                }
            }
            if (!skc.skc.isNullOrBlank() && skc.productSkcId != null) {
                val pc = productSkcRepository.getById(skc.productSkcId!!)
                if (pc != null && pc.skc.isNullOrBlank()) {
                    pc.skc = skc.skc
                    productSkcRepository.updateById(pc)
                }
            }
        }
    }

    /**
     * skc下没有sku, 则标记skc状态为取消
     * @param product
     */
    private fun checkSkuStats(product: Product) {

        val saleGoodsList = saleGoodsRepository.getByProduct(product.productId!!)
        if (saleGoodsList.isNotEmpty()) {
            return
        }

        // 找到所有skc
        val saleSkcList = saleSkcRepository.findAllBySaleGoodsIds(saleGoodsList.mapNotNull { it.saleGoodsId })
        if (saleSkcList.isEmpty()) {
            return
        }

        // 找到所有sku
        val skcIds = saleSkcList.mapNotNull { it.saleSkcId }
        val saleSkuList = saleSkuRepository.getByEnableSaleSkcId(skcIds.toSet())
        if (saleSkuList.isEmpty()) {
            return
        }
        // group map key=skcId
        val groupSkcMap = saleSkuList.groupBy { it.saleSkcId }
        saleSkcList.forEach { skc ->
            // 判断状态是否可用
            val skuList = groupSkcMap[skc.saleSkcId]
            if (skuList.isNullOrEmpty()) {
                // 没有sku, 取消状态 (只处理取消状态, 因为skc可能会系统更新成取消, 只时候拉下来是否有数据都不能激活, 维持原样)
                val newState = Bool.NO.code

                // 判断状态是否需要更新
                if (skc.state != newState) {
                    skc.state = newState
                    saleSkcRepository.updateById(skc)
                }
            }

        }
    }
}
