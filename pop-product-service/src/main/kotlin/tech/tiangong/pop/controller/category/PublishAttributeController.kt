package tech.tiangong.pop.controller.category

import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile
import team.aikero.blade.core.constant.UrlVersionConstant.WEB
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.PageVo
import team.aikero.blade.core.protocol.ok
import tech.tiangong.pop.req.category.*
import tech.tiangong.pop.resp.category.GroupAttributeResp
import tech.tiangong.pop.resp.category.PublishAttributeGroupVo
import tech.tiangong.pop.resp.category.PublishAttributeVo
import tech.tiangong.pop.resp.category.PublishGroupWithAttributeVo
import tech.tiangong.pop.service.category.PublishGroupAttrService

/**
 * 属性管理
 */
@RestController
@RequestMapping("$WEB/v1/publish-attribute")
class PublishAttributeController(
    private val publishGroupAttrService: PublishGroupAttrService
) {

    /**
     * 分组列表
     *
     * @param req 分页对象
     * @return 属性分组列表
     */
    @PostMapping("/list-group")
    fun listGroup(@RequestBody req: PublishAttributeGroupQueryReq): DataResponse<List<PublishAttributeGroupVo>> {
        return ok(publishGroupAttrService.listGroup(req))
    }

    /**
     * 保存分组
     *
     * @param req 分组保存请求
     * @return 操作结果
     */
    @PostMapping("/save-group")
    fun saveGroup(@RequestBody req: SavePublishAttributeGroupReq): DataResponse<Unit> {
        publishGroupAttrService.saveGroup(req)
        return ok()
    }

    /**
     * 删除分组
     *
     * @param groupId 分组ID
     * @return 操作结果
     */
    @PostMapping("/delete-group")
    fun deleteGroup(@RequestParam("groupId") groupId: Long): DataResponse<Unit> {
        publishGroupAttrService.deleteGroup(listOf(groupId))
        return ok()
    }

    /**
     * 分页查询分组属性列表
     *
     * @param req 分页查询请求
     * @return 分页结果
     */
    @PostMapping("/page-attribute")
    fun pageAttribute(@RequestBody req: PublishAttributePageQueryReq): DataResponse<PageVo<PublishAttributeVo>> {
        return ok(publishGroupAttrService.pageAttribute(req))
    }

    /**
     * 分组属性列表
     *
     * @param req 查询请求
     * @return 分组及其属性列表
     */
    @PostMapping("/list-group-with-attribute")
    fun listGroupWithAttribute(@RequestBody req: PublishAttributeQueryReq): DataResponse<List<PublishGroupWithAttributeVo>> {
        return ok(publishGroupAttrService.listGroupWithAttribute(req))
    }

    /**
     * 保存分组属性
     *
     * @param req 属性保存请求
     * @return 操作结果
     */
    @PostMapping("/save-attribute")
    fun saveAttribute(@RequestBody req: SavePublishAttributeReq): DataResponse<Unit> {
        publishGroupAttrService.saveAttribute(req)
        return ok()
    }

    /**
     * 删除分组属性
     *
     * @param attributeId 属性ID
     * @return 操作结果
     */
    @PostMapping("/remove-attribute")
    fun removeAttribute(@RequestParam("attributeId") attributeId: Long): DataResponse<Unit> {
        publishGroupAttrService.removeAttribute(listOf(attributeId))
        return ok()
    }

    /**
     * 导入
     *
     * @param file 导入文件
     * @return 导入结果信息
     */
    @PostMapping("/import")
    fun importAttribute(@RequestParam("file") file: MultipartFile): DataResponse<List<String>> {
        publishGroupAttrService.importAttribute(file)
        return ok()
    }

    /**
     * 修改属性分组状态
     *
     * @param req
     * @return 操作结果
     */
    @PostMapping("/modify-attribute-group-state")
    fun modifyAttributeGroupState(@RequestBody req: ModifyAttributeGroupStateReq): DataResponse<Unit> {
        publishGroupAttrService.modifyAttributeGroupState(req)
        return ok()
    }
    /**
     * 修改属性状态
     *
     * @param req
     * @return 操作结果
     */
    @PostMapping("/modify-attribute-state")
    fun modifyAttributeState(@RequestBody req: ModifyAttributeStateReq): DataResponse<Unit> {
        publishGroupAttrService.modifyAttributeState(req)
        return ok()
    }


    /**
     * 根据分组名称查询属性及值选项
     *
     * @param groupName 分组名称
     * @param listEnable 是否只返回状态正常的属性1是0否，默认1
     * @return 操作结果
     */
    @PostMapping("/list-attributes-by-group-name")
    fun listAttributesByGroupName(@RequestParam("groupName") groupName:String,
                                  @RequestParam("listEnable", required = false) listEnable:Int?): DataResponse<List<GroupAttributeResp>>{
        return ok(publishGroupAttrService.listAttributesByGroupName(groupName,listEnable))
    }
}