package tech.tiangong.pop.controller.category

import jakarta.servlet.http.HttpServletResponse
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile
import team.aikero.blade.core.constant.UrlVersionConstant.WEB
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.ok
import tech.tiangong.pop.req.category.*
import tech.tiangong.pop.resp.category.CategoryAttributeCopyByIdResp
import tech.tiangong.pop.resp.category.CategoryAttributeCopyResp
import tech.tiangong.pop.resp.category.CategoryAttributeRefResp
import tech.tiangong.pop.resp.category.PlatformCategoryAttributeMappingDetailResp
import tech.tiangong.pop.service.category.PublishCategoryAttributeService

/**
 * 品类属性关联控制层
 */
@RestController
@RequestMapping("$WEB/v1/category-attr")
class PublishCategoryAttributeController(
    private val publishCategoryAttributeService: PublishCategoryAttributeService
) {
    /**
     * 查看关联属性
     *
     * @param req CategoryAttrListReq
     * @return CategoryAttributeRefResp
     */
    @Deprecated(message = "用/get-mapping-values-by-category-mapping-id代替")
    @PostMapping("/detail")
    fun getAttributesByCategory(
        @Validated @RequestBody req: CategoryAttrListReq
    ): DataResponse<CategoryAttributeRefResp> {
        return ok(publishCategoryAttributeService.findByCategoryAttrIds(req))
    }

    /**
     * 移除属性关联
     *
     * @param categoryMappingId 平台品类映射ID
     * @return Void
     */
    @PostMapping("/remove/{categoryMappingId}")
    fun remove(@PathVariable("categoryMappingId") categoryMappingId: Long): DataResponse<Void> {
        publishCategoryAttributeService.removePlatformAttr(categoryMappingId)
        return ok()
    }

    /**
     * 关联属性
     *
     * @param req 平台品类映射ID
     * @return Void
     */
    @Deprecated(message = "用/save-category-attribute-value-mapping代替")
    @PostMapping("/edit")
    fun edit(@Validated @RequestBody req: CategoryAttributeEditReq): DataResponse<Void> {
        publishCategoryAttributeService.refCategoryAttribute(req)
        return ok()
    }

    /**
     * 失效所有的AE类目属性缓存
     */
    @PostMapping("/invalidate-all-aliexpress-category-attributes")
    fun invalidateAllAliexpressCategoryAttributes() {
        publishCategoryAttributeService.invalidateAllAliexpressCategoryAttributes()
    }

    /**
     * 复制品类属性
     * 将已配置完成品类的属性复制到其他品类
     *
     * @param req 品类属性复制请求
     * @return 品类属性复制响应
     */
    @PostMapping("/copy")
    fun copyAttributes(@Validated @RequestBody req: CategoryAttributeCopyReq): DataResponse<CategoryAttributeCopyResp> {
        val result = publishCategoryAttributeService.copyAttributes(req)
        return ok(result)
    }

    /**
     * 复制品类属性（基于ID）
     */
    @PostMapping("/copy-by-id")
    fun copyAttributesById(@Validated @RequestBody req: CategoryAttributeCopyByIdReq): DataResponse<CategoryAttributeCopyByIdResp> {
        val result = publishCategoryAttributeService.copyAttributesById(req)
        return ok(result)
    }

    /**
     * 根据已关联品类ID查询第三方平台的品类属性及已关联本地属性值
     *
     */
    @GetMapping("/get-mapping-values-by-category-mapping-id")
    fun getPlatformAttributeMappingDetailByCategoryMappingId(@RequestParam("categoryMappingId") categoryMappingId: Long): DataResponse<PlatformCategoryAttributeMappingDetailResp> {
        return ok(publishCategoryAttributeService.getPlatformAttributeMappingDetailByCategoryMappingId(categoryMappingId))
    }

    /**
     * 保存本地属性值与平台属性值的映射关系
     *
     * @param req
     * @return Void
     */
    @PostMapping("/save-category-attribute-value-mapping")
    fun saveCategoryAttributeValueMapping(@Validated @RequestBody req: SaveCategoryAttributeValueMappingReq): DataResponse<Void> {
        publishCategoryAttributeService.saveCategoryAttributeValueMapping(req)
        return ok()
    }

    /**
     * 复制本地属性值与平台属性值的映射关系
     *
     * @param req
     * @return Void
     */
    @PostMapping("/copy-category-attribute-value-mapping")
    fun copyCategoryAttributeValueMapping(@Validated @RequestBody req: CopyCategoryAttributeValueMappingReq): DataResponse<Void> {
        publishCategoryAttributeService.copyCategoryAttributeValueMapping(req)
        return ok()
    }

    /**
     * 导入本地品类属性对应平台品类属性值的映射
     *
     * @param file       excel
     * @param categoryMappingId 品类映射ID
     */
    @PostMapping("/import/category-attribute-mapping")
    fun importCategoryAttributeMapping(
        @RequestParam("file") file: MultipartFile
    ): DataResponse<Unit> {
        publishCategoryAttributeService.importCategoryAttributeMapping(file)
        return ok()
    }

    /**
     * 导出本地品类属性对应平台品类属性值的映射
     *
     * @param file       excel
     * @param platformId 平台ID
     * @param categoryId 品类ID 不传则导出所有叶子节点的品类属性映射
     */
    @PostMapping("/export/category-attribute-mapping")
    fun exportCategoryAttributeMapping(
                    @RequestBody req:ExportCategoryAttributeMappingReq,
                    response: HttpServletResponse){
        publishCategoryAttributeService.exportCategoryAttributeMapping(req,response)
    }
}