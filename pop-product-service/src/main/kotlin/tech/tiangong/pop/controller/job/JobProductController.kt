package tech.tiangong.pop.controller.job

import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import team.aikero.blade.auth.annotation.PreCheckIgnore
import team.aikero.blade.auth.withSystemUser
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.ok
import team.aikero.blade.util.async.runAsync
import tech.tiangong.pop.component.ae.AeProductScheduledSyncComponent
import tech.tiangong.pop.component.lazada.LazadaProductScheduledSyncComponent
import tech.tiangong.pop.req.product.ProductPartSyncReq
import tech.tiangong.pop.service.product.ProductAeService
import tech.tiangong.pop.service.product.ProductCenterService
import tech.tiangong.pop.service.product.ProductImportService
import tech.tiangong.pop.service.product.SaleProductPushImageTaskService
import tech.tiangong.pop.service.product.ProductTemuService
import java.util.concurrent.ExecutorService

/**
 * 操作数据的任务
 */
@PreCheckIgnore
@RestController
@RequestMapping("/job/v1/product")
class JobProductController(
    private val productImportService: ProductImportService,
    private val productAeService: ProductAeService,
    private val productTemuService: ProductTemuService,
    private val aeProductScheduledSyncComponent: AeProductScheduledSyncComponent,
    private val lazadaProductScheduledSyncComponent: LazadaProductScheduledSyncComponent,
    private val productService: ProductCenterService,
    @Qualifier("xxlJobExecutor")
    private val xxlJobExecutor: ExecutorService,
    private val saleProductPushImageTaskService: SaleProductPushImageTaskService,
) {

    /**
     * 扫描待审核中的商品信息 查询审核结果
     */
    @PostMapping("/update-video-status")
    fun videoUpdateTask() {
        withSystemUser{

            runAsync{
                // 更新ae
                productAeService.updateVideoStatusAndPushToAe()
            }
            runAsync {
                // 更新temu
                productTemuService.updateVideoInfo()
            }

        }
    }


    /**
     * 定时扫描铺货规则，上架商品
     */
    @PostMapping("/product-to-on-shelf")
    fun productToOnShelf(): DataResponse<Unit> {
        withSystemUser {
            productService.productToOnShelfTask()
        }
        return ok()
    }

    /**
     * 根据铺货规则扫描，将货通商品设置为待上架
     */
    @PostMapping("/product-to-pending")
    fun productToWaitOnShelf(): DataResponse<Unit> {
        withSystemUser {
            runAsync {
                productService.createPendingTaskFromProductPlacementRule()
            }
        }
        return ok()
    }

    /**
     * 定期清理同步任务数据
     * {"method":"POST","url":"/pop-product-service/job/v1/product/sync-await-product","system":"ola", "env":"dev"}
     */
    @PostMapping("/sync-await-product")
    fun cleanSyncTask(
        @RequestBody(required = false) taskIds: List<Long>?,
    ): DataResponse<Unit> {
        productImportService.processQueuingData(taskIds ?: emptyList())
        return ok()
    }

    /**
     * 定时刷新AE商品状态(审核中)
     * {"method":"POST","url":"/pop-product-service/job/v1/product/ae/refresh/status","system":"ola", "env":"dev"}
     */
    @PostMapping("/ae/refresh/status")
    fun refreshAeProductStatus(): DataResponse<Unit> {
        productAeService.scheduleRefreshProductStatus()
        return ok()
    }

    /**
     * 同步所有AE商品 部分信息
     * 给xxl-job调用的接口
     */
    @PostMapping("/ae/part/sync/all")
    fun syncAllAeProductPart(
        @RequestBody(required = false) req: ProductPartSyncReq?,
    ): DataResponse<Unit> {
        withSystemUser {
            runAsync(xxlJobExecutor) {
                aeProductScheduledSyncComponent.syncAllProductPart(req ?: ProductPartSyncReq())
            }
        }
        return ok()
    }

    /**
     * 同步所有lazada商品 部分信息
     * 给xxl-job调用的接口
     */
    @PostMapping("/lazada/part/sync/all")
    fun syncAllLazadaProductPart(
        @RequestBody(required = false) req: ProductPartSyncReq?,
    ): DataResponse<Unit> {
        withSystemUser {
            runAsync(xxlJobExecutor) {
                lazadaProductScheduledSyncComponent.syncAllProductPart(req ?: ProductPartSyncReq())
            }
        }
        return ok()
    }

    /**
     * 定时执行已上架数据到款式图片服务
     * {"method":"POST","url":"/pop-product-service/job/v1/product/execute/image/task","system":"ola", "env":"dev"}
     */
    @PostMapping("/execute/image/task")
    fun executeImageTask(@RequestBody spuCodes: List<String>?): DataResponse<Unit> {
        saleProductPushImageTaskService.executeImageTask(spuCodes ?: emptyList())
        return ok()
    }

}