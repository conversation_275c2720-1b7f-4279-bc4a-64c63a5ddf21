package tech.tiangong.pop.controller.product

import jakarta.validation.Valid
import jakarta.validation.constraints.NotNull
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.PageVo
import team.aikero.blade.core.protocol.ok
import tech.tiangong.pop.component.ae.CallAeComponent
import tech.tiangong.pop.component.export.AeQueryDataExportComponent
import tech.tiangong.pop.enums.ProductAeAttributeTypeEnum
import tech.tiangong.pop.req.product.ae.*
import tech.tiangong.pop.req.product.lazada.UpdateSkuByUpdateStockPriceReq
import tech.tiangong.pop.resp.product.ae.*
import tech.tiangong.pop.resp.sdk.aliexpress.AliexpressCategoryAttributeResponse
import tech.tiangong.pop.service.product.AePullSyncProductService
import tech.tiangong.pop.service.product.ProductAeConfirmUpdateService
import tech.tiangong.pop.service.product.ProductAeRegulatoryService
import tech.tiangong.pop.service.product.ProductAeService

/**
 * 商品管理-AE
 * <AUTHOR>
 * @date 2025-2-12 10:31:11
 */
@Validated
@RestController
@RequestMapping("/web/v1/product/ae")
class ProductAeController(
    private val productAeService: ProductAeService,
    private val callAeComponent: CallAeComponent,
    private val aePullSyncProductService: AePullSyncProductService,
    private val productAeRegulatoryService: ProductAeRegulatoryService,
    private val productAeConfirmUpdateService: ProductAeConfirmUpdateService,
    private val aeQueryDataExportComponent: AeQueryDataExportComponent,
) {

    /**
     * AE-商品详情
     * @tags v0904
     * @param req
     * @return ProductDetailResp
     */
    @PostMapping("/detail")
    fun detail(@Validated @RequestBody req: ProductAeDetailReq): DataResponse<ProductAeDetailResp> {
        return ok(productAeService.detail(req))
    }

    /**
     * AE-商品分页
     *
     * @param req
     * @return ProductDetailResp
     */
    @PostMapping("/page")
    fun page(@Validated @RequestBody req: ProductAePageQueryReq): DataResponse<PageVo<ProductAePageResp>> {
        return ok(productAeService.page(req))
    }

    /**
     * AE-统计数量
     */
    @PostMapping("/count")
    fun getProductCounts(@Validated @RequestBody req: ProductAePageQueryReq): DataResponse<ProductAeCountResp> {
        return ok(productAeService.getProductCounts(req))
    }

    /**
     * AE-商品更新
     * @tags v0904
     *
     * @param req
     * @return ProductDetailResp
     */
    @PostMapping("/update")
    fun update(@Validated @RequestBody req: ProductAePublishedUpdateReq): DataResponse<Unit> {
        productAeService.update(req)
        return ok()
    }

    /**
     * AE-单个/批量上架
     *
     * @param req
     * @return ProductDetailResp
     */
    @PostMapping("/publish")
    fun updateExistingPublish(@Validated @RequestBody req: ProductAeExistingPublishReq): DataResponse<Unit> {
        callAeComponent.publishExistingOrAddCountry(req)
        return ok()
    }

    /**
     * 应用更新
     *
     * @param req
     */
    @PostMapping("/apply-product")
    fun applyUpdateProduct(@Validated @RequestBody req: ProductAeApplyUpdateReq): DataResponse<Unit> {
        productAeConfirmUpdateService.applyUpdateProduct(req)
        return ok()
    }

    /**
     * 批量下架-弹窗-商品SKU列表查询
     */
    @PostMapping("/sku/online/list")
    fun pageOnlineSkuList(@Validated @RequestBody req: ProductAeOnlineSkuListReq): DataResponse<PageVo<ProductAeOnlineSkuListResp>> {
        return ok(productAeService.pageOnlineSkuList(req))
    }

    /**
     * 批量下架-弹窗-商品SKU列表查询-统计数量
     *
     * @param req 商品SKU查询请求
     */
    @PostMapping("/sku/online/count")
    fun countOnlineSku(@Validated @RequestBody req: ProductAeOfflineSubmitReq): DataResponse<ProductAeOnlineSkuCountResp> {
        return ok(productAeService.countOnlineSku(req))
    }

    /**
     * 批量下架
     *
     * @param req 批量下架请求参数
     */
    @PostMapping("/batch/offline")
    fun batchSendOfflineMessage(@Validated @RequestBody req: ProductAeOfflineSubmitReq): DataResponse<Unit> {
        productAeService.batchOffline(req)
        return ok()
    }

    /**
     * 批量同步其他店铺
     *
     * @param req 入参
     */
    @PostMapping("/batch/sync-shop")
    fun syncOtherShop(@Validated @RequestBody req: AeCloneProductToOtherShopReq): DataResponse<Unit> {
        productAeService.syncOtherShop(req)
        return ok()
    }

    /**
     * 批量修改库存
     *
     * @param req 入参
     */
    @PostMapping("/batch/update-quantity")
    fun batchStockQuantity(@Validated @RequestBody req: AeBatchUpdateQuantityReq): DataResponse<Unit> {
        productAeService.batchStockQuantity(req)
        return ok()
    }

    /**
     * 批量修改分组/模板等
     *
     * @param req 入参
     */
    @PostMapping("/batch/update-ae-info")
    fun batchUpdateAeInfo(@Validated @RequestBody req: AeBatchUpdateAeInfoReq): DataResponse<Unit> {
        productAeService.batchUpdateAeInfo(req)
        return ok()
    }

    /**
     * 查询速卖通用户运费模板列表
     *
     * @param req 查询请求
     * @return 运费模板列表
     */
    @PostMapping("/freight-templates")
    fun queryFreightTemplateList(
        @Valid @RequestBody req: ProductAeFreightTemplateQueryReq,
    ): DataResponse<List<ProductAeFreightTemplateResp>> {
        val result = productAeService.queryFreightTemplateList(
            shopId = req.shopId!!,
            channelSellerId = req.channelSellerId
        )
        return ok(result)
    }

    /**
     * 查询速卖通服务模板
     *
     * @param req 查询请求
     * @return 服务模板列表
     */
    @PostMapping("/promise-templates")
    fun queryPromiseTemplates(@Valid @RequestBody req: ProductAePromiseTemplateQueryReq): DataResponse<List<ProductAePromiseTemplateResp>> {
        val result = productAeService.queryPromiseTemplates(
            shopId = req.shopId!!,
            templateId = req.templateId
        )
        return ok(result)
    }

    /**
     * 查询欧盟责任人列表
     *
     * @param req 查询请求
     * @return 欧盟责任人列表
     */
    @PostMapping("/msr-list")
    fun queryMsrList(@Valid @RequestBody req: ProductAeMsrListQueryReq): DataResponse<List<ProductAeMsrItemResp>> {
        val result = productAeService.queryMsrList(
            shopId = req.shopId!!,
            channelSellerId = req.channelSellerId,
            channel = req.channel
        )
        return ok(result)
    }

    /**
     * 获取制造商信息列表
     *
     * @param req 查询请求
     * @return 制造商信息列表
     */
    @PostMapping("/manufacture-list")
    fun queryManufactureList(@Valid @RequestBody req: ProductAeManufactureListQueryReq): DataResponse<List<ProductAeManufactureItemResp>> {
        val result = productAeService.queryManufactureList(
            shopId = req.shopId!!,
            channelSellerId = req.channelSellerId,
            channel = req.channel
        )
        return ok(result)
    }

    /**
     * 获取当前会员的产品分组
     *
     * @param req 查询请求
     * @return 产品分组列表
     */
    @PostMapping("/product-groups")
    fun queryProductGroups(@Valid @RequestBody req: ProductAeProductGroupsQueryReq): DataResponse<List<ProductAeProductGroupResp>> {
        val result = productAeService.queryProductGroups(
            shopId = req.shopId!!
        )
        return ok(result)
    }

    /**
     * 弹窗查询-编辑库存/价格
     * @tags v0904
     *
     * @param req
     */
    @PostMapping("/update/sku/stock/price/query")
    fun querySkuByUpdateStockPrice(@Validated @RequestBody req: AeQuerySkuByUpdateStockPriceReq): DataResponse<List<AeQuerySkuByUpdateStockPriceResp>> {
        return ok(productAeService.querySkuByUpdateStockPrice(req))
    }

    /**
     * 编辑库存/价格
     * @tags v0904
     *
     * @param req
     */
    @PostMapping("/update/sku/stock/price")
    fun updateSkuByUpdateStockPrice(@Validated @RequestBody req: List<UpdateSkuByUpdateStockPriceReq>): DataResponse<Unit> {
        productAeService.updateSkuByUpdateStockPrice(req)
        return ok()
    }

    /**
     * 清除指定店铺的所有缓存
     * @param shopId 店铺ID
     */
    @PostMapping("/invalidate/all/shop/cache")
    fun invalidateAllShopCache(@RequestParam("shopId") shopId: Long): DataResponse<Unit> {
        productAeService.invalidateAllShopCache(shopId)
        return ok()
    }

    /**
     * 清除所有AliExpress相关缓存
     */
    @PostMapping("/invalidate/all/cache")
    fun productAeService(): DataResponse<Unit> {
        productAeService.invalidateAllCache()
        return ok()
    }

    /**
     * 提交拉取平台商品任务
     * @param req
     * @return
     */
    @PostMapping("/task/pull/submit")
    fun submitPullTask(@Valid @RequestBody req: ProductAePullReq): DataResponse<Unit> {
        aePullSyncProductService.submitPullTask(req)
        return ok()
    }

    /**
     * 提交同步平台商品任务
     * @param req
     * @return
     */
    @PostMapping("/task/sync/submit")
    fun submitSyncTask(@Valid @RequestBody req: ProductAeSyncReq): DataResponse<Unit> {
        aePullSyncProductService.submitSyncTask(req)
        return ok()
    }

    /**
     * 查询海关监管属性
     * 在编辑了四要素之后, 可以重新打开组件，渲染组件页面内容(用于推荐品名)
     */
    @PostMapping("/regulatory-attributes")
    fun queryRegulatoryAttributesOptions(
        @Valid @RequestBody req: AeQueryRegulatoryAttributesReq,
    ): DataResponse<AeRegulatoryAttributesDataResp> {
        val result = productAeRegulatoryService.queryRegulatoryAttributes(req)
        return ok(result)
    }

    /**
     * 批量查询海关监管属性
     * 在编辑了四要素之后, 可以重新打开组件，渲染组件页面内容(用于推荐品名)
     */
    @PostMapping("/batch-query-regulatory-attributes")
    fun batchQueryRegulatoryAttributesOptions(
        @Valid @RequestBody req: List<AeQueryRegulatoryAttributesReq>,
    ): DataResponse<BatchAeRegulatoryAttributesDataResp> {
        val result = productAeRegulatoryService.batchQueryRegulatoryAttributes(req)
        return ok(result)
    }

    /**
     * 商家编辑每个层级的海关监管属性
     * (逐级下拉)
     */
    @PostMapping("/regulatory-attributes/options")
    fun selectRegulatoryAttributesOptions(
        @Valid @RequestBody req: AeSelectRegulatoryAttributesOptionsReq,
    ): DataResponse<AeRegulatoryAttributesOptionsDataResp> {
        val result = productAeRegulatoryService.selectRegulatoryAttributesOptions(req)
        return ok(result)
    }

    /**
     * 单个更新海关监管属性
     */
    @PostMapping("/update/hs-code")
    fun updateHsCode(@Valid @RequestBody req: AeProductHsCodeReq): DataResponse<Unit> {
        productAeRegulatoryService.updateHsCode(req)
        return ok()
    }

    /**
     * 批量更新海关监管属性
     */
    @PostMapping("/update/hs-code/batch")
    fun batchUpdateHsCode(@Valid @RequestBody req: AeBatchProductHsCodeReq): DataResponse<Unit> {
        productAeRegulatoryService.batchUpdateHsCode(req)
        return ok()
    }

    /**
     * 查询商品产地属性
     */
    @PostMapping("/attribute/origin")
    fun queryOriginAttribute(@Valid @RequestBody req: ProductAeAttributeQueryReq): DataResponse<ProductAeAttributeResp> {
        val results = productAeService.getOriginAttribute(req)
        return ok(results)
    }

    /**
     * 查询发货地属性
     */
    @PostMapping("/attribute/ships-from")
    fun getShipsFromAttribute(@Valid @RequestBody req: ProductAeAttributeQueryReq): DataResponse<ProductAeAttributeResp> {
        val validatedReq = req.copy(attributeType = ProductAeAttributeTypeEnum.SHIPS_FROM)
        val result = productAeService.getAttributes(validatedReq)
        return ok(result)
    }

    /**
     * 查询类目下的所有属性
     */
    @PostMapping("/attribute/all")
    fun getAllCategoryAttributes(
        @Validated @RequestBody req: ProductAeCategoryAttributesQueryReq
    ): DataResponse<AliexpressCategoryAttributeResponse> {
        val result = productAeService.getAllAttributes(req)
        return ok(result)
    }

    /**
     * 提交导出查询数据任务
     *
     * 说明：
     * 1. 点击后，执行任务将列表数据导出；文本提示：导出任务已生成，请至下载管理内下载文件
     * 2. 导出文件名称（下载任务名称）：导出AE商品查询数据_年年年年月月日日时时分分秒秒
     * 3. 导出的字段跟导出AE通用模板Excel的字段是一样的，只是查询条件是AE已上架分页查询的查询条件
     * 4. 分页查出来的数据行数（aeSaleGoods）全部数据，不带分页数量限制
     */
    @PostMapping("/submit-query-data-task")
    fun submitQueryDataTask(@Validated @RequestBody req: ProductAePageQueryReq): DataResponse<String> {
        val message = aeQueryDataExportComponent.createExportTask(req)
        return ok(message)
    }

    /**
     * 修改指定AE商品的毛利率配置
     */
    @PostMapping("/update-gross-margin")
    fun updateProductGrossMargin(@Validated @RequestBody req: UpdateAEProductGrossMarginReq): DataResponse<Unit> {
        productAeService.updateProductGrossMargin(req)
        return ok()
    }

    /**
     * 中止指定AE商品的同步
     *
     * @param aeSaleGoodsIds 商品ID列表
     * @return
     */
    @PostMapping("/cancel-sync")
    fun cancelSync(@RequestBody @Validated @NotNull(message = "请选择商品") aeSaleGoodsIds: List<Long>): DataResponse<Unit> {
        productAeService.cancelSync(aeSaleGoodsIds)
        return ok()
    }
}
