package tech.tiangong.pop.controller.product

import co.elastic.clients.elasticsearch._types.Refresh
import jakarta.validation.Valid
import org.jetbrains.annotations.NotNull
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.PageVo
import team.aikero.blade.core.protocol.ok
import team.aikero.blade.data.mybatis.toolkit.toPageVo
import tech.tiangong.pop.component.lazada.CallLazadaComponent
import tech.tiangong.pop.es.EsSynchronizer
import tech.tiangong.pop.req.product.lazada.*
import tech.tiangong.pop.resp.product.lazada.PlatformProductPullTaskPageVo
import tech.tiangong.pop.resp.product.lazada.ProductLazadaDetailResp
import tech.tiangong.pop.resp.product.lazada.ProductLazadaPageResp
import tech.tiangong.pop.resp.product.lazada.QuerySkuByUpdateStockPriceResp
import tech.tiangong.pop.service.PlatformProductPullTaskService
import tech.tiangong.pop.service.product.ProductLazadaService

/**
 * 商品管理-Lazada
 * <AUTHOR>
 * @date 2025-2-12 10:31:11
 */
@Validated
@RestController
@RequestMapping("/web/v1/product/lazada")
class ProductLazadaController(
    private val productLazadaService: ProductLazadaService,
    private val platformProductPullTaskService: PlatformProductPullTaskService,
    private val callLazadaComponent: CallLazadaComponent,
    private val esSynchronizer: EsSynchronizer,
) {
    /**
     * 提交拉取Lazada商品任务
     * @param req
     * @return
     */
    @PostMapping("/task/pull/submit")
    fun submitPullTask(@Valid @RequestBody req: ProductLazadaPullReq): DataResponse<Unit> {
        productLazadaService.submitPullTask(req)
        return ok()
    }

    /**
     * 提交同步Lazada商品任务
     * @param req
     * @return
     */
    @PostMapping("/task/sync/submit")
    fun submitSyncTask(@Valid @RequestBody req: ProductLazadaSyncReq): DataResponse<Unit> {
        productLazadaService.submitSyncTask(req)
        return ok()
    }

    /**
     * 分页查询平台商品同步任务
     * @param req 查询条件
     * @return 分页结果
     */
    @PostMapping("/task/page")
    fun page(@Valid @RequestBody req: PlatformProductPullTaskPageQueryReq): DataResponse<PageVo<PlatformProductPullTaskPageVo>> {
        return ok(platformProductPullTaskService.page(req))
    }

    /**
     * 取消任务
     * @param taskId 任务ID
     * @return 操作结果
     */
    @PostMapping("/task/cancel/{taskId}")
    fun cancelTask(@PathVariable @NotNull("任务ID不能为空") taskId: Long): DataResponse<Unit> {
        platformProductPullTaskService.cancelTask(taskId)
        return ok()
    }

    /**
     * 导出平台商品同步任务数据
     * 支持多状态任务导出，支持时间范围和其他筛选条件
     *
     * @param req 导出请求参数
     * @return 导出任务ID
     */
    @PostMapping("/task/submit-export")
    fun submitPlatformProductSyncTaskExport(@RequestBody @Valid req: PlatformProductPullTaskExportReq): DataResponse<Unit> {
        platformProductPullTaskService.submitPlatformProductSyncTaskExport(req)
        return ok()
    }

    /**
     * lazada-商品详情
     * @tags v0904
     *
     * @param req
     * @return ProductDetailResp
     */
    @PostMapping("/detail")
    fun detail(@Validated @RequestBody req: ProductLazadaDetailReq): DataResponse<ProductLazadaDetailResp> {
        return ok(productLazadaService.detail(req))
    }

    /**
     * lazada-商品分页
     *
     * @param req
     * @return ProductDetailResp
     */
    @PostMapping("/page")
    fun page(@Validated @RequestBody req: ProductLazadaPageQueryReq): DataResponse<PageVo<ProductLazadaPageResp>> {
        return ok(productLazadaService.page(req))
    }
    
    /**
     * lazada-商品分页(查数据库)
     *
     * @param req
     * @return ProductDetailResp
     */
    @PostMapping("/page/db")
    fun pageDB(@Validated @RequestBody req: ProductLazadaPageQueryReq): DataResponse<PageVo<ProductLazadaPageResp>> {
        return ok(productLazadaService.queryFromDBAsPageResp(req).toPageVo())
    }

    /**
     * lazada-商品更新
     * @tags v0904
     *
     * @param req
     * @return ProductDetailResp
     */
    @PostMapping("/update")
    fun updateLazada(@Validated @RequestBody req: ProductLazadaPublishedUpdateReq): DataResponse<Unit> {
        productLazadaService.update(req)
        return ok()
    }

    /**
     * lazada-单个/批量上架
     *
     * @param req
     * @return ProductDetailResp
     */
    @PostMapping("/publish")
    fun updateLazadaExistingPublish(@Validated @RequestBody req: ProductLazadaExistingPublishReq): DataResponse<Unit> {
        callLazadaComponent.publishExistingOrAddCountry(req)
        return ok()
    }

    /**
     * 应用更新
     *
     * @param req
     */
    @PostMapping("/apply-product")
    fun applyUpdateProduct(@Validated @RequestBody req: ApplyUpdateProductReq): DataResponse<Unit> {
        productLazadaService.applyUpdateProduct(req)
        return ok()
    }

    /**
     * 弹窗查询-编辑库存/价格
     *
     * @param req
     */
    @PostMapping("/update/sku/stock/price/query")
    fun querySkuByUpdateStockPrice(@Validated @RequestBody req: QuerySkuByUpdateStockPriceReq): DataResponse<List<QuerySkuByUpdateStockPriceResp>> {
        return ok(productLazadaService.querySkuByUpdateStockPrice(req))
    }

    /**
     * 编辑库存/价格
     *
     * @param req
     */
    @PostMapping("/update/sku/stock/price")
    fun updateSkuByUpdateStockPrice(@Validated @RequestBody req: List<UpdateSkuByUpdateStockPriceReq>): DataResponse<Unit> {
        productLazadaService.updateSkuByUpdateStockPrice(req)
        
        // 同步双写ES，确保前端看到最新的数据
        val saleSkuIds = req.map { it.saleSkuId!! }
        esSynchronizer.syncSaleGoodsBySkuIds(saleSkuIds, Refresh.True)
        
        return ok()
    }
}
