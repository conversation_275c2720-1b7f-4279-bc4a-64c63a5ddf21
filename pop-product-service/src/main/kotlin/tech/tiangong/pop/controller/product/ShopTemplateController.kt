package tech.tiangong.pop.controller.product

import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.PageVo
import team.aikero.blade.core.protocol.ok
import tech.tiangong.pop.req.product.temu.*
import tech.tiangong.pop.resp.product.temu.ShopAndConfigResp
import tech.tiangong.pop.resp.product.temu.ShopTemplateDetailResp
import tech.tiangong.pop.resp.product.temu.ShopTemplatePageResp
import tech.tiangong.pop.service.product.ShopTemplateService

/**
 * 店铺上架模板
 */
@Validated
@RestController
@RequestMapping("/web/v1/product/shop-template")
class ShopTemplateController(
    private val shopTemplateService: ShopTemplateService
) {

    /**
     * 分页
     *
     * @param req
     * @return
     */
    @PostMapping("/page")
    fun page(@Validated @RequestBody req: ShopTemplatePageReq): DataResponse<PageVo<ShopTemplatePageResp>> {
        return ok(shopTemplateService.page(req))
    }

    /**
     * 详情
     * @return
     */
    @GetMapping("/detail/{shopTemplateId}")
    fun detail(@PathVariable(name = "shopTemplateId") shopTemplateId: Long): DataResponse<ShopTemplateDetailResp> {
        return ok(shopTemplateService.detail(shopTemplateId))
    }

    /**
     * 创建模板
     */
    @PostMapping("/create")
    fun create(@Validated @RequestBody req: ShopTemplateCreateReq): DataResponse<Long> {
        return ok(shopTemplateService.addTemplate(req))
    }

    /**
     * 编辑
     */
    @PostMapping("/edit")
    fun edit(@Validated @RequestBody req: ShopTemplateUpdateReq): DataResponse<Unit> {
        shopTemplateService.editTemplate(req)
        return ok()
    }

    /**
     * 启动/停用
     */
    @PostMapping("/update-status")
    fun updateStatus(@Validated @RequestBody req: ShopTemplateStateReq): DataResponse<Unit> {
        shopTemplateService.updateStatus(req)
        return ok()
    }

    /**
     * 平台店铺与模板配置信息查询
     * @return
     */
    @GetMapping("/shop-and-config/{platformId}")
    fun shopAndConfigList(@PathVariable(name = "platformId") platformId: Long): DataResponse<List<ShopAndConfigResp>> {
        return ok(shopTemplateService.shopAndConfigList(platformId))
    }

    /**
     * 复制模板
     */
    @PostMapping("/copy")
    fun copyListingTemplate(@Validated @RequestBody req: ShopTemplateCopyReq): DataResponse<List<Long>> {
        return ok(shopTemplateService.copyTemplate(req))
    }

}