package tech.tiangong.pop.controller.settings

import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.PageVo
import team.aikero.blade.core.protocol.ok
import tech.tiangong.pop.req.settings.*
import tech.tiangong.pop.resp.settings.*
import tech.tiangong.pop.service.settings.CustomsAttributeRuleService
import java.io.IOException
import javax.validation.Valid

/**
 * 基础配置-海关属性规则管理
 */
@RestController
@RequestMapping("/web/v1/customer-attribute-rule")
class CustomsAttributeRuleController(
    private val customsAttributeRuleService: CustomsAttributeRuleService,
) {

    /**
     * 列表
     *
     * @param req 查询请求参数
     * @return 分页查询结果
     */
    @PostMapping("/page")
    fun pageRule(@RequestBody @Valid req: CustomsAttributeRulePageReq): DataResponse<PageVo<CustomsAttributeRulePageResp>> {
        val result = customsAttributeRuleService.pageRule(req)
        return ok(result)
    }

    /**
     * 保存
     *
     * @param req 保存请求参数
     * @return 操作结果
     */
    @PostMapping("/save")
    fun saveRule(@Valid @RequestBody req: CustomsAttributeRuleSaveReq): DataResponse<Boolean> {
        val result = customsAttributeRuleService.saveRule(req)
        return ok(result)
    }

    /**
     * 更新
     *
     * @param req 更新请求参数
     * @return 操作结果
     */
    @PostMapping("/update")
    fun updateRule(@Valid @RequestBody req: CustomsAttributeRuleUpdateReq): DataResponse<Boolean> {
        val result = customsAttributeRuleService.updateRule(req)
        return ok(result)
    }

    /**
     * 导入更新
     */
    @PostMapping("/import-update")
    @Throws(IOException::class)
    fun importExcel(@RequestParam("excelFile") excelFile: MultipartFile,
                    @RequestParam("ruleId") ruleId: Long
    ): DataResponse<CustomsAttributeRuleImportResp> {
        return ok(customsAttributeRuleService.importUpdate(excelFile, ruleId))
    }

    /**
     * 查询详情
     */
    @GetMapping("/detail/{ruleId}")
    fun queryRuleDetail(@PathVariable("ruleId") ruleId: Long): DataResponse<CustomsAttributeRuleDetailWebResp> {
        return ok(customsAttributeRuleService.queryRuleDetail(ruleId))
    }

    /**
     * 停用
     *
     * @param req 停用请求参数
     * @return 操作结果
     */
    @PostMapping("/disable")
    fun disableRule(@Valid @RequestBody req: ImagePackRuleStatusReq): DataResponse<Boolean> {
        val result = customsAttributeRuleService.disableRule(req.imagePackRuleId!!)
        return ok(result)
    }

    /**
     * 启用
     *
     * @param req 启用请求参数
     * @return 操作结果
     */
    @PostMapping("/enable")
    fun enableRule(@Valid @RequestBody req: ImagePackRuleStatusReq): DataResponse<Boolean> {
        val result = customsAttributeRuleService.enableRule(req.imagePackRuleId!!)
        return ok(result)
    }


}