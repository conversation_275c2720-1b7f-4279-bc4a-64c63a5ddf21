package tech.tiangong.pop.exception

/**
 * 部分同步异常
 * 当批量同步操作中有部分记录成功、部分记录失败时抛出此异常
 *
 * @param successfulIds 成功同步的记录ID列表
 * @param failedIds 失败的记录ID列表
 * @param message 异常消息
 * @param cause 原始异常
 */
class PartialSyncException(
    /** 成功同步的记录ID列表 */
    val successfulIds: List<Long>,
    /** 失败的记录ID列表 */
    val failedIds: List<Long>,
    message: String = "批量同步部分失败：成功${successfulIds.size}条，失败${failedIds.size}条",
    cause: Throwable? = null
) : RuntimeException(message, cause) {
    
    /** 总记录数 */
    val totalCount: Int get() = successfulIds.size + failedIds.size
    
    /** 成功率 */
    val successRate: Double get() = if (totalCount > 0) successfulIds.size.toDouble() / totalCount else 0.0
    
    /** 是否有成功的记录 */
    val hasSuccessful: Boolean get() = successfulIds.isNotEmpty()
    
    /** 是否有失败的记录 */
    val hasFailed: Boolean get() = failedIds.isNotEmpty()
}