package tech.tiangong.pop.listeners

import com.rabbitmq.client.Channel
import jakarta.annotation.Resource
import org.springframework.amqp.core.Message
import org.springframework.amqp.rabbit.annotation.Exchange
import org.springframework.amqp.rabbit.annotation.Queue
import org.springframework.amqp.rabbit.annotation.QueueBinding
import org.springframework.amqp.rabbit.annotation.RabbitListener
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.parseJson
import tech.tiangong.pop.constant.MqConstants
import tech.tiangong.pop.core.lock.LockComponent
import tech.tiangong.pop.dto.VideoUpsertDto
import tech.tiangong.pop.service.image.VisualImageSyncService
import tech.tiangong.pop.service.mq.MessageDistributeService
import tech.tiangong.sdp.design.vo.dto.VisualImageMqDto

@Slf4j
@Component
@RabbitListener(bindings = [QueueBinding(
    value = Queue(value = MqConstants.QUEUE_POP_PRODUCT_VIDEO_UPSERT, durable = "true"),
    exchange = Exchange(
        value = MqConstants.EXCHANGE_POP_PRODUCT_VIDEO_UPSERT,
        ignoreDeclarationExceptions = Exchange.TRUE
    ),
    key = [MqConstants.KEY_POP_PRODUCT_VIDEO_UPSERT]
)])
class VideoUpsertListener : MqBaseListener() {

    @field:Resource(name = "messageDistributeServiceImplV2")
    override lateinit var messageDistributeService: MessageDistributeService
    @field:Resource
    override lateinit var lockComponent: LockComponent

    @field:Resource
    private lateinit var visualImageSyncService: VisualImageSyncService
    @Transactional(rollbackFor = [Exception::class])
    override fun onMessage(
        message: Message,
        channel: Channel,
        headers: Map<String, Any>,
        deliveryTag: Long,
        payload: String
    ) {
        log.info { "VideoUpsertListener - queue=${message.messageProperties.consumerQueue}, messageId=${message.messageProperties.messageId}, body=${payload.take(256)}" }

        val videoMqDto = payload.parseJson(VideoUpsertDto::class.java)

        if (videoMqDto.styleCode.isBlank()) {
            log.warn { "VideoUpsertListener styleCode为空, messageId=${message.messageProperties.messageId}" }
            return
        }

        visualImageSyncService.syncVideo(videoMqDto)

        log.info { "VideoUpsertListener finish: styleCode=${videoMqDto.styleCode}" }

    }



}