<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tech.tiangong.pop.dao.mapper.GrossMarginRecordMapper">

    <insert id="initGrossMarginRecord">
        <foreach collection="list" item="data" index="index" separator=";">
            insert into gross_margin_record(`record_id`,`product_id`,`shop_id`,`gross_margin`,`creator_id`,`creator_name`,`created_time`,`reviser_id`,`reviser_name`,`revised_time`)
            values(#{data.recordId},#{data.productId},#{data.shopId},#{data.grossMargin},
                                                   #{data.creatorId},#{data.creatorName},#{data.createdTime},
                                                   #{data.reviserId},#{data.reviserName},#{data.revisedTime})
        </foreach>
    </insert>
</mapper>
