<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tech.tiangong.pop.dao.mapper.ProductBarcodeMapper">

    <select id="selectBarcodesBySkcAndSourceGroupCode" resultType="tech.tiangong.pop.common.resp.BarCodeListResp">
        SELECT
        sku_id AS sku,
        barcode,
        spu_code AS spuCode,
        skc,
        color,
        group_name AS groupName,
        source_group_code AS sourceGroupCode,
        size_name AS sizeName,
        product_barcode_id AS productBarcodeId,
        shop_sku AS shopSku
        FROM
        product_barcode
        WHERE
        (skc, source_group_code) IN (
        <foreach item="item" collection="skcSourceGroupPairs" separator=",">
            (#{item.skcCode}, #{item.sizeGroupCode})
        </foreach>)
        AND deleted = 0;
    </select>

    <select id="selectListByBarcode" resultType="tech.tiangong.pop.common.resp.BarCodeListResp">
        SELECT
         *
        FROM
        product_barcode ps
        WHERE
         ps.deleted = 0
        <if test="req.barcodeList != null and req.barcodeList.size > 0">
            AND ps.barcode IN
            <foreach item="item" index="index" collection="req.barcodeList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

    </select>

    <select id="countBarcodeExportData" resultType="java.lang.Long">
        SELECT
        COUNT(1)
        FROM
        product_barcode p
        FORCE INDEX (idx_product_barcode_created_time)
        <include refid="Export_Where_Conditions"/>
    </select>
    
    <select id="queryBarcodeExportData" resultType="tech.tiangong.pop.dto.product.ProductBarcodeExportDto">
        SELECT
        product_barcode_id,
        spu_code,
        skc,
        color,
        barcode,
        design_img_url,
        main_img_url,
        size_name,
        local_price,
        supply_mode,
        group_name,
        category_name,
        source_group_code,
        category_code,
        seller_sku,
        unit,
        creator_name,
        created_time
        FROM
        product_barcode p
        FORCE INDEX (idx_product_barcode_created_time)
        <include refid="Export_Where_Conditions"/>
    </select>

    <select id="pageBarcode" resultType="tech.tiangong.pop.dao.entity.ProductBarcode">
        SELECT
        product_barcode_id,
        spu_code,
        skc,
        color,
        barcode,
        design_img_url,
        main_img_url,
        size_name,
        local_price,
        supply_mode,
        group_name,
        category_name,
        source_group_code,
        category_code,
        seller_sku,
        unit,
        creator_name,
        created_time
        FROM
        product_barcode p
        FORCE INDEX (idx_product_barcode_created_time)
        <include refid="Export_Where_Conditions"/>
        order by p.created_time desc
    </select>

    <!-- 公共条件片段 -->
    <sql id="Export_Where_Conditions">
        <where>
            p.deleted = 0
            <if test="req.barcodeList != null and req.barcodeList.size() > 0">
                AND p.barcode IN
                <foreach collection="req.barcodeList" item="barcode" open="(" separator="," close=")">
                    #{barcode}
                </foreach>
            </if>
            <if test="req.skcList != null and req.skcList.size() > 0">
                AND p.skc IN
                <foreach collection="req.skcList" item="skc" open="(" separator="," close=")">
                    #{skc}
                </foreach>
            </if>
            <if test="req.spuList != null and req.spuList.size() > 0">
                AND p.spu_code IN
                <foreach collection="req.spuList" item="spu" open="(" separator="," close=")">
                    #{spu}
                </foreach>
            </if>
            <if test="req.createdTime != null and req.endTime != null">
                AND p.created_time BETWEEN #{req.createdTime} AND #{req.endTime}
            </if>
            <if test="req.createdTime != null and req.endTime == null">
                AND p.created_time <![CDATA[ >= ]]> #{req.createdTime}
            </if>
            <if test="req.createdTime == null and req.endTime != null">
                AND p.created_time <![CDATA[ <= ]]> #{req.endTime}
            </if>
            <if test="req.supplyMode != null and req.supplyMode != ''">
                AND supply_mode = #{req.supplyMode}
            </if>
            <if test="req.categoryCode != null and req.categoryCode != ''">
                AND category_code = #{req.categoryCode}
            </if>
        </where>
    </sql>

    <!-- 忽略逻辑删除字段查询指定ID的记录 -->
    <select id="findByIdsIgnoreDeletedFilter" resultType="tech.tiangong.pop.dao.entity.ProductBarcode">
        SELECT *
        FROM product_barcode 
        WHERE product_barcode_id IN 
        <foreach collection="barcodeIds" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>
    
    <!-- 全量同步查询（忽略逻辑删除过滤） -->
    <select id="findForFullSyncIgnoreDeleted" resultType="tech.tiangong.pop.dao.entity.ProductBarcode">
        SELECT *
        FROM product_barcode 
        WHERE product_barcode_id > #{lastId}
        ORDER BY product_barcode_id ASC 
        LIMIT #{pageSize}
    </select>
</mapper>
