<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tech.tiangong.pop.dao.mapper.ProductSkcMapper">
    <!-- 查询有效的SKC信息（根据在售SKU过滤） -->
    <select id="selectActiveSkcList" resultType="tech.tiangong.pop.dao.entity.ProductSkc">
        SELECT DISTINCT ps.product_skc_id, ps.color_code, ps.skc, ps.product_id
        FROM product_skc ps
        INNER JOIN sale_sku ss ON ps.product_skc_id = ss.product_skc_id
        WHERE ps.deleted = 0 and ss.deleted = 0
        AND ps.state = 1 AND ss.enable = 1
        AND ss.platform_sku_id IS NOT NULL
        AND ps.product_id IN
        <foreach collection="productIds" item="productId" open="(" separator="," close=")">
            #{productId}
        </foreach>
        AND ss.publish_state NOT IN
        <foreach collection="excludePublishStates" item="state" open="(" separator="," close=")">
            #{state}
        </foreach>
    </select>


    <select id="supplementSkc" resultType="tech.tiangong.pop.dao.entity.dto.SupplementSkcDto">
        select a.product_skc_id, c.skc
        from product_skc a
                 join sale_sku b on a.product_skc_id = b.product_skc_id
                 join product_barcode c on b.barcode = c.barcode
        where a.deleted = 0
          and b.deleted = 0
          and c.deleted = 0
          and (a.skc is null or a.skc = '')
        group by a.product_skc_id
    </select>

    <update id="fillSizeNames">
        <foreach collection="list" item="data" index="index" separator=";">
            update product_skc set size_names = #{data.sizeNames} where product_skc_id = #{data.productSkcId}
        </foreach>
    </update>
</mapper>
