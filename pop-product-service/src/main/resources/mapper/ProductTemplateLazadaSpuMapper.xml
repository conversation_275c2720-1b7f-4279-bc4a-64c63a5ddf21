<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tech.tiangong.pop.dao.mapper.ProductTemplateLazadaSpuMapper">
    <select id="pendingPage" resultType="tech.tiangong.pop.dao.entity.ProductTemplateLazadaSpu">
        SELECT tspu.*
        FROM product_template_lazada_spu tspu
        join product p on tspu.product_id = p.product_id
        JOIN product_template_lazada_skc tskc ON tspu.lazada_spu_id = tskc.lazada_spu_id
        <include refid="pendingListWhere" />
        GROUP BY tspu.lazada_spu_id
        ORDER BY tspu.revised_time DESC
    </select>

    <select id="statistics" resultType="tech.tiangong.pop.dao.entity.dto.ProductPendingListStatisticsDto">
        <!--
        CANCELED(-2, "取消上架"),
        FAILED(-1, "上架失败"),
        PENDING(0, "待开始"),
        STARTING(1, "进行中"),
        COMPLETED(2, "已完成"),
         -->
        select
            sum(if(task_status = 0, 1, 0))        as pendingCount,
            sum(if(task_status = 1, 1, 0))        as startingCount,
            sum(if(task_status = -1, 1, 0))       as failedCount,
            sum(if(resource_state != 1, 1, 0))  as missingInfoCount,
            sum(if(resource_state = 1, 1, 0)) as completeInfoCount
        from (SELECT tspu.lazada_spu_id, tspu.task_status, resource_state
                FROM product_template_lazada_spu tspu
                    join product p on tspu.product_id = p.product_id
                    JOIN product_template_lazada_skc tskc ON tspu.lazada_spu_id = tskc.lazada_spu_id
                    <include refid="pendingListWhere" />
                group by tspu.lazada_spu_id) as t
    </select>

    <sql id="pendingListWhere">
        <where>
            tspu.deleted = 0
            and tskc.deleted = 0
            and p.deleted = 0
            <if test="req.listingUserName != null and req.listingUserName != ''">
                AND tspu.task_executor_name LIKE CONCAT('%', #{req.listingUserName}, '%')
            </if>
            <if test="req.shopName != null and req.shopName != ''">
                AND p.shop_name LIKE CONCAT('%', #{req.shopName}, '%')
            </if>
            <if test="req.listingShopIdList != null and req.listingShopIdList.size > 0">
                AND tspu.shop_id IN
                <foreach item="item" index="index" collection="req.listingShopIdList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.country != null and req.country != ''">
                AND p.countrys LIKE CONCAT('%', #{req.country}, '%')
            </if>
            <if test="req.supplyMode != null and req.supplyMode != ''">
                AND p.supply_mode = #{req.supplyMode}
            </if>
            <if test="req.supplyModeList != null and req.supplyModeList.size > 0">
                AND p.supply_mode IN
                <foreach item="item" index="index" collection="req.supplyModeList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.selectStyleName != null and req.selectStyleName != ''">
                AND p.select_style_name  LIKE CONCAT('%', #{req.selectStyleName}, '%')
            </if>
            <if test="req.spuList != null and req.spuList.size > 1">
                AND p.spu_code IN
                <foreach item="item" index="index" collection="req.spuList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.spuList != null and req.spuList.size == 1">
                AND p.spu_code
                <foreach item="item" index="index" collection="req.spuList">
                    LIKE CONCAT('%', #{item}, '%')
                </foreach>
            </if>
            <if test="req.waves != null and req.waves != ''">
                AND p.waves = #{req.waves}
            </if>
            <if test="req.wavesList != null and req.wavesList.size > 0">
                AND p.waves IN
                <foreach item="item" index="index" collection="req.wavesList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.creatorName != null and req.creatorName != ''">
                AND p.creator_name LIKE CONCAT('%', #{req.creatorName}, '%')
            </if>
            <if test="req.createdTimeStart != null and req.createdTimeEnd != null  ">
                AND p.created_time BETWEEN #{req.createdTimeStart} AND #{req.createdTimeEnd}
            </if>
            <if test="req.skcList != null and req.skcList.size > 1">
                AND tskc.skc IN
                <foreach item="item" index="index" collection="req.skcList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.skcList != null and req.skcList.size == 1">
                AND tskc.skc
                <foreach item="item" index="index" collection="req.skcList">
                    LIKE CONCAT('%', #{item}, '%')
                </foreach>
            </if>
            <if test="req.clothingStyleCode != null and req.clothingStyleCode != ''">
                AND p.clothing_style_code = #{req.clothingStyleCode}
            </if>
            <if test="req.categoryCodeList != null and req.categoryCodeList.size > 0">
                AND p.category_code IN
                <foreach item="item" index="index" collection="req.categoryCodeList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.goodsTypeList != null and req.goodsTypeList.size > 0">
                AND p.goods_type IN
                <foreach item="item" index="index" collection="req.goodsTypeList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.imagePackageStateList != null and req.imagePackageStateList.size > 0">
                AND p.image_package_state IN
                <foreach item="item" index="index" collection="req.imagePackageStateList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.planAuditStateList != null and req.planAuditStateList.size > 0">
                AND p.plan_audit_state IN
                <foreach item="item" index="index" collection="req.planAuditStateList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.missingInfoFlag != null and req.missingInfoFlag != ''">
                AND p.resource_state = #{req.missingInfoFlag}
            </if>
            <if test="req.taskStateList != null and req.taskStateList.size > 0">
                AND tspu.task_status IN
                <foreach item="item" index="index" collection="req.taskStateList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="req.videoStateList != null and req.videoStateList.size > 0">
                AND p.video_state IN
                <foreach item="item" index="index" collection="req.videoStateList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            AND tspu.task_status != 2
            AND tspu.task_status != -2
            <!-- Include the tag filtering condition -->
            <include refid="tagFilterCondition" />
        </where>
    </sql>

    <!-- Define the SQL fragment for tag filtering -->
    <sql id="tagFilterCondition">
        <if test="req.tagCodes != null and req.tagCodes.size > 0">
            AND (
            <!-- Check product level tags -->
            EXISTS (
            SELECT 1 FROM product_tag pt_prod
            WHERE pt_prod.target_id = p.product_id
            AND pt_prod.target_type = 'PRODUCT_ID'
            AND pt_prod.deleted = 0
            AND (
            <foreach collection="req.tagCodes" item="valueList" index="key" separator=" OR ">
                (pt_prod.tag_key = #{key} AND pt_prod.tag_value IN
                <foreach collection="valueList" item="value" open="(" close=")" separator=",">
                    #{value}
                </foreach>
                )
            </foreach>
            )
            )

            <!-- Check SPU level tags -->
            OR EXISTS (
            SELECT 1 FROM product_template_lazada_spu ptas
            JOIN product_tag pt_ae ON ptas.lazada_spu_id = pt_ae.target_id
            WHERE ptas.product_id = p.product_id
            AND ptas.deleted = 0
            AND pt_ae.target_type = 'LAZADA_SPU_ID'
            AND pt_ae.deleted = 0
            AND (
            <foreach collection="req.tagCodes" item="valueList" index="key" separator=" OR ">
                (pt_ae.tag_key = #{key} AND pt_ae.tag_value IN
                <foreach collection="valueList" item="value" open="(" close=")" separator=",">
                    #{value}
                </foreach>
                )
            </foreach>
            )
            )

            )
        </if>
    </sql>
</mapper>

