<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tech.tiangong.pop.dao.mapper.PublishAttributeGroupMapper">
    <select id="countAttributeAmount" resultType="tech.tiangong.pop.dto.GroupCountAttributeDto">
        select count(1) as amount,attribute_group_id as groupId
        from publish_attribute
        where deleted=0
        <if test="groupIds != null and groupIds.size > 1">
            and attribute_group_id IN
            <foreach item="item" index="index" collection="groupIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        group by attribute_group_id
    </select>

    <select id="listAllGroupIdByAttributeIds">
        select distinct attribute_group_id
        from publish_attribute
        where attribute_id IN
        <foreach item="item" index="index" collection="attributeIds" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="listAttributesByGroupName" resultType="tech.tiangong.pop.resp.category.GroupAttributeResp">
        SELECT
        a.attribute_id,
        a.attribute_group_id,
        a.attribute_name,
        a.attribute_code,
        a.state,
        a.show_type,

        g.group_name,
        g.group_type

        FROM
        publish_attribute a
        LEFT JOIN publish_attribute_group g on a.attribute_group_id = g.attribute_group_id
        WHERE
        g.group_name = #{groupName}
        AND a.deleted = 0
        AND g.deleted = 0
        <if test="listEnable!=null and listEnable==1">
            AND a.state = 1
            AND g.state = 1
        </if>
    </select>
</mapper>

