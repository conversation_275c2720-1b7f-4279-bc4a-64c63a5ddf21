<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tech.tiangong.pop.dao.mapper.PublishCategoryAttrMapper">

    <resultMap type="tech.tiangong.pop.dao.entity.PublishCategoryAttr" id="PublishCategoryAttrMap">
        <!--@mbg.generated-->
        <!--@Table publish_category_attr-->
        <id property="categoryAttrId" column="category_attr_id" jdbcType="BIGINT"/>
        <result property="categoryId" column="category_id" jdbcType="BIGINT"/>
        <result property="attributeId" column="attribute_id" jdbcType="BIGINT"/>
        <result property="creatorId" column="creator_id" jdbcType="BIGINT"/>
        <result property="creatorName" column="creator_name" jdbcType="VARCHAR"/>
        <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
        <result property="reviserId" column="reviser_id" jdbcType="BIGINT"/>
        <result property="reviserName" column="reviser_name" jdbcType="VARCHAR"/>
        <result property="revisedTime" column="revised_time" jdbcType="TIMESTAMP"/>
        <result property="deleted" column="deleted" jdbcType="INTEGER"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        category_attr_id
        ,category_id
        ,attribute_id
        ,creator_id
        ,creator_name
        ,created_time
        ,reviser_id
        ,reviser_name
        ,revised_time
        ,deleted
    </sql>


    <resultMap id="AttributeMapperResultMap" type="tech.tiangong.pop.resp.category.AttributeMapperResp">
        <id column="platform_attr_id" property="platformAttrId"/>
        <result column="category_id" property="categoryId"/>
        <result column="platform_id" property="platformId"/>
        <result column="category_mapping_id" property="categoryMappingId"/>
        <result column="attribute_id" property="attributeId"/>
        <result column="attribute_name" property="attributeName"/>
        <result column="platform_attribute_label_name" property="platformAttributeLabelName"/>
        <result column="platform_attribute_key_name" property="platformAttributeKeyName"/>
    </resultMap>


    <resultMap id="AttributeValueMapperResultMap" type="tech.tiangong.pop.resp.category.AttributeValueMapperResp">
        <id column="platform_attr_value_id" property="platformAttrValueId"/>
        <result column="attribute_value_id" property="attributeValueId"/>
        <result column="attribute_id" property="attributeId"/>
        <result column="attribute_value" property="attributeValue"/>
        <result column="platform_attribute_value" property="platformAttributeValue"/>
    </resultMap>


    <select id="findByCategoryAttrIds" resultMap="AttributeMapperResultMap">
        SELECT
            pa.platform_attr_id as platform_attr_id,
            pca.category_id as category_id,
            pa.platform_id as platform_id,
            pa.category_mapping_id as category_mapping_id,
            a.attribute_id as attribute_id,
            a.attribute_name as attribute_name,
            pa.platform_attribute_label_name as platform_attribute_label_name,
            pa.platform_attribute_key_name as platform_attribute_key_name
        FROM
            publish_category_attr pca
                JOIN
            publish_attribute a ON pca.attribute_id = a.attribute_id
                LEFT JOIN
            publish_platform_attr pa ON pa.attribute_id = a.attribute_id
                AND pa.category_mapping_id =#{categoryMappingId}
                AND pa.deleted = 0
        WHERE
            pca.category_id = #{categoryId}
          AND pca.deleted = 0
          AND a.deleted = 0

    </select>

    <select id="listAttributesByCategoryId" resultType="tech.tiangong.pop.common.resp.CategoryAttributeResp">
        SELECT
            a.attribute_id,
            a.attribute_group_id,
            a.attribute_name,
            a.attribute_code,
            a.state,
            a.show_type,

            g.group_name,
            g.group_type,

            pca.category_id,
            pca.request_flag as popRequestFlag
        FROM
            publish_category_attr pca
            LEFT JOIN publish_attribute a ON pca.attribute_id = a.attribute_id
            LEFT JOIN publish_attribute_group g on a.attribute_group_id = g.attribute_group_id
        WHERE
            pca.category_id = #{categoryId}
          AND pca.deleted = 0
          AND a.deleted = 0
          AND g.deleted = 0
          <if test="listEnable!=null and listEnable==1">
              AND a.state = 1
              AND g.state = 1
          </if>
    </select>

    <select id="findLazadaPlatformAttributes" resultType="tech.tiangong.pop.dto.PublishPlatformAttributesDTO">
        SELECT
        lazadaAttr.platform_attribute_key_name AS platformAttributeKeyName,
        lazadaAttr.platform_attribute_label_name AS platformAttributeLabelName,
        lazadaValue.platform_attribute_value AS platformAttributeValue,
        saasAttr.platform_attribute_key_name AS saasPlatformAttributeKeyName,
        saasAttr.platform_attribute_label_name AS saasPlatformAttributeLabelName,
        saasValue.platform_attribute_value AS saasPlatformAttributeValue,
        saasValue.platform_attribute_code AS saasPlatformAttributeValueCode
        FROM
        publish_platform_attr saasAttr
        JOIN
        publish_platform_attr_value saasValue
        ON saasAttr.platform_attr_id = saasValue.publish_platform_attr_id
        JOIN
        publish_attribute_value attr_value
        ON saasValue.attribute_value_id = attr_value.attribute_value_id and attr_value.state = 1
        JOIN
        publish_platform_attr lazadaAttr
        ON saasAttr.attribute_id = lazadaAttr.attribute_id
        JOIN
        publish_platform_attr_value lazadaValue
        ON lazadaAttr.platform_attr_id = lazadaValue.publish_platform_attr_id
        WHERE
        saasAttr.platform_id = (
        SELECT platform_id
        FROM publish_platform
        WHERE platform_name = #{saasPlatformName}
        )
        AND
        lazadaAttr.platform_id = (
        SELECT platform_id
        FROM publish_platform
        WHERE platform_name = #{lazadaPlatformName}
        )
        <!--AND  saasAttr.platform_attribute_key_name in
          <foreach collection="saasAttrCodeSets" item="item" open="(" close=")" separator=",">
              #{item}
          </foreach>
        <if test="saasAttrCodeSets != null and saasAttrCodeSets.size() > 0">
              AND saasValue.platform_attribute_code IN
              <foreach collection="saasAttrCodeSets" item="item" open="(" close=")" separator=",">
                  #{item}
              </foreach>
          </if>-->
        AND
        saasValue.attribute_value_id = lazadaValue.attribute_value_id
        AND
        saasAttr.category_mapping_id = #{saasCategoryMappingId}
        AND
        lazadaAttr.category_mapping_id = #{lazadaCategoryMappingId}
        And saasAttr.deleted = 0
        And saasValue.deleted = 0
        And attr_value.deleted = 0
        And lazadaAttr.deleted = 0
        And lazadaValue.deleted = 0
    </select>
</mapper>

