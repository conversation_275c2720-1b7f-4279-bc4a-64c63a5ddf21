<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tech.tiangong.pop.dao.mapper.ShopMapper">
    <!-- 可以在这里添加自定义的SQL语句 -->
    <select id="getInnerShopList" parameterType="tech.tiangong.pop.common.req.ShopReq" resultType="tech.tiangong.pop.common.resp.ShopResp">
        SELECT
        s.shop_id,
        s.platform_seller_id,
        s.platform_id,
        p.platform_name,
        s.channel_id,
        pc.channel_name,
        s.shop_name,
        s.short_code,
        s.brand_id,
        s.brand_name,
        s.remark,
        s.token,
        s.is_auth,
        s.country,
        s.country_type,
        s.shop_alias,
        s.business_type,
        s.external,
        s.entity_code,
        s.temu_site
        FROM shop s
        LEFT JOIN publish_platform p ON s.platform_id = p.platform_id
        LEFT JOIN publish_channel pc ON s.channel_id = pc.channel_id
        <where>
            s.deleted = 0
            <if test="shopName != null and shopName != ''">
                AND s.shop_name LIKE CONCAT('%', #{shopName}, '%')
            </if>
            <if test="shopNames != null and shopNames.size() > 0">
                AND s.shop_name IN
                <foreach collection="shopNames" item="name" open="(" separator="," close=")">
                    #{name}
                </foreach>
            </if>
            <if test="isAuth != null">
                AND s.is_auth = #{isAuth}
            </if>
            <if test="platformId != null">
                AND s.platform_id = #{platformId}
            </if>
        </where>
        ORDER BY s.sort desc, s.shop_id DESC
    </select>


    <!-- 可以在这里添加自定义的SQL语句 -->
    <select id="page"  resultType="tech.tiangong.pop.common.resp.ShopResp">
        SELECT
        s.shop_id,
        s.platform_seller_id,
        s.platform_id,
        p.platform_name,
        s.channel_id,
        pc.channel_name,
        s.shop_name,
        s.shop_alias,
        s.short_code,
        s.brand_id,
        s.brand_name,
        s.remark,
        s.token,
        s.is_auth,
        s.country,
        s.country_type,
        s.business_type,
        s.external,
        s.creator_name,
        s.created_time,
        s.operator_name,
        s.operated_time,
        s.entity_code,
        s.market_code,
        s.sort
        FROM shop s
        LEFT JOIN publish_platform p ON s.platform_id = p.platform_id
        LEFT JOIN publish_channel pc ON s.channel_id = pc.channel_id
        <where>
            s.deleted = 0
            <if test="req.shopName != null and req.shopName != ''">
                AND s.shop_name LIKE CONCAT('%', #{req.shopName}, '%')
            </if>
            <if test="req.isAuth != null">
                AND s.is_auth = #{req.isAuth}
            </if>
        </where>
        ORDER BY s.shop_id DESC
    </select>

</mapper>
