<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tech.tiangong.pop.dao.mapper.ShopTemplateMapper">
    <!-- 可以在这里添加自定义的SQL语句 -->

    <select id="listShopTemplateDTO" resultType="tech.tiangong.pop.dto.product.ShopTemplateDTO">
        select t.*, td.*
        from shop_template t
        left join shop_template_detail td on t.shop_template_id = td.shop_template_id
        where t.detete = 0 and t.state = #{state}
          and t.ship_id in
        <foreach collection="shopIds" item="shopId" open="(" separator="," close=")">
            #{shopId}
        </foreach>

    </select>


</mapper>
