import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.transaction.PlatformTransactionManager
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import tech.tiangong.pop.PopApplication
import tech.tiangong.pop.controller.planning.PlanningController

/**
 * <AUTHOR>
 * @date 2024/11/13 14:35
 */
@SpringBootTest(
    classes = [PopApplication::class],
    args = [
        "--spring.cloud.nacos.discovery.register-enabled=false",
        "--env=dev",
        "--app=ola",
        "--spring.profiles.active=\${env}-\${app}",
        "--spring.config.import=https://jv-devops.oss-cn-hangzhou.aliyuncs.com/\${env}/\${app}/middleware-conf.yaml"
    ]
)
@Disabled
@Slf4j
class PlanningTest {
    @Autowired
    lateinit var planningController: PlanningController

    @Autowired
    lateinit var transactionManager: PlatformTransactionManager

    @Test
    fun demoTest() {
//        // 弄一个假用户
//        val user = CurrentUser(
//            tenantId = 1487,
//            id = 7097773421496389689,
//            name = "测试回滚",
//            code = "FASHION",
//        )
//        withMockedCurrentUserContent(user) {
//            println("Hello World!")
//            val req = PlanningSummaryCreateReq()
//            req.planningName = "test"
//            val resp = planningController.summaryCreate(req)
//            val s = planningController.detail(1)
//            println(s.toJson())
//        }

        try {
            throw Exception("抛出xxx")
        } catch (e: Exception) {
            log.error {"测试异常1 ${e.message}"}
            log.error(e) {"测试异常2 ${e.message}"}
        }
    }
}