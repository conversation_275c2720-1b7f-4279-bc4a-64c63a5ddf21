package tech.tiangong.pop.component.config

import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.util.assertion.Assert
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.PopApplication

/**
 * 海关属性配置组件测试类
 */
@SpringBootTest(
    classes = [PopApplication::class],
    args = [
        "--spring.cloud.nacos.discovery.register-enabled=false",
        "--env=dev",
        "--app=ola",
        "--spring.profiles.active=\${env}-\${app}",
        "--spring.config.import=https://jv-devops.oss-cn-hangzhou.aliyuncs.com/\${env}/\${app}/middleware-conf.yaml"
    ]
)
@Disabled
@Slf4j
class CustomsAttributeRuleComponentTest {

    @Autowired
    private lateinit var customsAttributeRuleComponent: CustomsAttributeRuleComponent

    /**
     * 品类获取税号配置
     */
    @Test
    fun getCustomsAttributeRuleByCategoryCodeTest() {
        /*
            category_code = 01-0101-010101
            category_code = 01-0111-011101
            category_code = 01-0111-011101
            category_code = 01-0101-010111
         */
        // 成功
        val categoryCode = "01-0101-010101"
        val detail = customsAttributeRuleComponent.getCustomsAttributeRuleByCategoryCode(categoryCode)
        Assert.isTrue(detail != null && detail.hsCode?.isNotBlank() == true, "税号获取失败: categoryCode=${categoryCode} detail=${detail?.toJson()}")

        // 失败
        val categoryCode1 = "01-0101-01010222"
        val detail1 = customsAttributeRuleComponent.getCustomsAttributeRuleByCategoryCode(categoryCode1)
        Assert.isTrue(detail1 != null && detail1.hsCode?.isNotBlank() == true, "税号获取失败: categoryCode=${categoryCode1} detail=${detail1?.toJson()}")
    }
}