package tech.tiangong.pop.component.temu

import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.*
import team.aikero.blade.core.protocol.R
import team.aikero.blade.util.json.toJson
import tech.tiangong.eis.client.TemuClient
import tech.tiangong.eis.temu.req.*
import tech.tiangong.eis.temu.resp.*
import tech.tiangong.pop.config.TemuProperties
import tech.tiangong.pop.dao.entity.*
import tech.tiangong.pop.dao.entity.dto.ProductSizeJsonDto
import tech.tiangong.pop.dao.repository.*
import tech.tiangong.pop.dto.CategoryItemDTO

/**
 * TemuUpdateProductComponent.createSizeCharts 方法的单元测试
 * 
 * 测试覆盖：
 * 1. 正常情况下成功创建尺码表
 * 2. categoryId 为 null 的异常情况
 * 3. 空尺码详情的处理
 * 4. API 调用失败的情况
 * 5. 数据转换异常的处理
 * 6. 尺码映射缺失的情况
 */
@ExtendWith(MockitoExtension::class)
class TemuUpdateProductComponentCreateSizeChartsTest {

    @Mock
    private lateinit var productRepository: ProductRepository
    
    @Mock
    private lateinit var shopRepository: ShopRepository
    
    @Mock
    private lateinit var temuSaleGoodsRepository: TemuSaleGoodsRepository
    
    @Mock
    private lateinit var temuSaleSkcRepository: TemuSaleSkcRepository
    
    @Mock
    private lateinit var temuSaleSkuRepository: TemuSaleSkuRepository
    
    @Mock
    private lateinit var imageRepositoryRepository: ImageRepositoryRepository
    
    @Mock
    private lateinit var productSyncLogRepository: ProductSyncLogRepository
    
    @Mock
    private lateinit var publishCategoryMappingRepository: PublishCategoryMappingRepository
    
    @Mock
    private lateinit var inspirationClientExternal: tech.tiangong.pop.external.InspirationClientExternal
    
    @Mock
    private lateinit var imageCollectionHelper: tech.tiangong.pop.helper.ImageCollectionHelper
    
    @Mock
    private lateinit var eisCenterClientExternal: tech.tiangong.pop.external.EisCenterClientExternal
    
    @Mock
    private lateinit var temuClient: TemuClient
    
    @Mock
    private lateinit var temuCategoryRepository: TemuCategoryRepository
    
    @Mock
    private lateinit var temuService: tech.tiangong.pop.service.TemuService
    
    @Mock
    private lateinit var temuSkcDetailedImageRepository: TemuSkcDetailedImageRepository
    
    @Mock
    private lateinit var lockComponent: tech.tiangong.pop.core.lock.LockComponent
    
    @Mock
    private lateinit var productPublishTemuHelper: tech.tiangong.pop.helper.ProductPublishTemuHelper
    
    @Mock
    private lateinit var saleProductPushImageTaskService: tech.tiangong.pop.service.product.SaleProductPushImageTaskService
    
    @Mock
    private lateinit var temuProperties: TemuProperties
    
    @Mock
    private lateinit var messageRecordService: tech.tiangong.pop.service.mq.MessageRecordService
    
    @Mock
    private lateinit var imagePackCollectionHelper: tech.tiangong.pop.helper.ImagePackCollectionHelper
    
    @Mock
    private lateinit var productSaleAttributesV2Repository: ProductSaleAttributesV2Repository
    
    @Mock
    private lateinit var productPublishStoreService: tech.tiangong.pop.service.ProductPublishStoreService
    
    @Mock
    private lateinit var productSaleSizeDetailRepository: ProductSaleSizeDetailRepository

    @InjectMocks
    private lateinit var temuUpdateProductComponent: TemuUpdateProductComponent

    private lateinit var categoryItemDTOS: List<CategoryItemDTO>
    private lateinit var saleGoods: TemuSaleGoods
    private lateinit var shop: Shop
    private lateinit var product: Product

    @BeforeEach
    fun setUp() {
        // 准备测试数据
        categoryItemDTOS = listOf(
            CategoryItemDTO().apply {
                categoryId = "12345"
                categoryName = "服装"
                leaf = true
            }
        )

        saleGoods = TemuSaleGoods().apply {
            saleGoodsId = 1L
            productId = 1L
            spuCode = "TEST_SPU_001"
        }

        shop = Shop().apply {
            shopId = 1L
            country = "CN"
            shortCode = "TEST_SHOP"
        }

        product = Product().apply {
            productId = 1L
            spuCode = "TEST_SPU_001"
        }
    }

    @Test
    fun `测试正常情况下成功创建尺码表`() {
        // Given - 准备测试数据
        val sizeDetails = listOf(
            ProductSaleSizeDetail().apply {
                sizeDetailId = 1L
                partName = "胸围"
                sizeJson = """[{"size":"S","data":"90"},{"size":"M","data":"95"},{"size":"L","data":"100"}]"""
            },
            ProductSaleSizeDetail().apply {
                sizeDetailId = 2L
                partName = "肩宽"
                sizeJson = """[{"size":"S","data":"40"},{"size":"M","data":"42"},{"size":"L","data":"44"}]"""
            }
        )

        val sizechartsClassGetResp = TemuSizechartsClassGetResp().apply {
            sizeSpecClassCat = TemuSizechartsClassGetResp.SizeSpecClassCat().apply {
                classId = 100L
            }
        }

        val chartsSettingsResp = TemuSizeChartsSettingsResp().apply {
            code = "STANDARD"
        }

        val sizeChartsMetaResp = TemuSizeChartsMetaGetResp().apply {
            sizeSpecMeta = TemuSizeChartsMetaGetResp.SizeSpecMeta().apply {
                groupList = listOf(
                    TemuSizeChartsMetaGetResp.SizeSpecMeta.Group().apply {
                        id = 1L
                        name = "胸围"
                        unnecessary = false
                    },
                    TemuSizeChartsMetaGetResp.SizeSpecMeta.Group().apply {
                        id = 2L
                        name = "肩宽"
                        unnecessary = false
                    }
                )
                elementList = listOf(
                    TemuSizeChartsMetaGetResp.SizeSpecMeta.Element().apply {
                        id = 10L
                        name = "S"
                        necessary = true
                    },
                    TemuSizeChartsMetaGetResp.SizeSpecMeta.Element().apply {
                        id = 11L
                        name = "M"
                        necessary = true
                    },
                    TemuSizeChartsMetaGetResp.SizeSpecMeta.Element().apply {
                        id = 12L
                        name = "L"
                        necessary = true
                    }
                )
            }
        }

        val createSizeChartResp = TemuCreateSizeChartResp().apply {
            businessId = 999L
        }

        // 配置尺码映射
        val sizeMapping = mapOf("S" to "S", "M" to "M", "L" to "L")
        whenever(temuProperties.sizeMapping).thenReturn(sizeMapping)
        whenever(temuProperties.displaySizeChartsTemplate).thenReturn(true)

        // Mock repository 调用
        whenever(productSaleSizeDetailRepository.listBySaleGoodsId(1L)).thenReturn(sizeDetails)

        // Mock API 调用
        whenever(temuClient.goodsSizechartsClassGet(any())).thenReturn(R.success(sizechartsClassGetResp))
        whenever(temuClient.goodsSizeChartsSettings(any())).thenReturn(R.success(chartsSettingsResp))
        whenever(temuClient.goodsSizeChartsMetaGet(any())).thenReturn(R.success(sizeChartsMetaResp))
        whenever(temuClient.createSizeChart(any())).thenReturn(R.success(createSizeChartResp))

        // When - 执行测试方法
        val result = temuUpdateProductComponent.createSizeCharts(categoryItemDTOS, saleGoods, shop, product)

        // Then - 验证结果
        assertNotNull(result)
        assertEquals(999L, result?.businessId)

        // 验证方法调用
        verify(productSaleSizeDetailRepository).listBySaleGoodsId(1L)
        verify(temuClient).goodsSizechartsClassGet(any())
        verify(temuClient).goodsSizeChartsSettings(any())
        verify(temuClient).goodsSizeChartsMetaGet(any())
        verify(temuClient).createSizeChart(any())
    }

    @Test
    fun `测试 categoryId 为 null 时抛出异常`() {
        // Given - 准备无效的分类数据
        val invalidCategoryItemDTOS = listOf(
            CategoryItemDTO().apply {
                categoryId = null
                categoryName = "服装"
                leaf = true
            }
        )

        // When & Then - 验证异常抛出
        val exception = assertThrows<IllegalArgumentException> {
            temuUpdateProductComponent.createSizeCharts(invalidCategoryItemDTOS, saleGoods, shop, product)
        }

        assertEquals("categoryId is null", exception.message)

        // 验证没有调用其他方法
        verify(productSaleSizeDetailRepository, never()).listBySaleGoodsId(any())
        verify(temuClient, never()).goodsSizechartsClassGet(any())
    }

    @Test
    fun `测试空尺码详情的处理`() {
        // Given - 准备空的尺码详情
        whenever(productSaleSizeDetailRepository.listBySaleGoodsId(1L)).thenReturn(emptyList())

        val sizechartsClassGetResp = TemuSizechartsClassGetResp().apply {
            sizeSpecClassCat = TemuSizechartsClassGetResp.SizeSpecClassCat().apply {
                classId = 100L
            }
        }

        val chartsSettingsResp = TemuSizeChartsSettingsResp().apply {
            code = "STANDARD"
        }

        val sizeChartsMetaResp = TemuSizeChartsMetaGetResp().apply {
            sizeSpecMeta = TemuSizeChartsMetaGetResp.SizeSpecMeta().apply {
                groupList = emptyList()
                elementList = emptyList()
            }
        }

        val createSizeChartResp = TemuCreateSizeChartResp().apply {
            businessId = 888L
        }

        whenever(temuProperties.sizeMapping).thenReturn(emptyMap())
        whenever(temuProperties.displaySizeChartsTemplate).thenReturn(false)

        // Mock API 调用
        whenever(temuClient.goodsSizechartsClassGet(any())).thenReturn(R.success(sizechartsClassGetResp))
        whenever(temuClient.goodsSizeChartsSettings(any())).thenReturn(R.success(chartsSettingsResp))
        whenever(temuClient.goodsSizeChartsMetaGet(any())).thenReturn(R.success(sizeChartsMetaResp))
        whenever(temuClient.createSizeChart(any())).thenReturn(R.success(createSizeChartResp))

        // When - 执行测试方法
        val result = temuUpdateProductComponent.createSizeCharts(categoryItemDTOS, saleGoods, shop, product)

        // Then - 验证结果
        assertNotNull(result)
        assertEquals(888L, result?.businessId)

        // 验证方法调用
        verify(productSaleSizeDetailRepository).listBySaleGoodsId(1L)
        verify(temuClient).createSizeChart(argThat { req ->
            req.content?.records?.isEmpty() == true
        })
    }
