package tech.tiangong.pop.controller

import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import team.aikero.blade.logging.core.annotation.Slf4j
import tech.tiangong.pop.PopApplication
import tech.tiangong.pop.service.FixData2Service

@SpringBootTest(
    classes = [PopApplication::class],
    args = [
        "--spring.cloud.nacos.discovery.register-enabled=false",
        "--env=dev",
        "--app=ola",
        "--spring.profiles.active=\${env}-\${app}",
        "--spring.config.import=https://jv-devops.oss-cn-hangzhou.aliyuncs.com/\${env}/\${app}/middleware-conf.yaml"
    ]
)
@Disabled
@Slf4j
class TestInnerControllerTest {

    @Autowired
    private lateinit var fixData2Service: FixData2Service

    @Test
    fun fixTemplateSkuShopIdTest() {
        fixData2Service.fixTemplateSkuShopId()
    }
}