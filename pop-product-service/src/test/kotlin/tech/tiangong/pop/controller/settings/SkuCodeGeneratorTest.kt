package tech.tiangong.pop.controller.settings

import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import team.aikero.blade.logging.core.annotation.Slf4j
import tech.tiangong.pop.PopApplication
import tech.tiangong.pop.req.settings.SkuCodeGenerateReq
import tech.tiangong.pop.service.SkuCodeGenerateService
import test.BaseTest

@SpringBootTest(
    classes = [PopApplication::class],
    args = [
        "--spring.cloud.nacos.discovery.register-enabled=false",
        "--env=dev",
        "--app=ola",
        "--spring.profiles.active=\${env}-\${app}",
        "--spring.config.import=https://jv-devops.oss-cn-hangzhou.aliyuncs.com/\${env}/\${app}/middleware-conf.yaml"
    ]
)
@Disabled
@Slf4j
class SkuCodeGeneratorTest : BaseTest() {
    @Autowired
    lateinit var generator: SkuCodeGenerateService


    @Test
    fun `generateSkuCode with custom rule type should return valid SKU code`() {
        // Arrange
        mockedUser {
            val req = SkuCodeGenerateReq(
                1, 1, "AIGC", "SPU", "S", "White", "", ""
            )

            // Act
            val skuCode = generator.generateSkuCode(req)
            println("SKU code: $skuCode")
            // Assert
            assertNotNull(skuCode)
        }
    }

}
