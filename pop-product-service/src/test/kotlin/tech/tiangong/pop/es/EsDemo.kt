package tech.tiangong.pop.es

import org.dromara.easyes.annotation.IndexField
import org.dromara.easyes.annotation.IndexId
import org.dromara.easyes.annotation.IndexName
import org.dromara.easyes.annotation.InnerIndexField
import org.dromara.easyes.annotation.MultiIndexField
import org.dromara.easyes.annotation.Settings
import org.dromara.easyes.annotation.rely.Analyzer
import org.dromara.easyes.annotation.rely.FieldType
import org.dromara.easyes.annotation.rely.IdType

@Settings(shardsNum = 3, replicasNum = 2)
@IndexName(value = "_es_demo", keepGlobalPrefix = true)
class EsDemo {

    @IndexId(type = IdType.CUSTOMIZE)
    var id: String? = null

    @MultiIndexField(
        mainIndexField = IndexField(fieldType = FieldType.TEXT, analyzer = Analyzer.IK_MAX_WORD, searchAnalyzer = Analyzer.IK_SMART),
        otherIndexFields = [
            InnerIndexField(suffix = "PH", fieldType = FieldType.TEXT, analyzer = Analyzer.STANDARD, searchAnalyzer = Analyzer.STANDARD),
            InnerIndexField(suffix = "TH", fieldType = FieldType.TEXT, analyzer = "thai", searchAnalyzer = "thai"),
            InnerIndexField(suffix = "VN", fieldType = FieldType.TEXT, analyzer = "icu_analyzer", searchAnalyzer = "icu_analyzer"),
            InnerIndexField(suffix = "SG", fieldType = FieldType.TEXT, analyzer = Analyzer.IK_MAX_WORD, searchAnalyzer = Analyzer.IK_SMART),
            InnerIndexField(suffix = "MY", fieldType = FieldType.TEXT, analyzer = "indonesian", searchAnalyzer = "indonesian"),
            InnerIndexField(suffix = "ID", fieldType = FieldType.TEXT, analyzer = "indonesian", searchAnalyzer = "indonesian"),
        ]
    )
    var productTitle: String? = null
}