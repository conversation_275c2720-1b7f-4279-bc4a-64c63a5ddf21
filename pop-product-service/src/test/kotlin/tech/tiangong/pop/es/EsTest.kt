package tech.tiangong.pop.es

import co.elastic.clients.elasticsearch._types.Refresh
import org.assertj.core.api.Assertions.assertThat
import org.dromara.easyes.spring.annotation.EsMapperScan
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import tech.tiangong.pop.PopApplication
import tech.tiangong.pop.es.mapper.EsDemoMapper
import java.util.UUID

@SpringBootTest(
    classes = [PopApplication::class],
    args = [
        "--spring.cloud.nacos.discovery.register-enabled=false",
        "--env=qa",
        "--app=ola",
        "--spring.profiles.active=\${env}-\${app}",
        "--spring.config.import=https://jv-devops.oss-cn-hangzhou.aliyuncs.com/\${env}/\${app}/middleware-conf.yaml"
    ]
)
@EsMapperScan("tech.tiangong.pop.es.mapper")
class EsTest(@Autowired val mapper: EsDemoMapper) {
    
    @Test
    fun createIndex() {
        mapper.createIndex()
    }
    
    @Test
    fun insertWithRefreshPolicy() {
        // 默认刷新策略（refresh=false）
        run {
            val id = UUID.randomUUID().toString()
            val demo = EsDemo().apply { this.id = id }
            mapper.insert(demo)
            val document = mapper.selectById(id)
            assertThat(document).isNull()
        }
        
        // 立即刷新（refresh=true）
        run {
            val id = UUID.randomUUID().toString()
            val demo = EsDemo().apply { this.id = id }
            mapper.insert(demo, Refresh.True)
            val document = mapper.selectById(id)
            assertThat(document).isNotNull()
        }
    }
    
    @Test
    fun testThaiSearch() {
        val id = UUID.randomUUID().toString()
        val demo = EsDemo().apply { 
            this.id = id
            this.productTitle = "เสื้อยืดฤดูร้อนชิคๆ"
        }
        mapper.insert(demo, Refresh.True)
    }
}