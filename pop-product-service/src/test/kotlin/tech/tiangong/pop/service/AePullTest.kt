package tech.tiangong.pop.service

import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import team.aikero.blade.auth.withSystemUser
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.PopApplication
import tech.tiangong.pop.component.ae.AeProductSyncComponent
import tech.tiangong.pop.config.AliexpressProperties
import tech.tiangong.pop.dao.repository.ShopRepository
import tech.tiangong.pop.enums.ProductAeAttributeTypeEnum
import tech.tiangong.pop.helper.cache.AliexpressServiceHelper
import test.BaseTest

@SpringBootTest(
    classes = [PopApplication::class],
    args = [
        "--spring.cloud.nacos.discovery.register-enabled=false",
        "--env=qa",
        "--app=ola",
        "--spring.profiles.active=\${env}-\${app}",
        "--spring.config.import=https://jv-devops.oss-cn-hangzhou.aliyuncs.com/\${env}/\${app}/middleware-conf.yaml"
    ]
)
@Disabled
@Slf4j
class AePullTest : BaseTest() {

    @Autowired
    lateinit var aeProductSyncComponent: AeProductSyncComponent

    @Autowired
    lateinit var shopRepository: ShopRepository

    @Autowired
    lateinit var aliexpressServiceHelper: AliexpressServiceHelper

    @Autowired
    lateinit var aliexpressProperties: AliexpressProperties


    @Test
    fun testQueryCategoryTreeList() {

        val pid = 1005009132525114
        val shopId = 7308304887945629727

        val shop = shopRepository.getById(shopId) ?: throw IllegalArgumentException("店铺不存在")

        withSystemUser {
            val resultData = aeProductSyncComponent.getAeProductDetail(
                pid,
                shop
            )

            val result = resultData.first
            val spu = resultData.second?.first
            val skuList = resultData.second?.second
            log.warn { "xxxxxxxxxx====result; ${result.toJson()} " }
            log.warn { "xxxxxxxxxx====spu: ${spu?.toJson()} " }
            log.warn { "xxxxxxxxxx====skuList: ${skuList?.toJson()} " }
        }
    }

    @Test
    fun testQueryProductInfo() {

        val attributeId = ProductAeAttributeTypeEnum.SHIPS_FROM.getPropertyId(aliexpressProperties)
        val categoryAttributes = aliexpressServiceHelper.getCachedCategoryAttributes(
            123L,
            201303101
        )
        val attributeValues = categoryAttributes.result
            ?.attributes
            ?.find {
                it.id == attributeId
            }
            ?.values
        // 转成Map
        val attributeValuesMap = attributeValues?.associate { it.id to it.names?.get("en") }
        log.warn { "xxxxxxxxxx====attributes: ${attributeValuesMap?.toJson()} " }
    }

}