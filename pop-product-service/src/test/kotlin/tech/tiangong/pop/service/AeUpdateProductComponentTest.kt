package tech.tiangong.pop.service

import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import team.aikero.blade.auth.withSystemUser
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.PopApplication
import tech.tiangong.pop.component.ae.AeUpdateProductComponent
import tech.tiangong.pop.dao.repository.AeSaleGoodsRepository

/**
 * AliExpress服务测试类
 */
@SpringBootTest(
    classes = [PopApplication::class],
    args = [
        "--spring.cloud.nacos.discovery.register-enabled=false",
        "--env=qa",
        "--app=ola",
        "--spring.profiles.active=\${env}-\${app}",
        "--spring.config.import=https://jv-devops.oss-cn-hangzhou.aliyuncs.com/\${env}/\${app}/middleware-conf.yaml"
    ]
)
@Disabled
@Slf4j
class AeCreateOrUpdateProductComponentTest {

    @Autowired
    lateinit var aeUpdateProductComponent: AeUpdateProductComponent

    @Autowired
    lateinit var aliexpressService: AliexpressService

    @Autowired
    lateinit var aeSaleGoodsRepository: AeSaleGoodsRepository

    /**
     * 测试用的AliExpress店铺ID
     */
    private var aeSaleGoodsId = 7312811741892972841

    @Test
    fun test() {
        withSystemUser {
            try {
                val aeSaleGoods = aeSaleGoodsRepository.getById(aeSaleGoodsId)
                aeUpdateProductComponent.createOrUpdateProduct(
                    aeSaleGoods.shopId!!,
                    aeSaleGoods
                )
            } catch (e: Exception) {
                log.error(e) { "Error occurred during test" }
            }
        }
    }

    @Test
    fun test2() {
        withSystemUser {
            val token = "50000001733j8gioatRfaGCRBrvwdgGOi0HS1FiWkSqbS14b1ad2buZwMOAfLAQd4udp"

            val saleGoodsId: Long = 7330883695611240492
            val pid: Long = 1005009235365772

            val before = aliexpressService.queryProduct(pid, token)
            log.info { "更新商品前: ${before.toJson()}" }


            aeUpdateProductComponent.testUpdate(saleGoodsId)

            val after = aliexpressService.queryProduct(pid, token)
            log.info { "更新商品后: ${after.toJson()}" }
        }
    }

    @Test
    fun test3() {
        withSystemUser {
            val token = "50000001733j8gioatRfaGCRBrvwdgGOi0HS1FiWkSqbS14b1ad2buZwMOAfLAQd4udp"

            val resp1 = aliexpressService.queryFreightTemplateList(token, "cn1100881170nhzae")
            log.info { "resp: ${resp1.toJson()}" }

            val templateId = 50146976475L
            val resp = aliexpressService.getFreightSettingByTemplateQuery(token, templateId)
            log.info { "resp: ${resp.toJson()}" }
            val allCountry = resp.freightSettingList?.flatMap { it -> it.selfStandardList?.map { it2 -> it2.selfStandardCountry } ?: emptyList() }?.distinct()
            println(allCountry)
        }
    }

}
