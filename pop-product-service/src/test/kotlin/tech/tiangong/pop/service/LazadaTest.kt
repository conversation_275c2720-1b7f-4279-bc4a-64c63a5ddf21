package tech.tiangong.pop.service

import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import team.aikero.blade.auth.withSystemUser
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.PopApplication
import tech.tiangong.pop.dao.entity.Product
import tech.tiangong.pop.dao.repository.ProductRepository
import tech.tiangong.pop.req.product.lazada.ProductLazadaPageQueryReq
import tech.tiangong.pop.service.product.ProductLazadaService
import test.BaseTest

@SpringBootTest(
    classes = [PopApplication::class],
    args = [
        "--spring.cloud.nacos.discovery.register-enabled=false",
        "--env=qa",
        "--app=ola",
        "--spring.profiles.active=\${env}-\${app}",
        "--spring.config.import=https://jv-devops.oss-cn-hangzhou.aliyuncs.com/\${env}/\${app}/middleware-conf.yaml"
    ]
)
@Disabled
@Slf4j
class LazadaTest : BaseTest() {

    @Autowired
    lateinit var productRepository: ProductRepository
    
    @Autowired
    lateinit var productLazadaService: ProductLazadaService

    @Test
    fun testAutoFill() {
        withSystemUser {
            val product = Product().apply {
                productTitle = "测试商品"
                // 故意不设置deleted
            }

            productRepository.save(product)

            val inserted = productRepository.getById(product.productId)
            log.info { "插入的商品: ${inserted.toJson()}" }
            assert(inserted.deleted == 0) { "deleted应自动填充为0，实际值为${inserted.deleted}" }
        }
    }
    
    @Test
    fun testESQueryStrategy() {
        withSystemUser {
            log.info { "测试ES查询策略优化" }
            
            // 测试单站点查询（应该使用详情索引）
            val singleCountryReq = ProductLazadaPageQueryReq().apply {
                country = "TH"
                pageNum = 1
                pageSize = 10
            }
            
            val singleCountryResult = productLazadaService.page(singleCountryReq)
            log.info { "单站点查询结果: 总数=${singleCountryResult.total}, country=${singleCountryReq.country}" }
            
            // 测试多站点查询（应该使用聚合索引）
            val multiCountryReq = ProductLazadaPageQueryReq().apply {
                // 不设置country参数
                pageNum = 1
                pageSize = 10
            }
            
            val multiCountryResult = productLazadaService.page(multiCountryReq)
            log.info { "多站点查询结果: 总数=${multiCountryResult.total}" }
            
            // 验证查询策略的日志输出
            log.info { "ES查询策略测试完成" }
        }
    }
}
