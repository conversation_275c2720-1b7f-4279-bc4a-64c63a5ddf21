package tech.tiangong.pop.service

import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import team.aikero.blade.auth.withUser
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.user.entity.CurrentUser
import team.aikero.blade.util.json.toJson
import team.aikero.pigeon.common.dto.WorkflowInstance
import tech.tiangong.pop.PopApplication
import tech.tiangong.pop.external.DingTalkWorkflowClientExternal
import tech.tiangong.pop.external.PigeonMessageClientExternal

@SpringBootTest(
    classes = [PopApplication::class],
    args = [
        "--spring.cloud.nacos.discovery.register-enabled=false",
        "--env=qa",
        "--app=ola",
        "--spring.profiles.active=\${env}-\${app}",
        "--spring.config.import=https://jv-devops.oss-cn-hangzhou.aliyuncs.com/\${env}/\${app}/middleware-conf.yaml"
    ]
)
@Disabled
@Slf4j
class PigeonMessageClientSingletonTest {

    @Autowired
    private lateinit var pigeonMessageClientExternal: PigeonMessageClientExternal

    @Autowired
    private lateinit var dingTalkWorkflowClientExternal: DingTalkWorkflowClientExternal

    @Test
    fun testSendPriceFloorNotify() {
        // 准备测试数据
        val receivers = listOf("800000232")
        val title = "价格兜底通知测试"
        val content = """
            ## 价格异常提醒
            
            商品 **A123456** 的价格设置不合理，请及时处理。
            
            - SKC: ABC123
            - 当前售价: ¥99.99
            - 允许范围: ¥120.00 - ¥150.00
        """.trimIndent()
        val messageId = "MSG-20250524-001"

        // 调用被测试方法
        val result = pigeonMessageClientExternal.sendPriceFloorNotify(
            receivers,
            title,
            content
        )

        // 验证结果
        assert(result == messageId)

    }

    @Test
    fun createTest() {

        val user = CurrentUser(
            tenantId = 1,
            id = 148231653,
            name = "张景恒",
            code = "TEST",
        )
        withUser(user) {
            // 测试环境
            val code = "PROC-A938D89B-BB12-448E-BDAA-801A86273ACB"
            val newInstance = WorkflowInstance(
                processDefinitionCode = code,
                startUserId = "148231653",
                approveUserIds = listOf("148231653", "148231653"),
                approveType = "AND",
                ccList = listOf("148231653"),
                variables = """
                [
                    {
                        "name": "SPU",
                        "value": "SPU00001"
                    },
                    {
                        "name": "SKC",
                        "value": "SKC00001"
                    },
                    {
                        "name": "品类",
                        "value": "品类1"
                    },
                    {
                        "name": "成本变动环节",
                        "value": "预估核价"
                    },
                    {
                        "name": "当前成本",
                        "value": 100
                    },
                    {
                        "name": "原成本",
                        "value": 80
                    },
                    {
                        "name": "当前定价成本",
                        "value": 100
                    },
                    {
                        "name": "原定价成本",
                        "value": 100
                    }
                ]
            """.trimIndent(),
            )
            val instanceId = dingTalkWorkflowClientExternal.create(newInstance)
            log.info { "===============instanceId: $instanceId" }

            val dto = dingTalkWorkflowClientExternal.status(instanceId)
            log.info { "===============dto: ${dto?.toJson()}" }
        }
    }
}