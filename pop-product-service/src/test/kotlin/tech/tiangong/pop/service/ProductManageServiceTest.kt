package tech.tiangong.pop.service

import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import team.aikero.blade.auth.withSystemUser
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.PopApplication
import tech.tiangong.pop.req.product.RefreshProductImagePackageStateReq
import tech.tiangong.pop.req.product.UpdateProductGrossMarginByShopReq
import tech.tiangong.pop.req.product.manage.ProductManagePageReq
import tech.tiangong.pop.service.product.ProductManageService
import java.math.BigDecimal

/**
 * 商品管理服务测试类
 */
@SpringBootTest(
    classes = [PopApplication::class],
    args = [
        "--spring.cloud.nacos.discovery.register-enabled=false",
        "--env=dev",
        "--app=ola",
        "--spring.profiles.active=\${env}-\${app}",
        "--spring.config.import=https://jv-devops.oss-cn-hangzhou.aliyuncs.com/\${env}/\${app}/middleware-conf.yaml"
    ]
)
@Disabled
@Slf4j
class ProductManageServiceTest {

    @Autowired
    lateinit var productManageService: ProductManageService

    @Test
    fun refreshImagePackageState() {
        withSystemUser {
            val req = RefreshProductImagePackageStateReq()
            req.productIds = listOf<Long>(7338153754204704773L)
            productManageService.refreshImagePackageState(req)
        }
    }

    @Test
    fun updateProductGrossMarginByShop() {
        withSystemUser {
            val req = UpdateProductGrossMarginByShopReq(mutableListOf(7354766801996608959),
                BigDecimal(15),mutableListOf(7308304887945629727))
            productManageService.updateProductGrossMarginByShop(req)
        }
    }

    @Test
    fun listSpuCodes(){
        val req = ProductManagePageReq()
//        req.hasModifyGrossMarginShopIds = mutableListOf(7325779076249538647)
        req.noModifyGrossMarginShopIds = mutableListOf(7325779076249538647)
        val result  = productManageService.listSpuCodes(req)
        log.info { "TTTTT===${result.toJson()}" }
    }
}
