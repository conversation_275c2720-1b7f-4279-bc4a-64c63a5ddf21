package tech.tiangong.pop.service

import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import team.aikero.blade.auth.withSystemUser
import team.aikero.blade.core.enums.Bool
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.PopApplication
import tech.tiangong.pop.enums.AttributeGroupTypeEnum
import tech.tiangong.pop.req.category.*
import tech.tiangong.pop.req.category.SavePublishAttributeReq.SavePublishAttributeValueReq
import tech.tiangong.pop.service.category.PublishCategoryAttrService
import tech.tiangong.pop.service.category.PublishGroupAttrService

/**
 */
@SpringBootTest(
    classes = [PopApplication::class],
    args = [
        "--spring.cloud.nacos.discovery.register-enabled=false",
        "--spring.profiles.active=dev-ola",
        "--spring.config.import=https://jv-devops.oss-cn-hangzhou.aliyuncs.com/\${spring.profiles.active}-yunwei-config.yml,classpath:local-bootstrap.yml"
    ]
)
@Disabled
@Slf4j
class PublishCategoryAttrServiceTest {

    @Autowired
    lateinit var publishCategoryAttrService: PublishCategoryAttrService
    @Autowired
    lateinit var publishGroupAttrService:PublishGroupAttrService
    /**
     * 关联品类和属性
     */
    @Test
    fun relateCategoryAttr() {
        withSystemUser {
            try {
                var req = RelatePublishCategoryAttrReq()
                req.publishCategoryId = 7270576779235684433
                var publishAttrIds: MutableList<Long> = mutableListOf()
                publishAttrIds.add(7355782715131073381)
                req.publishAttrIds = publishAttrIds
                publishCategoryAttrService.relateCategoryAttr(req)
            } catch (e: Exception) {
                throw e
            }
        }
    }

    @Test
    fun queryPublishCategoryAttr() {
        withSystemUser {
            try {
                var req = PublishCategoryAttrQueryReq()
                req.categoryId = 7270576779235684433
                val result = publishCategoryAttrService.queryPublishCategoryAttr(req)
                log.info { "result=========${result.toJson()}" }
            } catch (e: Exception) {
                throw e
            }
        }
    }

    @Test
    fun queryCategoryAttrByPage() {
        withSystemUser {
            try {
                var req = PublishCategoryAttrPageQueryReq()
                req.categoryId = 7270576779235684433
                val result = publishCategoryAttrService.queryCategoryAttrByPage(req)
                log.info { "result=========${result.toJson()}" }
            } catch (e: Exception) {
                throw e
            }
        }
    }

    @Test
    fun pageAttribute() {
        var req = PublishAttributePageQueryReq()
        req.groupId = 7355776911875281764
        var result = publishGroupAttrService.pageAttribute(req)
        log.info { "result=========${result.toJson()}" }
    }

    @Test
    fun modifyAttributeGroupState(){
        var req = ModifyAttributeGroupStateReq();
        req.groupIds = mutableListOf<Long>(7355776911875281764)
        req.state = Bool.NO.code
        publishGroupAttrService.modifyAttributeGroupState(req)
    }

    @Test
    fun modifyAttributeState(){
        var req = ModifyAttributeStateReq()
        req.attributeIds = mutableListOf<Long>(7355782715131073381)
        req.state = Bool.NO.code
        publishGroupAttrService.modifyAttributeState(req)
    }

    @Test
    fun saveAttribute(){
        withSystemUser {
            var req = SavePublishAttributeReq()
            req.attributeGroupId = 7355776911875281764
            req.attributeId = 7356137940308258817
            req.attributeName = "D2"
            var values = mutableListOf<SavePublishAttributeValueReq>()
            values.add(SavePublishAttributeValueReq().apply {
                this.value = "D2-111"
            })
//            values.add(SavePublishAttributeValueReq().apply {
//                this.value = "D2-222"
//            })
            req.values = values
            publishGroupAttrService.saveAttribute(req)
        }
    }

    @Test
    fun saveGroup(){
        withSystemUser {
            var req = SavePublishAttributeGroupReq()
            req.groupType = AttributeGroupTypeEnum.CATEGORY.code
            req.groupName = "AA"
            publishGroupAttrService.saveGroup(req)
        }
    }
}
