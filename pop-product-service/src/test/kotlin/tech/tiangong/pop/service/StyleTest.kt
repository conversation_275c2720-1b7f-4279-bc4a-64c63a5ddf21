package tech.tiangong.pop.service

import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import team.aikero.blade.auth.withSystemUser
import team.aikero.blade.core.exception.BusinessException
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.PopApplication
import tech.tiangong.pop.dao.entity.Product
import tech.tiangong.pop.dao.repository.ProductRepository
import tech.tiangong.pop.external.SdpSpuClient
import tech.tiangong.sdp.design.vo.req.style.PopCreateInfoReq

@SpringBootTest(
    classes = [PopApplication::class],
    args = [
        "--spring.cloud.nacos.discovery.register-enabled=false",
        "--env=qa",
        "--app=ola",
        "--spring.profiles.active=\${env}-\${app}",
        "--spring.config.import=https://jv-devops.oss-cn-hangzhou.aliyuncs.com/\${env}/\${app}/middleware-conf.yaml"
    ]
)
@Disabled
@Slf4j
class StyleTest {
    @Autowired
    private lateinit var productRepository: ProductRepository

    @Autowired
    lateinit var sdpSpuClient: SdpSpuClient

    @Test
    fun testBuildCategoryTree() {
        val spuCodeList = listOf(
            "USMTS01Z-aA2742-1",
            "240100424101241",
            "240100424101242",
            "PG2504240025",
            "PG2504270008",
            "PG2504240030",
            "PG2504240018",
            "240100424101241234",
            "PG2504280004",
            "250508001101"
        )

        withSystemUser {
            // 获取上游商品信息
            val resp = sdpSpuClient.queryStyleInfo4Pop(PopCreateInfoReq().apply {
                this.styleCodeList = spuCodeList
            })
            log.info { "resp: ${resp.toJson()}" }

            if (!resp.isSuccessful) {
                throw BusinessException("拉取上游商品信息异常")
            }

            // 遍历上游数据
            val updateProductList = mutableListOf<Product>()
            resp.data.forEach { productData ->
                // spu获取product表(可能多个)
                val productList = productRepository.getListBySpuCode(productData.spuCode)
                if (productList.isEmpty()) {
                    return@forEach
                }

                // 遍历product表数据
                productList.forEach { product ->
                    // 比较product表字段, 不一致的则更新
                    val updateProduct = Product().apply {
                        this.productId = product.productId
                    }

                    var isUpdate = false
                    checkUpdate(product.supplyMode, productData.supplyMode) { new -> updateProduct.supplyMode = new }?.let { isUpdate = true }
                    checkUpdate(product.waves, productData.waves) { new -> updateProduct.waves = new }?.let { isUpdate = true }
                    checkUpdate(product.spotTypeCode, productData.spotTypeCode) { new -> updateProduct.spotTypeCode = new }?.let { isUpdate = true }
                    checkUpdate(product.goodsRepType, productData.goodsRepType) { new -> updateProduct.goodsRepType = new }?.let { isUpdate = true }
                    checkUpdate(product.goodsType, productData.goodsType) { new -> updateProduct.goodsType = new }?.let { isUpdate = true }

                    if (isUpdate) {
                        updateProductList.add(updateProduct)
                    }
                }
            }
            // 更新product
            if (updateProductList.isNotEmpty()) {
                productRepository.updateBatchById(updateProductList)
            }
        }
    }

    /**
     * 公共方法, 封装product字段比较逻辑
     * @param old 旧值
     * @param new 新值
     * @param updateAction 更新逻辑
     * @return true有更新, null无更新
     */
    fun checkUpdate(old: String?, new: String?, updateAction: (new: String?) -> Unit): Boolean? {
        if (new != null && old != new) {
            updateAction(new)
            return true
        }
        return null
    }
}