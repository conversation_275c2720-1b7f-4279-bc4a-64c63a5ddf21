package tech.tiangong.pop.service

import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import team.aikero.blade.auth.withSystemUser
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.PopApplication
import tech.tiangong.pop.common.enums.ChannelEnum
import tech.tiangong.pop.common.enums.PlatformEnum
import tech.tiangong.pop.dao.repository.PublishCategoryMappingRepository
import tech.tiangong.pop.req.product.temu.TemuBaseReq

@SpringBootTest(
    classes = [PopApplication::class],
    args = [
        "--spring.cloud.nacos.discovery.register-enabled=false",
        "--env=qa",
        "--app=ola",
        "--spring.profiles.active=\${env}-\${app}",
        "--spring.config.import=https://jv-devops.oss-cn-hangzhou.aliyuncs.com/\${env}/\${app}/middleware-conf.yaml"
    ]
)
@Disabled
@Slf4j
class TemuTest {
    @Autowired
    private lateinit var publishCategoryMappingRepository: PublishCategoryMappingRepository

    @Autowired
    lateinit var temuService: TemuService

    @Test
    fun testBuildCategoryTree() {
        temuService.updateTemuCategoryTreeCache()
    }

    @Test
    fun testBuildCategoryPath() {
        withSystemUser {
            temuService.updateTemuCategoryPathList()
        }
    }

    @Test
    fun testFetch() {
        withSystemUser {
            temuService.syncTemuCategory(TemuBaseReq("634418223015936", "CN"))
        }
    }

    @Test
    fun testAttr() {

        val platformEnum = PlatformEnum.TEMU
        val channelEnum = ChannelEnum.OTHER

        // 系统品类id
        val categoryId = 7303653328587837907

        val platformCategoryId = publishCategoryMappingRepository.getByPublishCategoryId(categoryId, platformEnum.platformId, channelEnum.channelId)?.platformCategoryId
        val platformAttr = temuService.getAttrsByCategoryWithCache(platformCategoryId!!.toInt())
        log.info { "获取平台属性结果: ${platformAttr?.toJson()}" }



    }
}