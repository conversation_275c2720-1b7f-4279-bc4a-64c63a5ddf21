package tech.tiangong.pop.service

import org.babyfish.jimmer.sql.kt.KSqlClient
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import team.aikero.blade.auth.withSystemUser
import team.aikero.blade.core.toolkit.isNotBlank
import team.aikero.blade.core.toolkit.isNotEmpty
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.PopApplication
import tech.tiangong.pop.dao.entity.AeCategoryAttrGroupTmp
import tech.tiangong.pop.dao.entity.AeCategoryAttrValueTmp
import tech.tiangong.pop.dao.entity.AeCategoryTmp
import tech.tiangong.pop.dao.repository.AeCategoryAttrGroupTmpRepository
import tech.tiangong.pop.dao.repository.ShopRepository

/**
 * AE 速卖通AliExpress服务测试类
 */
@SpringBootTest(
    classes = [PopApplication::class],
    args = [
        "--spring.cloud.nacos.discovery.register-enabled=false",
        "--env=dev",
        "--app=ola",
        "--spring.profiles.active=\${env}-\${app}",
        "--spring.config.import=https://jv-devops.oss-cn-hangzhou.aliyuncs.com/\${env}/\${app}/middleware-conf.yaml"
    ]
)
@Disabled
@Slf4j
class ThirdCategoryAttrTest {

    @Autowired
    lateinit var aliexpressService: AliexpressService

    @Autowired
    lateinit var shopRepository: ShopRepository

    @Autowired
    lateinit var sqlClient: KSqlClient

    @Autowired
    lateinit var aeCategoryAttrGroupTmpRepository: AeCategoryAttrGroupTmpRepository

    @Test
    fun prepareTestImage() {
        withSystemUser {

            val token = shopRepository.getById(7308304887945629727L).token!!

//            // 查询AE品类
//            val saveList = saveCategoriesRecursively(0L, token!!)
//            if (saveList.isEmpty()) {
//                return@withSystemUser
//            }
//
//            log.info { "saveList size: ${saveList.size}, saveList: ${saveList.toJson()}" }
//            sqlClient.saveEntities(saveList)

            // 查询AE属性
            val categoryList = sqlClient
                .createQuery(AeCategoryTmp::class) {
                    select(table)
                }
                .execute()
            val saveAttrList = categoryList.map {
                // 休眠1.5s
                saveCategoriesSAttrRecursively(it.id, null, token)
            }

            saveAttrList.forEach {
                val groupList = it.first
                val valueList = it.second
                if (groupList.isNotEmpty()) {
                    sqlClient.saveEntities(groupList)
                }
                if (valueList.isNotEmpty()) {
                    sqlClient.saveEntities(valueList)
                }
            }
        }
    }

    // 递归保存分类数据的辅助函数
    private fun saveCategoriesRecursively(parentId: Long, token: String): List<AeCategoryTmp> {
        val categoryTree = aliexpressService.queryCategoryTreeList(
            accessToken = token,
            null,
            onlyWithPermission = false,
            channel = "AE_GLOBAL",
            categoryId = parentId
        )
        if (!categoryTree.isSuccess() || categoryTree.aeopPostCategoryList.isNullOrEmpty()) {
            return emptyList()
        }
        return categoryTree.aeopPostCategoryList
            .filter { category ->
                if (parentId == 0L) {
                    category.id == 200000345L // 女装品类
                } else {
                    true
                }
            }
            .map { category ->
                AeCategoryTmp {
                    this.id = category.id
                    this.level = category.level
                    this.leaf = if (category.isleaf) 1 else 0
                    this.zhName = category.names["zh"]
                    this.names = category.names.toJson()
                    this.features = category.features?.toJson()
                    // 递归处理子分类
                    if (!category.isleaf) {
                        this.childNodes = saveCategoriesRecursively(category.id, token)
                    }
                }
            }
    }

    // 递归保存分类数据的辅助函数
    private fun saveCategoriesSAttrRecursively(categoryId: Long, attrValueId: String?, token: String): Pair<List<AeCategoryAttrGroupTmp>, List<AeCategoryAttrValueTmp>> {
        log.info {"categoryId: $categoryId, attrValueId: $attrValueId 休眠1.5s"}
        Thread.sleep(1500)
        val result = aliexpressService.queryCategoryAttributes(
            accessToken = token,
            categoryId = categoryId,
            param2 = attrValueId,
        )
        if (!result.isSuccess() || result.result == null || result.result.attributes.isNullOrEmpty()) {
            return Pair(emptyList(), emptyList())
        }
        val list = mutableListOf<AeCategoryAttrGroupTmp>()
        val valueList = mutableListOf<AeCategoryAttrValueTmp>()
        result.result.attributes
            .forEach { attr ->
                val group = AeCategoryAttrGroupTmp {
                    this.attrGroupId = attr.id!!
                    this.categoryId = categoryId
                    this.parentAttrValue = if (attrValueId.isNotBlank()) attrValueId else null
                    this.inputType = attr.inputType
                    this.keyAttribute = if (attr.keyAttribute == true) 1 else 0
                    this.zhName = attr.names?.get("zh")
                    this.names = attr.names?.toJson()
                    this.required = if (attr.required == true) 1 else 0
                    this.sku = if (attr.sku == true) 1 else 0
                    this.skuStyleValue = attr.skuStyleValue
                    this.spec = attr.spec
                    this.units = attr.units?.toJson()
                    this.visible = if (attr.visible == true) 1 else 0
                    this.features = attr.features?.toJson()
                    this.supportEnumInput = if (attr.supportEnumInput == true) 1 else 0
                    this.attributeShowTypeValue = attr.attributeShowTypeValue
                    this.customizedName = if (attr.customizedName == true) 1 else 0
                    this.customizedPic = if (attr.customizedPic == true) 1 else 0
                }
                if (attr.values.isNotEmpty()) {
                    attr.values?.forEach { value ->
                        val tmp = AeCategoryAttrValueTmp {
                            this.attrValueId = value.id!!
                            this.attrGroupId = group.attrGroupId
                            this.categoryId = group.categoryId
                            this.attrValue = "${attr.id}=${value.id}"
                            this.zhName = value.names?.get("zh")
                            this.names = value.names?.toJson()
                            this.valueTags = value.valueTags?.toJson()
                            this.hasSubAttr = if (value.hasSubAttr == true) 1 else 0
                            this.attributes = value.attributes?.toJson()
                        }
                        valueList.add(tmp)

                        if (value.hasSubAttr == true) {
                            val children = saveCategoriesSAttrRecursively(categoryId, "${attr.id}=${value.id}", token)
                            val first = children.first
                            val second = children.second
                            list.addAll(first)
                            valueList.addAll(second)
                        }
                        tmp
                    }
                }
                list.add(group)
            }
        return Pair(list, valueList)
    }
}
