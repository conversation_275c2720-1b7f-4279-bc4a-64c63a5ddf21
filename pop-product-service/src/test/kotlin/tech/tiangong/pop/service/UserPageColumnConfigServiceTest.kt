package tech.tiangong.pop.service

import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import team.aikero.blade.auth.withSystemUser
import team.aikero.blade.core.enums.Bool
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.PopApplication
import tech.tiangong.pop.enums.PageCodeEnum
import tech.tiangong.pop.enums.PageColumnEnum
import tech.tiangong.pop.req.product.SaveShowPageColumnReq
import tech.tiangong.pop.req.product.SaveShowPageColumnReq.ColumnConfigReq

/**
 * AE 速卖通AliExpress服务测试类
 */
@SpringBootTest(
    classes = [PopApplication::class],
    args = [
        "--spring.cloud.nacos.discovery.register-enabled=false",
        "--env=dev",
        "--app=ola",
        "--spring.profiles.active=\${env}-\${app}",
        "--spring.config.import=https://jv-devops.oss-cn-hangzhou.aliyuncs.com/\${env}/\${app}/middleware-conf.yaml"
    ]
)
@Disabled
@Slf4j
class UserPageColumnConfigServiceTest {

    @Autowired
    lateinit var userPageColumnConfigService: UserPageColumnConfigService

    @Test
    fun saveShowPageColumnConfig() {
        withSystemUser {
            var req = SaveShowPageColumnReq(PageCodeEnum.productPage.pageCode)
            var showColumnConfigReqList: MutableList<ColumnConfigReq> = mutableListOf()
            showColumnConfigReqList.add(ColumnConfigReq(PageColumnEnum.mainImgUrl.columnCode, Bool.YES.code))
            showColumnConfigReqList.add(ColumnConfigReq(PageColumnEnum.spuCode.columnCode, Bool.YES.code))
            showColumnConfigReqList.add(ColumnConfigReq(PageColumnEnum.supplyMode.columnCode, Bool.YES.code))
            showColumnConfigReqList.add(ColumnConfigReq(PageColumnEnum.waves.columnCode, Bool.YES.code))
            req.showColumnConfigReqList = showColumnConfigReqList
            userPageColumnConfigService.saveShowPageColumnConfig(req)
        }
    }

    @Test
    fun getCurrentUserPageColumnByPageCode() {
        withSystemUser {
            var result =
                userPageColumnConfigService.getCurrentUserPageColumnByPageCode(PageCodeEnum.productPage.pageCode)
            log.info { "result====: ${result.toJson()}" }
        }
    }

}
