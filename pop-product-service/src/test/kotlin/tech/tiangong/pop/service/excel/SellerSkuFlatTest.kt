package tech.tiangong.pop.service.excel

import com.alibaba.excel.EasyExcel
import com.alibaba.excel.annotation.ExcelProperty
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import team.aikero.blade.auth.withUser
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.sequence.id.IdHelper
import team.aikero.blade.user.entity.CurrentUser
import tech.tiangong.pop.PopApplication
import tech.tiangong.pop.common.enums.PlatformEnum
import tech.tiangong.pop.dao.entity.BarcodeUnitSnapshot
import tech.tiangong.pop.dao.entity.SellerSkuBarcodeRef
import tech.tiangong.pop.dao.entity.SellerSkuFlatInfo
import tech.tiangong.pop.dao.repository.BarcodeUnitSnapshotRepository
import tech.tiangong.pop.dao.repository.SellerSkuBarcodeRefRepository
import tech.tiangong.pop.dao.repository.SellerSkuFlatInfoRepository
import java.io.File

@SpringBootTest(
    classes = [PopApplication::class],
    args = [
        "--spring.cloud.nacos.discovery.register-enabled=false",
        "--env=dev",
        "--app=ola",
        "--spring.profiles.active=\${env}-\${app}",
        "--spring.config.import=https://jv-devops.oss-cn-hangzhou.aliyuncs.com/\${env}/\${app}/middleware-conf.yaml"
    ]
)
@Disabled
@Slf4j
class SellerSkuFlatTest {
    @Autowired
    lateinit var sellerSkuFlatInfoRepository: SellerSkuFlatInfoRepository

    @Autowired
    lateinit var sellerSkuBarcodeRefRepository: SellerSkuBarcodeRefRepository

    @Autowired
    lateinit var barcodeUnitSnapshotRepository: BarcodeUnitSnapshotRepository

    val user = CurrentUser(
        id = 0,
        name = "2025年4月26日-技术-导入AE条码",
        code = "",
        tenantId = 0,
        superAdmin = false
    )

    @Test
//    @Transactional(rollbackFor = [Exception::class])
    fun import() {
        val excelFile = File("C:\\Users\\<USER>\\Desktop\\AE商品对应的seller_sku.xlsx")
        if (excelFile.exists()) {
            val listener = ExcelDataTestListener<ExcelData>()
            EasyExcel.read(excelFile, ExcelData::class.java, listener).sheet().doRead()
            val dataList = listener.getDataList()
            service(dataList)
        } else {
            error { "指定的 Excel 文件不存在" }
        }
    }

    fun service(dataList: List<ExcelData>) {
        if (dataList.isEmpty()) {
            return
        }
        // 设置用户
        withUser(user) {

            log.info { "开始处理数据" }
            val saveFlatList = mutableListOf<SellerSkuFlatInfo>()
            val saveBarcodeRefList = mutableListOf<SellerSkuBarcodeRef>()
            val saveBarcodeUnit = mutableListOf<BarcodeUnitSnapshot>()
            // 循环逻辑
            dataList
                .associateBy { it.sellerSku!! }
                .values
                .forEach { dto ->
                    val flat = SellerSkuFlatInfo().apply {
                        this.sellerSkuFlatId = IdHelper.getId()
                        this.image = ""
                        this.color = dto.skuColorSpec?.split(";")?.first()?.trim()
                        this.colorCode = dto.colorSpec?.split(";")?.first()?.trim()
                        this.sellerSku = dto.sellerSku?.trim()
                        this.spuCode = dto.sellerSku?.split("-")?.first()?.trim()
                        this.platformId = PlatformEnum.AE.platformId // 写死平台
                        this.shopId = 7276480307564389700 // 写死店铺
                    }
                    saveFlatList.add(flat)

                    // 处理barcode
                    val barcodeRef = SellerSkuBarcodeRef().apply {
                        this.barcodeRefId = IdHelper.getId()
                        this.sellerSkuFlatId = flat.sellerSkuFlatId
                        this.barcode = dto.barcode?.trim()
                    }
                    saveBarcodeRefList.add(barcodeRef)

                    // 处理unit
                    val unit = BarcodeUnitSnapshot().apply {
                        this.snapshotId = IdHelper.getId()
                        this.barcodeRefId = barcodeRef.barcodeRefId
                        this.unit = 1
                    }
                    saveBarcodeUnit.add(unit)
                }

            // 批量插入, 500条一批\
            log.info { "开始批量插入数据" }
            val batchSize = 500
            saveFlatList.chunked(batchSize).forEach {
                sellerSkuFlatInfoRepository.saveBatch(it)
            }
            saveBarcodeRefList.chunked(batchSize).forEach {
                sellerSkuBarcodeRefRepository.saveBatch(it)
            }
            saveBarcodeUnit.chunked(batchSize).forEach {
                barcodeUnitSnapshotRepository.saveBatch(it)
            }
            log.info { "结束批量插入数据" }
        }
    }

    // 定义一个数据类来映射 Excel 中的数据
    data class ExcelData(
        @ExcelProperty("店铺名称")
        var shopName: String? = null,
        @ExcelProperty("线上商品名称")
        var productName: String? = null,
        @ExcelProperty("线上颜色规格") // Blue;S
        var colorSpec: String? = null,
        @ExcelProperty("平台店铺款式编码") // PID
        var platformProductId: String? = null,
        @ExcelProperty("平台店铺商品编码") // SKU ID
        var platformSkuId: String? = null,
        @ExcelProperty("线上商品编码")
        var sellerSku: String? = null,
        @ExcelProperty("对应商品编码")
        var barcode: String? = null,
        @ExcelProperty("对应商品颜色规格") // 浅蓝色;155/64A(S)
        var skuColorSpec: String? = null,
    )
}
